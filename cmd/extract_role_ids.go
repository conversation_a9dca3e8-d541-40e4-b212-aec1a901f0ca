package cmd

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
)

func init() {
	root.AddCommand(extractRoleIds)
}

var extractRoleIds = &cobra.Command{
	Use:   "extract_role_ids",
	Short: "Extract role IDs from Excel file to text file",
	Long:  "Extract role IDs from Excel file and save them to a text file",
	Run: func(cmd *cobra.Command, args []string) {
		if len(args) < 1 {
			fmt.Println("Please provide input Excel file path")
			return
		}

		inputPath := args[0]
		ext := strings.ToLower(filepath.Ext(inputPath))
		if ext != ".xlsx" {
			fmt.Println("Input file must be an Excel file (.xlsx)")
			return
		}

		// 打开Excel文件
		f, err := excelize.OpenFile(inputPath)
		if err != nil {
			fmt.Printf("Error opening excel file: %v\n", err)
			return
		}
		defer f.Close()

		// 获取第一个工作表
		sheets := f.GetSheetList()
		if len(sheets) == 0 {
			fmt.Println("No sheets found in excel file")
			return
		}

		// 读取所有行
		rows, err := f.GetRows(sheets[0])
		if err != nil {
			fmt.Printf("Error reading rows: %v\n", err)
			return
		}

		if len(rows) < 2 {
			fmt.Println("Excel file must contain at least header row and one data row")
			return
		}

		// 创建输出文件
		outputPath := strings.TrimSuffix(inputPath, ext) + "_role_ids.txt"
		outputFile, err := os.Create(outputPath)
		if err != nil {
			fmt.Printf("Error creating output file: %v\n", err)
			return
		}
		defer outputFile.Close()

		// 提取并写入roleId（跳过标题行）
		for i, row := range rows {
			if i == 0 { // 跳过标题行
				continue
			}
			if len(row) > 1 {
				roleId := strings.TrimSpace(row[1])
				if roleId != "" {
					_, err := outputFile.WriteString(roleId + "\n")
					if err != nil {
						fmt.Printf("Error writing to file: %v\n", err)
						return
					}
				}
			}
		}

		fmt.Printf("Successfully extracted role IDs to: %s\n", outputPath)
	},
}
