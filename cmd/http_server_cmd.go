//go:build !windows

package cmd

import (
	"fmt"
	"net/http"
	"time"

	"app/config"
	"app/router"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"

	_ "net/http/pprof"

	"github.com/facebookgo/grace/gracehttp"
	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(server)
}

var server = &cobra.Command{
	Use:   "server",
	Short: "Start HTTP Server",
	Long:  "Start HTTP Server",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		serverStart()
	},
}

func serverStart() {
	httpServer := router.InitRouter(router.RegisterRoute)

	if config.C.Test.TestEnv {
		go func() {
			http.ListenAndServe("0.0.0.0:6060", nil)
		}()
	}

	fmt.Println("start "+config.C.App.AppName+" listen ", config.C.App.HttpListen)
	err := gracehttp.Serve(
		&http.Server{Addr: config.C.App.HttpListen, Handler: httpServer, IdleTimeout: 62 * time.Second},
	)
	if err != nil {
		elog.Error(err.Error())
	}
}
