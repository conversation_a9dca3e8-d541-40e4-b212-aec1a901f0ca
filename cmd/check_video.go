package cmd

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/service"
	"app/util"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(checkVideos)
	checkVideos.Flags().StringP("file", "f", "not_found_videos.csv", "指定输出文件路径")
	checkVideos.Flags().BoolP("fix", "i", false, "执行修复")
}

var checkVideos = &cobra.Command{
	Use:   "check-video",
	Short: "Scan work video for audit status",
	Long:  "Scans all works in l36_action_work, checks if the video audit status is failed, and outputs the results to not_found_videos.csv.",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		// 获取输出文件路径
		csvFileName, _ := cmd.Flags().GetString("file")
		fix, _ := cmd.Flags().GetBool("fix")
		ctx := context.Background()

		log.Println("Starting cover scan...")
		startTime := time.Now()

		workRepo := repo.NewWorkRepo()
		csvFile, err := os.Create(csvFileName)
		if err != nil {
			log.Fatalf("Failed to create CSV file %s: %v", csvFileName, err)
		}
		defer csvFile.Close()

		csvWriter := csv.NewWriter(csvFile)
		defer csvWriter.Flush()

		// Write CSV header
		if err := csvWriter.Write([]string{"id", "video_url", "audit_status", "reason"}); err != nil {
			log.Fatalf("Failed to write CSV header: %v", err)
		}

		const batchSize = 1000
		var lastID int64 = 0
		httpClient := &http.Client{Timeout: 10 * time.Second}

		processedCount := 0
		auditPassCount := 0

		for {
			var works []model.L36ActionWork
			err := workRepo.NormalScope(context.Background()).
				Where("id > ? AND type = ? and video != ''", lastID, 2). // 只查询 type=2 的视频作品
				Order("id asc").
				Limit(batchSize).
				Find(&works).Error
			if err != nil {
				log.Printf("Error fetching works batch (lastID: %d): %v", lastID, err)
				break
			}

			if len(works) == 0 {
				log.Println("No more works to process.")
				break
			}

			log.Printf("Processing batch of %d works starting from ID > %d...", len(works), lastID)

			var wg sync.WaitGroup
			semaphore := make(chan struct{}, 20) // 限制最大并发数为20

			for _, work := range works {
				wg.Add(1)
				semaphore <- struct{}{} // 获取信号量

				go func(w model.L36ActionWork) {
					defer wg.Done()
					defer func() { <-semaphore }() // 释放信号量

					isFailed := false

					req, err := http.NewRequest("HEAD", w.Video, nil)
					if err != nil {
						log.Printf("Error creating request for work %d (%s): %v", w.ID, w.Cover, err)
						return
					}
					req.Header.Set("User-Agent", "cover-check-script/1.0")

					resp, err := httpClient.Do(req)
					if err != nil {
						log.Printf("Error checking cover for work %d (%s): %v", w.ID, w.Cover, err)
						return
					}
					resp.Body.Close()

					isFailed = resp.StatusCode == http.StatusNotFound
					reason := "404"
					if !isFailed {
						reviewStatus, err := service.NewFPService().GetFpFileReviewStatus(ctx, w.Video)
						if err != nil {
							log.Printf("Error getting review status for work %d (%s): %v", w.ID, w.Video, err)
							return
						}
						isFailed = reviewStatus == constant.FpReviewStatusReject
						reason = "review_status"
					}

					if isFailed {
						if err := csvWriter.Write([]string{
							strconv.FormatInt(w.ID, 10),
							w.Video,
							strconv.FormatInt(int64(w.AuditStatus), 10),
							reason,
						}); err != nil {
							log.Printf("Error writing record to CSV: %v", err)
						}
						auditPassCount++

						if fix {
							var auditInfo service.AuditInfo
							err := json.Unmarshal([]byte(w.AuditInfo), &auditInfo)
							if err != nil {
								log.Printf("Error unmarshalling audit info for work %d: %v", w.ID, err)
								return
							}
							auditInfo.Video = int(constant.FpReviewStatusReject)
							err = workRepo.UpdateById(ctx, w.ID, map[string]interface{}{
								"audit_status": int(constant.FpReviewStatusReject),
								"audit_info":   util.StructToJson(auditInfo),
							})
							if err != nil {
								log.Printf("Error updating work %d: %v", w.ID, err)
							}

							_, err = cache.GetWorkCache().Update(ctx, w.ID, map[string]interface{}{
								"audit_status": int(constant.FpReviewStatusReject),
								"audit_info":   util.StructToJson(auditInfo),
							})
							if err != nil {
								log.Printf("Error updating work %d: %v", w.ID, err)
							}

							workEsRepo := es.NewWorkEsRepo()
							err = workEsRepo.Update(ctx, int(w.ID), map[string]any{
								"audit_status": int(constant.FpReviewStatusReject),
							})
							if err != nil {
								log.Printf("Error updating work %d: %v", w.ID, err)
							}
						}
					}
					processedCount++
					if processedCount%100 == 0 {
						log.Printf("Processed %d works so far...", processedCount)
					}
				}(work)
			}

			wg.Wait() // 等待所有 goroutine 完成
			lastID = works[len(works)-1].ID
			csvWriter.Flush()
		}

		duration := time.Since(startTime)
		log.Printf("Cover scan finished in %s.", duration)
		log.Printf("Checked %d works.", processedCount)
		log.Printf("Found %d video with failed audit.", auditPassCount)
		log.Printf("Results saved to %s", csvFileName)

		fmt.Println("Video check complete. See logs and not_found_video.csv.")
	},
}
