package cmd

import (
	"context"
	"fmt"
	"sync"
	"time"

	"app/config"
	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/service"
	"app/util"
	"app/util/wait"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/bytedance/sonic"
	cron2 "github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

func init() {
	root.AddCommand(cron)
}

var (
	c        *cron2.Cron
	cronOnce sync.Once
)

var cron = &cobra.Command{
	Use:   "cron",
	Short: "Start Cron Server",
	Long:  "Start Cron Server",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		go serverStart()

		initCron(cmd.Context())
		err := CronRun(cmd, args)
		if err != nil {
			fmt.Println(err)
		}
	},
}

func initCron(ctx context.Context) {
	// 每月1号删除两个月以前的有效浏览记录，并创建下个月的有效浏览记录表
	_, err := AddCronJob("0 0 10 1 * *", cron2.FuncJob(func() {
		ctx := context.Background()
		now := time.Now()
		month := now.Month()
		subRepo := repo.NewWorkPlaySubRepo()

		// 删除两个月前的表
		twoMonthAgoMonth := int(month) - 2
		twoMonthAgoYear := now.Year()
		if twoMonthAgoMonth < 1 {
			twoMonthAgoMonth += 12
			twoMonthAgoYear--
		}
		dropDate := int64(twoMonthAgoYear*10000 + twoMonthAgoMonth*100 + 1)
		if err := subRepo.DropSubTable(ctx, model.L36ActionWorkPlay{
			Date: dropDate,
		}); err != nil {
			elog.WithContext(ctx).WithError(err).Error("subRepo.DropSubTable failed")
		} else {
			elog.WithContext(ctx).WithFields(map[string]interface{}{
				"date": dropDate,
			}).Info("drop sub table success")
		}

		// 创建下个月的表
		nextMonth := int(month) + 1
		year := now.Year()
		if nextMonth > 12 {
			nextMonth -= 12
			year++
		}
		createDate := int64(year*10000 + nextMonth*100 + 1)
		if err := subRepo.CreateSubTable(ctx, model.L36ActionWorkPlay{
			Date: createDate,
		}); err != nil {
			elog.WithContext(ctx).WithError(err).Error("subRepo.CreateSubTable failed")
		} else {
			elog.WithContext(ctx).WithFields(map[string]interface{}{
				"date": createDate,
			}).Info("create sub table success")
		}
	}))
	if err != nil {
		panic(err)
	}

	// 每30分钟计算一次周热度
	//_, err = AddCronJob("0 */30 * * * *", cron2.FuncJob(func() {
	//	ctx := cmd.Context()
	//	RefreshWeekHot(ctx)
	//}))
	//if err != nil {
	//	panic(err)
	//}

	// 每分钟检查一下审核状态
	_, err = AddCronJob("0 */1 * * * *", cron2.FuncJob(func() {
		CheckReviewStatus(ctx)
	}))
	if err != nil {
		panic(err)
	}

	// 每两个小时衰减一次作品热度
	_, err = AddCronJob("0 0 */2 * * *", cron2.FuncJob(func() {
		DecayWorkHot(ctx)
	}))
	if err != nil {
		panic(err)
	}

	// Add designer exp rank cache refresh job - every 5 minutes
	_, err = AddCronJob("0 */5 * * * *", cron2.FuncJob(func() {
		cache.NewDesignerExpRankCache().Refresh(ctx, 0)
	}))
	if err != nil {
		panic(err)
	}

	// Add work hot rank cache refresh job - every 5 minutes
	_, err = AddCronJob("0 */5 * * * *", cron2.FuncJob(func() {
		// Refresh for each work type
		for workType := 1; workType <= 2; workType++ {
			cache.NewWorkHotRankCache().Refresh(ctx, workType)
		}
	}))
	if err != nil {
		panic(err)
	}

	// 每分钟检查一次作品删除表，如果存在则删除作品收藏数据
	_, err = AddCronJob("0 */1 * * * *", cron2.FuncJob(func() {
		CheckDeleteWorks()
	}))
	if err != nil {
		panic(err)
	}

	// 每分钟刷新一次备份推荐列表
	_, err = AddCronJob("0 */1 * * * *", cron2.FuncJob(func() {
		_, err = cache.NewBackupRecommendCache().Refresh(ctx, constant.WorkTypeAction)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("cache.NewBackupRecommendCache().Refresh failed")
		}
		_, err = cache.NewBackupRecommendCache().Refresh(ctx, constant.WorkTypeVideo)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("cache.NewBackupRecommendCache().Refresh failed")
		}
		_, err = cache.NewBackupRecommendCache().Refresh(ctx, constant.WorkTypePureVideo)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("cache.NewBackupRecommendCache().Refresh failed")
		}
	}))
	if err != nil {
		panic(err)
	}
}

func CheckDeleteWorks() bool {
	ctx := context.Background()
	workDeleteRepo := repo.NewWorkDeleteRepo()

	// 获取待删除的作品
	workDeletes, err := workDeleteRepo.FindBatch(ctx, 100)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("find work deletes failed")
		return true
	}
	if len(workDeletes) == 0 {
		return true
	}

	startTime := time.Now()
	deleteCount := 0

	workIds := lo.Map(workDeletes, func(workDelete model.L36ActionWorkDelete, _ int) int64 {
		return workDelete.WorkID
	})

	works, err := repo.NewWorkRepo().FindByIds(ctx, workIds)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("find works failed")
		return true
	}

	// 遍历每个待删除的作品
	for _, work := range works {
		workService := service.NewWorkService()
		err := workService.HandleWorkDelete(ctx, &work)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("handle work delete failed")
		}
	}
	if err := workDeleteRepo.DeleteByWorkIds(ctx, workIds); err != nil {
		elog.WithContext(ctx).WithError(err).Error("delete work deletes failed")
		return true
	}

	elog.WithContext(ctx).WithFields(map[string]interface{}{
		"delete_count": deleteCount,
		"cost":         time.Since(startTime).Milliseconds(),
	}).Info("clean work collect success")
	return false
}

func AddCronJob(spec string, cmd cron2.Job) (cron2.EntryID, error) {
	cronOnce.Do(func() {
		c = cron2.New(cron2.WithSeconds())
	})
	fmt.Println("AddCronJob", spec)
	return c.AddJob(spec, cmd)
}

func CronRun(cmd *cobra.Command, args []string) error {
	c.Start()
	listenStop(func() error {
		ctx := c.Stop()
		<-ctx.Done()
		return nil
	})
	return nil
}

// func RefreshWeekHot(ctx context.Context) {
// 	start := util.GetHalfHourAgo()
// 	weekStartTime := start - 7*24*60*60*1000
// 	weekEndTime := weekStartTime + 30*60*60*1000
// 	dayStartTime := start - 24*60*60*1000
// 	dayEndTime := dayStartTime + 30*60*60*1000
// 	likeRepo := repo.NewWorkLikeRepo()
// 	weekLikeCount, err := likeRepo.CountByTimeRange(ctx, weekStartTime, weekEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("likeRepo.CountByTimeRange failed")
// 		return
// 	}
// 	dayLikeCount, err := likeRepo.CountByTimeRange(ctx, dayStartTime, dayEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("likeRepo.CountByTimeRange failed")
// 		return
// 	}
// 	commentRepo := repo.NewCommentRepo()
// 	weekCommentCount, err := commentRepo.CountByTimeRange(ctx, weekStartTime, weekEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("commentRepo.CountByTimeRange failed")
// 		return
// 	}
// 	dayCommentCount, err := commentRepo.CountByTimeRange(ctx, dayStartTime, dayEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("commentRepo.CountByTimeRange failed")
// 		return
// 	}
// 	collectRepo := repo.NewWorkCollectRepo()
// 	weekCollectCount, err := collectRepo.CountByTimeRange(ctx, weekStartTime, weekEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("collectRepo.CountByTimeRange failed")
// 		return
// 	}
// 	dayCollectCount, err := collectRepo.CountByTimeRange(ctx, dayStartTime, dayEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("collectRepo.CountByTimeRange failed")
// 		return
// 	}
// 	shareRepo := repo.NewWorkShareRepo()
// 	weekShareCount, err := shareRepo.CountByTimeRange(ctx, weekStartTime, weekEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("shareRepo.CountByTimeRange failed")
// 		return
// 	}
// 	dayShareCount, err := shareRepo.CountByTimeRange(ctx, dayStartTime, dayEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("shareRepo.CountByTimeRange failed")
// 		return
// 	}
// 	playRepo := repo.NewWorkPlaySubRepo()
// 	weekPlayCount, err := playRepo.CountByTimeRange(ctx, weekStartTime, weekEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("playRepo.CountByTimeRange failed")
// 		return
// 	}
// 	dayPlayCount, err := playRepo.CountByTimeRange(ctx, dayStartTime, dayEndTime)
// 	if err != nil {
// 		elog.WithContext(ctx).WithError(err).Error("playRepo.CountByTimeRange failed")
// 		return
// 	}
// 	weekHotMap := make(map[int64]int)
// 	dayHotMap := make(map[int64]int)
// 	workCfg := config.C.Work
// 	for _, record := range weekLikeCount {
// 		weekHotMap[record.WorkId] += record.Count * workCfg.LikeHot
// 	}
// 	for _, record := range dayLikeCount {
// 		dayHotMap[record.WorkId] += record.Count * workCfg.LikeHot
// 	}
// 	for _, record := range weekCommentCount {
// 		weekHotMap[record.WorkId] += record.Count * workCfg.CommentHot
// 	}
// 	for _, record := range dayCommentCount {
// 		dayHotMap[record.WorkId] += record.Count * workCfg.CommentHot
// 	}
// 	for _, record := range weekCollectCount {
// 		weekHotMap[record.WorkId] += record.Count * workCfg.CollectHot
// 	}
// 	for _, record := range dayCollectCount {
// 		dayHotMap[record.WorkId] += record.Count * workCfg.CollectHot
// 	}
// 	for _, record := range weekShareCount {
// 		weekHotMap[record.WorkId] += record.Count * workCfg.ShareHot
// 	}
// 	for _, record := range dayShareCount {
// 		dayHotMap[record.WorkId] += record.Count * workCfg.ShareHot
// 	}
// 	for _, record := range weekPlayCount {
// 		weekHotMap[record.WorkId] += record.Count * workCfg.CompletePlayHot
// 	}
// 	for _, record := range dayPlayCount {
// 		dayHotMap[record.WorkId] += record.Count * workCfg.CompletePlayHot
// 	}
// 	allWorkIds := lo.Union(lo.Keys(weekHotMap), lo.Keys(dayHotMap))
// 	workRepo := repo.NewWorkRepo()
// 	for _, workId := range allWorkIds {
// 		weekHot := weekHotMap[workId]
// 		dayHot := dayHotMap[workId]
// 		err := workRepo.UpdateById(ctx, workId, map[string]interface{}{
// 			"week_hot": gorm.Expr("if(week_hot > ?, week_hot - ? , 0)", weekHot, weekHot),
// 			"day_hot":  gorm.Expr("if(day_hot > ?, day_hot - ? ,0 )", dayHot, dayHot),
// 		})
// 		if err != nil {
// 			elog.WithContext(ctx).WithError(err).Error("workRepo.UpdateById failed")
// 		}
// 	}
// 	elog.WithContext(ctx).Info("RefreshWeekHot success")
// }

func CheckReviewStatus(ctx context.Context) {
	// 检查15分钟前发布还在审核中的作品
	// 最多检查 45 分钟前发布的作品
	startTime := time.Now().Add(-45 * time.Minute).UnixMilli()
	endTime := time.Now().Add(-5 * time.Minute).UnixMilli()
	workRepo, workEsRepo := repo.NewWorkRepo(), es.NewWorkEsRepo()
	works, err := workRepo.FindReviewingWorksBetween(ctx, startTime, endTime)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("workRepo.FindReviewingWorksBetween failed")
		return
	}
	workIds := lo.Map(works, func(work model.L36ActionWork, i int) int64 {
		return work.ID
	})
	workTopicMap, err := repo.NewWorkTopicRepo().FindWorkTopics(ctx, workIds)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("workTopicRepo.FindWorkTopics failed")
		return
	}
	for _, work := range works {
		auditInfo, auditStatus, err := getNewAuditStatus(ctx, &work)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("getNewAuditStatus failed")
			continue
		}
		err = workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
			"audit_info":   util.StructToJson(auditInfo),
			"audit_status": auditStatus,
		})
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("workRepo.UpdateById failed")
			continue
		}
		_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
			"audit_info":   util.StructToJson(auditInfo),
			"audit_status": auditStatus,
		})
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("workCache.Update failed")
		}

		topicIds := workTopicMap[work.ID]
		work.AuditStatus = int8(auditStatus)
		if err := workEsRepo.IndexWork(ctx, &work, topicIds); err != nil {
			elog.WithContext(ctx).WithError(err).Error("index work error")
		}
	}
}

func getNewAuditStatus(ctx context.Context, work *model.L36ActionWork) (*service.AuditInfo, constant.FpReviewStatus, error) {
	fpService := service.NewFPService()
	var auditInfo service.AuditInfo
	err := sonic.Unmarshal([]byte(work.AuditInfo), &auditInfo)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("sonic.Unmarshal failed")
		return &auditInfo, constant.FpReviewStatusReviewing, err
	}
	auditFiles := []string{}
	images := util.JsonToStringArray(work.Images)
	for i, status := range auditInfo.Images {
		if fpService.IsReviewingStatus(constant.FpReviewStatus(status)) {
			auditFiles = append(auditFiles, images[i])
		}
	}
	if work.Video != "" && fpService.IsReviewingStatus(constant.FpReviewStatus(auditInfo.Video)) {
		auditFiles = append(auditFiles, work.Video)
	}

	if len(auditFiles) == 0 {
		return &auditInfo, constant.FpReviewStatusPass, nil
	}

	auditStatusMap := make(map[string]constant.FpReviewStatus)
	for _, file := range auditFiles {
		status, err := fpService.GetFpFileReviewStatus(ctx, file)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Warn("fpService.GetFpFileReviewStatus failed")
		}
		auditStatusMap[file] = status
	}
	for i, image := range images {
		if _, ok := auditStatusMap[image]; ok {
			auditInfo.Images[i] = int(auditStatusMap[image])
		}
	}
	if work.Video != "" {
		if status, ok := auditStatusMap[work.Video]; ok {
			auditInfo.Video = int(status)
		}
	}
	auditStatus := service.NewWorkService().GetAuditStatusByAuditInfo(&auditInfo)
	return &auditInfo, auditStatus, nil
}

func DecayWorkHot(ctx context.Context) {
	now := time.Now().UnixMilli()
	date := util.UnixMilliToDate(now)
	todayStart := util.GetTodayStart()
	workRepo, readOnlyWorkRepo, workEsRepo := repo.NewWorkRepo(), repo.NewReadonlyWorkRepo(), es.NewWorkEsRepo()
	// 如果日期
	offset := 0
	batch := 500
	updateBatch := 10
	for {
		works, err := readOnlyWorkRepo.FindToDecayWorks(ctx, offset, batch, todayStart)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("readOnlyWorkRepo.FindToDecayWorks failed")
			return
		}
		if len(works) == 0 {
			break
		}
		for i := 0; i < len(works); i += updateBatch {
			wg := wait.Group{}
			for j := i; j < i+updateBatch && j < len(works); j++ {
				work := works[j]
				wg.Start(func() {
					// 计算本次衰减的热度值，取昨日总衰减值的 1/12 (因为每2小时衰减一次，一天12次) 和 1 中的最大值
					decayHot := lo.Max([]int32{work.YesterdayDecayHot / 12, 1})
					// 获取上次衰减时间的日期
					decayDate := util.UnixMilliToDate(work.LastDecayTime)
					// 如果上次衰减日期与当前日期相同，说明是当天内的衰减
					if decayDate == date {
						err := workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
							// 直接减去本次衰减值，确保热度不低于0
							"hot":             gorm.Expr("if(hot > ?, hot - ?, 0)", decayHot, decayHot),
							"last_decay_time": now,
						})
						if err != nil {
							elog.WithContext(ctx).WithError(err).Warn("workRepo.UpdateById failed")
						}

						_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
							"hot":             work.Hot - decayHot,
							"last_decay_time": now,
						})
						if err != nil {
							elog.WithContext(ctx).WithError(err).Warn("workCache.Update failed")
						}

						// 同步到 es
						_ = workEsRepo.Update(ctx, int(work.ID), map[string]interface{}{
							"hot": work.Hot - decayHot,
						})

					} else {
						// 如果上次衰减日期与当前日期不同，说明是跨天衰减
						// 计算昨日应衰减的总热度值 = 当前热度 * 配置的衰减比例(config.C.Work.HotDecay)，至少为1
						yesterdayDecayHot := lo.Max([]int{int(float64(work.Hot) * (1 - config.C.Work.HotDecay)), 1})
						err := workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
							// 先减去昨日应衰减的总值
							"hot": gorm.Expr("if(hot > ?, hot - ?, 0)", yesterdayDecayHot, yesterdayDecayHot),
							// 更新昨日衰减值字段，该值将用于当天后续11次衰减的计算
							"yesterday_decay_hot": yesterdayDecayHot,
							"last_decay_time":     now,
						})
						if err != nil {
							elog.WithContext(ctx).WithError(err).Warn("workRepo.UpdateById failed")
						}

						_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
							"hot":             int(work.Hot) - yesterdayDecayHot,
							"last_decay_time": now,
						})

						// 同步到 es
						_ = workEsRepo.Update(ctx, int(work.ID), map[string]interface{}{
							"hot": int(work.Hot) - yesterdayDecayHot,
						})
					}
				})
			}
			wg.Wait()
		}
		offset = int(works[len(works)-1].ID)
	}
}
