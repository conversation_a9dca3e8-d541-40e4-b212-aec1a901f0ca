package cmd

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"

	"app/service"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(uploadDir)
}

var uploadDir = &cobra.Command{
	Use:   "upload-dir",
	Short: "Upload all files in directory to FP",
	Long:  "Scan directory and upload all files to FP service, output results to CSV",
	Run: func(cmd *cobra.Command, args []string) {
		if len(args) < 1 {
			fmt.Println("Please provide directory path")
			return
		}
		dirPath := args[0]
		outputFile := "upload_results.csv"
		if len(args) > 1 {
			outputFile = args[1]
		}

		bootstrap()
		fpService := service.NewFPService()

		// 创建CSV文件
		file, err := os.Create(outputFile)
		if err != nil {
			fmt.Printf("Error creating CSV file: %v\n", err)
			return
		}
		defer file.Close()

		writer := csv.NewWriter(file)
		defer writer.Flush()

		// 写入CSV头
		writer.Write([]string{"Original Path", "FP URL"})

		// 遍历目录
		err = filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 跳过目录
			if info.IsDir() {
				return nil
			}

			// 读取文件内容
			content, err := os.ReadFile(path)
			if err != nil {
				fmt.Printf("Error reading file %s: %v\n", path, err)
				return nil
			}

			// 上传到FP
			url, err := fpService.UploadPassContent(context.Background(), string(content), "system")
			if err != nil {
				fmt.Printf("Error uploading file %s: %v\n", path, err)
				return nil
			}

			// 获取文件名
			fileName := filepath.Base(path)

			// 写入CSV
			writer.Write([]string{fileName, url})
			fmt.Printf("Uploaded: %s -> %s\n", path, url)

			return nil
		})
		if err != nil {
			fmt.Printf("Error walking directory: %v\n", err)
			return
		}

		fmt.Printf("Upload completed. Results saved to %s\n", outputFile)
	},
}
