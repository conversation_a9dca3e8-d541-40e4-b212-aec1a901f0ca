package cmd

import (
	"fmt"
	"time"

	"app/dao/model"
	"app/dao/repo"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(createSubTable)
}

var createSubTable = &cobra.Command{
	Use:   "create_sub_table",
	Short: "CreateSubTable",
	Long:  "CreateSubTable",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		ctx := cmd.Context()

		// 有效浏览记录创建本月和上个月的分表
		now := time.Now()
		workPlaySubRepo := repo.NewWorkPlaySubRepo()
		monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
		lastMonth := monthStart.AddDate(0, -1, 0)

		monthStartDate := int64(monthStart.Year()*100 + int(monthStart.Month()))
		lastMonthStartDate := int64(lastMonth.Year()*100 + int(lastMonth.Month()))

		fmt.Println("CreateSubTable", monthStartDate)
		err := workPlaySubRepo.CreateSubTable(ctx, model.L36ActionWorkPlay{
			Date: monthStartDate,
		})
		if err != nil {
			panic(err)
		}

		fmt.Println("CreateSubTable", lastMonthStartDate)
		err = workPlaySubRepo.CreateSubTable(ctx, model.L36ActionWorkPlay{
			Date: lastMonthStartDate,
		})
		if err != nil {
			panic(err)
		}

		// 设计师经验限制创建100张分表
		expLimitSubRepo := repo.NewExpLimitSubRepo()
		for i := 0; i < 100; i++ {
			fmt.Println("CreateSubTable", i)
			err := expLimitSubRepo.CreateSubTable(ctx, model.L36ActionUserDesignerExpLimit{
				UserID: int64(i),
			})
			if err != nil {
				panic(err)
			}
		}
	},
}
