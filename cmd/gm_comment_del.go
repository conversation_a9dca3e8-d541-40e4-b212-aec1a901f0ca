package cmd

import (
	"fmt"

	"app/dao/repo"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

func init() {
	gmCommentDel.Flags().IntP("comment_id", "c", 0, "comment id")
	gmCommentDel.Flags().BoolP("confirm", "y", false, "confirm")
	gmCommentDel.MarkFlagRequired("comment_id")
	root.AddCommand(gmCommentDel)
}

var gmCommentDel = &cobra.Command{
	Use:   "gm_comment_del",
	Short: "GmCommentDel",
	Long:  "GmCommentDel",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		ginCtx := gin.Context{}

		commentId, err := cmd.Flags().GetInt("comment_id")
		if err != nil {
			fmt.Println("parse comment id failed", err)
			return
		}
		if commentId == 0 {
			fmt.Println("comment id is required")
			return
		}

		confirm, err := cmd.Flags().GetBool("confirm")
		if err != nil {
			fmt.Println("parse confirm failed", err)
			return
		}
		if !confirm {
			comment, err := repo.NewCommentRepo().FindById(&ginCtx, int64(commentId))
			if err != nil {
				fmt.Println("find comment failed", err)
				return
			}
			fmt.Println("comment:", comment.Content)
			return
		}

		if err != nil {
			fmt.Println("parse comment id failed", err)
			return
		}

		gmService := service.NewGmService()
		err = gmService.GmCommentDel(&ginCtx, &schema.GmCommentDelReq{
			CommentBase: schema.CommentBase{
				CommentId: int64(commentId),
			},
		})
		if err != nil {
			fmt.Println("gm comment del failed", err)
			return
		}

		fmt.Println("gm comment del success, commentId:", commentId)
	},
}
