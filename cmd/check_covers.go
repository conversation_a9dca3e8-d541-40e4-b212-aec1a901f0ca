package cmd

import (
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"app/dao/model"
	"app/dao/repo"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(checkCovers)
}

var checkCovers = &cobra.Command{
	Use:   "check-covers",
	Short: "Scan work covers for 404 status",
	Long:  "Scans all works in l36_action_work, checks if the cover URL returns 404, and outputs the results to not_found_covers.csv.",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		log.Println("Starting cover scan...")
		startTime := time.Now()

		workRepo := repo.NewWorkRepo()
		csvFileName := "not_found_covers.csv"
		csvFile, err := os.Create(csvFileName)
		if err != nil {
			log.Fatalf("Failed to create CSV file %s: %v", csvFileName, err)
		}
		defer csvFile.Close()

		csvWriter := csv.NewWriter(csvFile)
		defer csvWriter.Flush()

		// Write CSV header
		if err := csvWriter.Write([]string{"id", "cover"}); err != nil {
			log.Fatalf("Failed to write CSV header: %v", err)
		}

		const batchSize = 1000
		const concurrency = 20
		var lastID int64 = 0
		var wg sync.WaitGroup
		sem := make(chan struct{}, concurrency)
		resultChan := make(chan []string, concurrency*2)
		httpClient := &http.Client{Timeout: 10 * time.Second}

		// Goroutine to write results to CSV
		var csvWg sync.WaitGroup
		csvWg.Add(1)
		go func() {
			defer csvWg.Done()
			for result := range resultChan {
				if err := csvWriter.Write(result); err != nil {
					log.Printf("Error writing record to CSV: %v", err)
				}
			}
			csvWriter.Flush()
		}()

		processedCount := 0
		notFoundCount := 0

		for {
			var works []model.L36ActionWork
			// Adjust query as needed based on your repo implementation
			// Assuming workRepo itself has the DB connection or a method to get it
			// Use NormalScope based on usage in server_service.go
			// Use context.Background() for simplicity
			err := workRepo.NormalScope(context.Background()).Where("id > ?", lastID).Order("id asc").Limit(batchSize).Find(&works).Error
			if err != nil {
				log.Printf("Error fetching works batch (lastID: %d): %v", lastID, err)
				// Decide whether to break or continue based on error type if needed
				break
			}

			if len(works) == 0 {
				log.Println("No more works to process.")
				break
			}

			log.Printf("Processing batch of %d works starting from ID > %d...", len(works), lastID)

			for i := range works {
				work := works[i]
				if work.Cover == "" {
					continue
				}

				wg.Add(1)
				sem <- struct{}{}

				go func(w model.L36ActionWork) {
					defer wg.Done()
					defer func() { <-sem }()

					req, err := http.NewRequest("HEAD", w.Cover, nil)
					if err != nil {
						log.Printf("Error creating request for work %d (%s): %v", w.ID, w.Cover, err)
						return
					}
					req.Header.Set("User-Agent", "cover-check-script/1.0")

					resp, err := httpClient.Do(req)
					if err != nil {
						log.Printf("Error checking cover for work %d (%s): %v", w.ID, w.Cover, err)
						return
					}
					defer resp.Body.Close()

					if resp.StatusCode == http.StatusNotFound {
						resultChan <- []string{strconv.FormatInt(w.ID, 10), w.Cover}
						notFoundCount++
					}
				}(work)
				processedCount++
			}

			lastID = works[len(works)-1].ID

			if processedCount%5000 == 0 {
				log.Printf("Processed %d works so far...", processedCount)
			}
		}

		wg.Wait()
		close(resultChan)
		csvWg.Wait()

		duration := time.Since(startTime)
		log.Printf("Cover scan finished in %s.", duration)
		log.Printf("Checked %d works.", processedCount)
		log.Printf("Found %d covers with 404 status.", notFoundCount)
		log.Printf("Results saved to %s", csvFileName)

		// Remove unused variables from the original test command
		// userId := 1
		// roleId := 206820201161
		// tokenTime := time.Now().Unix()
		// Assuming SkeyService is available after bootstrap()
		// token := service.NewSkeyService().GenerateSkey(int64(userId), int64(roleId), tokenTime)
		// fmt.Println("Generated Token:", token) // Example token generation remains
		fmt.Println("Cover check complete. See logs and not_found_covers.csv.")
	},
}
