package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"app/conn"
	"app/dao/cache"
	"app/dao/model"
	"app/dao/repo"
	"app/external"
	"app/service"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(normalizeAudio)
	normalizeAudio.Flags().StringP("file", "f", "", "Output file path")
	normalizeAudio.Flags().Int64P("id", "i", 0, "Specific work ID to process (if not provided, process all works)")
	normalizeAudio.Flags().Int64P("start-id", "s", 0, "Start ID for batch processing (default: 0)")
	normalizeAudio.Flags().IntP("workers", "w", 5, "Number of concurrent workers for batch processing (default: 5)")
}

var normalizeAudio = &cobra.Command{
	Use:   "normalize-audio",
	Short: "Normalize audio for all works",
	Long:  "Scans all works, normalizes audio URLs in property field and updates the database.",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		// 获取输出文件路径
		csvFileName, _ := cmd.Flags().GetString("file")
		// 获取指定的work ID
		workID, _ := cmd.Flags().GetInt64("id")
		// 获取开始处理的ID
		startID, _ := cmd.Flags().GetInt64("start-id")
		// 获取并发worker数量
		maxWorkers, _ := cmd.Flags().GetInt("workers")
		ctx := context.Background()

		log.Println("Starting audio normalization scan...")
		startTime := time.Now()

		workRepo := repo.NewWorkRepo()
		fpService := service.NewFPService()
		processedCount := 0
		normalizedCount := 0

		// 如果指定了work ID，只处理这个ID
		if workID > 0 {
			log.Printf("Processing specific work ID: %d", workID)

			var work model.L36ActionWork
			err := workRepo.NormalScope(context.Background()).
				Where("id = ? AND property != ''", workID).
				First(&work).Error
			if err != nil {
				log.Printf("Error fetching work with ID %d: %v", workID, err)
				return
			}

			success := processWork(ctx, work, fpService)
			if success {
				normalizedCount++
			}
			processedCount++
		} else {
			// 并发批量处理逻辑
			const batchSize = 1000
			var lastID int64 = startID

			// 创建工作池
			workChan := make(chan model.L36ActionWork, batchSize)
			var wg sync.WaitGroup
			var mu sync.Mutex

			// 启动指定数量的worker goroutines
			for i := 0; i < maxWorkers; i++ {
				wg.Add(1)
				go func(workerID int) {
					defer wg.Done()
					log.Printf("Worker %d started", workerID)

					for work := range workChan {
						success := processWork(ctx, work, fpService)

						mu.Lock()
						if success {
							normalizedCount++
						}
						processedCount++
						if processedCount%100 == 0 {
							log.Printf("Processed %d works so far...", processedCount)
						}
						mu.Unlock()
					}
					log.Printf("Worker %d finished", workerID)
				}(i)
			}

			log.Printf("Starting batch processing from ID > %d with %d workers...", lastID, maxWorkers)

			// 生产者：分批获取数据并发送到工作通道
			go func() {
				defer close(workChan)

				for {
					var works []model.L36ActionWork
					err := workRepo.NormalScope(context.Background()).
						Where("id > ? AND property != ''", lastID).
						Order("id asc").
						Limit(batchSize).
						Find(&works).Error
					if err != nil {
						log.Printf("Error fetching works batch (lastID: %d): %v", lastID, err)
						break
					}

					if len(works) == 0 {
						log.Println("No more works to process.")
						break
					}

					log.Printf("Fetched batch of %d works starting from ID > %d...", len(works), lastID)

					// 将works发送到工作通道
					for _, w := range works {
						workChan <- w
					}

					lastID = works[len(works)-1].ID
					log.Printf("Current batch max ID: %d", lastID)
				}
			}()

			// 等待所有worker完成
			wg.Wait()
		}

		duration := time.Since(startTime)
		log.Printf("Audio normalization scan finished in %s.", duration)
		log.Printf("Checked %d works.", processedCount)
		log.Printf("Normalized %d audio URLs.", normalizedCount)
		log.Printf("Results saved to %s", csvFileName)

		fmt.Println("Audio normalization complete. See logs for details.")
	},
}

// processWork 处理单个work的音频标准化
func processWork(ctx context.Context, w model.L36ActionWork, fpService *service.FPService) bool {
	workRepo := repo.NewWorkRepo()

	// 下载 property 内容
	var property map[string]interface{}
	_, err := kit.HttpGet2(ctx, &property, w.Property, conn.GetClient())
	if err != nil {
		log.Printf("Error downloading property for work %d: %v", w.ID, err)
		return false
	}

	// 获取 speechUrl
	speechURL, ok := property["speechUrl"].(string)
	if !ok || speechURL == "" {
		log.Printf("No speechUrl found for work %d", w.ID)
		return false
	}

	// 调用音频标准化
	resp, err := external.NormalizeAudio(ctx, &external.AudioNormalizeReq{
		URL: speechURL,
		UID: strconv.FormatInt(w.RoleID, 10),
	})
	if err != nil {
		log.Printf("Error normalizing audio for work %d: %v", w.ID, err)
		return false
	}

	fmt.Println(resp.AudioURL)

	// 更新 property 中的 speechUrl
	property["speechUrl"] = resp.AudioURL
	newProperty, err := json.Marshal(property)
	if err != nil {
		log.Printf("Error marshalling property for work %d: %v", w.ID, err)
		return false
	}

	// 上传到 fp
	// 上传 Property 到 FP
	propertyUrl, err := fpService.UploadPassContent(context.Background(), string(newProperty), strconv.FormatInt(w.RoleID, 10))
	if err != nil {
		log.Printf("Error uploading property for work %d: %v", w.ID, err)
		return false
	}

	fmt.Printf("update work %d property from %s to %s\n", w.ID, w.Property, propertyUrl)

	// 更新数据库
	err = workRepo.UpdateById(ctx, w.ID, map[string]interface{}{
		"property": propertyUrl,
	})
	if err != nil {
		log.Printf("Error updating work %d: %v", w.ID, err)
		return false
	}

	cache.NewWorkCacheClass().Del(ctx, w.ID)

	log.Printf("Successfully processed work %d", w.ID)
	return true
}
