package cmd

import (
	"app/dao/es"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(initEsIndex)
}

var initEsIndex = &cobra.Command{
	Use:   "init_es_index",
	Short: "InitEsIndex",
	Long:  "InitEsIndex",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		ctx := cmd.Context()

		designerEsRepo := es.NewDesignerEsRepo()
		if err := designerEsRepo.CreateIndex(ctx); err != nil {
			panic(err)
		}

		workEsRepo := es.NewWorkEsRepo()
		if err := workEsRepo.CreateIndex(ctx); err != nil {
			panic(err)
		}
	},
}
