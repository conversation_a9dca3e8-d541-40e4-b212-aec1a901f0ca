package cmd

import (
	"context"
	"log"
	"time"

	"app/conn"
	"app/dao/model"
	"app/dao/repo"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(recalculateCollectCount)
}

var recalculateCollectCount = &cobra.Command{
	Use:   "recalculate_collect_count",
	Short: "Recalculate collect count",
	Long:  "Recalculate collect count",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		ctx := context.Background()
		db := conn.GetDB()
		workCollectRepo := repo.NewWorkCollectRepo()

		// 获取用户总数
		var totalUsers int64
		if err := db.Model(&model.L36ActionUser{}).Count(&totalUsers).Error; err != nil {
			log.Fatalf("Failed to count users: %v", err)
		}
		log.Printf("Total users to process: %d", totalUsers)

		// 批量处理用户
		batchSize := 100
		offset := 0
		count := 0
		for {
			var users []model.L36ActionUser
			if err := db.Order("id asc").Where("id > ?", offset).Limit(batchSize).Find(&users).Error; err != nil {
				log.Fatalf("Failed to get users: %v", err)
			}

			if len(users) == 0 {
				break
			}

			offset = int(users[len(users)-1].ID)
			count += len(users)

			// 获取这批用户的ID列表
			userIDs := make([]int64, 0, len(users))
			for _, user := range users {
				userIDs = append(userIDs, user.ID)
			}

			// 一次性查询这批用户的收藏数
			type UserCollectCount struct {
				UserID int64
				Count  int64
			}
			var counts []UserCollectCount
			if err := workCollectRepo.NormalScope(ctx).
				Where("user_id IN ? AND is_delete = 0", userIDs).
				Group("user_id").
				Select("user_id, COUNT(*) as count").
				Find(&counts).Error; err != nil {
				log.Printf("Failed to count collects for batch: %v", err)
				continue
			}

			// 创建用户ID到收藏数的映射
			countMap := make(map[int64]int64)
			for _, count := range counts {
				countMap[count.UserID] = count.Count
			}

			// 更新这批用户的收藏数
			for _, user := range users {
				count := countMap[user.ID]
				if err := db.Model(&user).
					Update("collect_count", count).Error; err != nil {
					log.Printf("Failed to update collect_count for user %d: %v", user.ID, err)
					continue
				}

				log.Printf("Updated user %d collect_count to %d", user.ID, count)
			}

			log.Printf("Processed %d/%d users, offset: %d", count, totalUsers, offset)
			time.Sleep(100 * time.Millisecond) // 添加小延迟避免数据库压力过大
		}

		log.Println("Recalculation completed successfully")
	},
}
