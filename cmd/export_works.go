package cmd

import (
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"app/constant"
	"app/dao/model"
	"app/dao/repo"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(exportWorks)
	exportWorks.Flags().StringP("output", "o", "exported_works.csv", "指定输出文件路径")
}

var exportWorks = &cobra.Command{
	Use:   "export-works",
	Short: "Export works data to CSV",
	Long:  "导出type=1和type=2的未删除且审核通过的作品数据到CSV文件中，包含字段：id,type,name,property,images,resample_url,scene,gender",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		// 获取输出文件路径
		csvFileName, _ := cmd.Flags().GetString("output")

		log.Println("开始导出作品数据...")
		startTime := time.Now()

		workRepo := repo.NewWorkRepo()
		csvFile, err := os.Create(csvFileName)
		if err != nil {
			log.Fatalf("创建CSV文件失败 %s: %v", csvFileName, err)
		}
		defer csvFile.Close()

		csvWriter := csv.NewWriter(csvFile)
		defer csvWriter.Flush()

		// 写入CSV头部
		if err := csvWriter.Write([]string{"id", "type", "cover", "name", "property", "images", "resample_url", "scene", "video", "gender"}); err != nil {
			log.Fatalf("写入CSV头部失败: %v", err)
		}

		const batchSize = 1000
		var lastID int64 = 0
		processedCount := 0
		exportedCount := 0

		for {
			var works []model.L36ActionWork
			err := workRepo.NormalScope(context.Background()).
				Where("id > ? AND type IN (?, ?) AND is_delete = ? AND audit_status = ?",
					lastID, 1, 2, constant.StatusNormal, constant.FpReviewStatusPass).
				Order("id asc").
				Limit(batchSize).
				Find(&works).Error
			if err != nil {
				log.Printf("获取作品批次失败 (lastID: %d): %v", lastID, err)
				break
			}

			if len(works) == 0 {
				log.Println("没有更多作品需要处理。")
				break
			}

			log.Printf("正在处理从ID > %d 开始的 %d 个作品...", lastID, len(works))

			for _, work := range works {
				// 写入CSV记录
				if err := csvWriter.Write([]string{
					strconv.FormatInt(work.ID, 10),          // id
					strconv.FormatInt(int64(work.Type), 10), // type
					work.Cover,                              // cover
					work.Name,                               // name
					work.Property,                           // property
					work.Images,                             // images
					work.ResampleURL,                        // resample_url
					strconv.FormatInt(work.Scene, 10),       // scene
					work.Video,                              // video
					strconv.FormatInt(int64(work.Gender), 10), // gender
				}); err != nil {
					log.Printf("写入CSV记录失败: %v", err)
					continue
				}
				exportedCount++
			}

			processedCount += len(works)
			lastID = works[len(works)-1].ID
			csvWriter.Flush()

			if processedCount%1000 == 0 {
				log.Printf("已处理 %d 个作品，已导出 %d 个符合条件的作品...", processedCount, exportedCount)
			}
		}

		duration := time.Since(startTime)
		log.Printf("作品导出完成，耗时 %s", duration)
		log.Printf("总计处理 %d 个作品", processedCount)
		log.Printf("导出 %d 个符合条件的作品", exportedCount)
		log.Printf("结果已保存到 %s", csvFileName)

		fmt.Printf("作品导出完成。已导出 %d 个作品到 %s\n", exportedCount, csvFileName)
	},
}
