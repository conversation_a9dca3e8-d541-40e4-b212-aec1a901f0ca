package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
)

var uploadWork = &cobra.Command{
	Use:   "upload_work",
	Short: "Upload work from file",
	Long:  "Upload work from file",
	Run: func(cmd *cobra.Command, args []string) {
		if len(args) < 1 {
			fmt.Println("Please provide input file path")
			return
		}

		filePath := args[0]
		ext := strings.ToLower(filepath.Ext(filePath))

		var err error
		if ext == ".xlsx" {
			err = processUploadWorkFromExcel(filePath)
		} else {
			err = processUploadWork(filePath)
		}

		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
	},
}

// 添加 skipRows 变量
var skipRows int

func init() {
	// 添加 skip-rows 参数
	uploadWork.Flags().IntVar(&skipRows, "skip-rows", 0, "Number of rows to skip at the beginning of the file")
	root.AddCommand(uploadWork)
}

type UploadData struct {
	RoleId      string      `json:"role_id"`
	Cover       string      `json:"cover"`
	Type        string      `json:"type"`
	Name        string      `json:"name"`
	Summary     string      `json:"summary"`
	Video       string      `json:"video"`
	Images      string      `json:"images"`
	Contents    []int       `json:"contents"`
	Scene       string      `json:"scene"`
	Gender      string      `json:"gender"`
	Visibility  string      `json:"visibility"`
	Property    interface{} `json:"property"`
	ResampleUrl string      `json:"resample_url"`
}

// convertToString 去除字符串首尾空格和引号
func convertToString(str string) string {
	str = strings.Trim(str, " ")
	str = strings.Trim(str, "\"")
	return str
}

func processUploadWorkFromExcel(filePath string) error {
	bootstrap()

	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return fmt.Errorf("error opening excel file: %v", err)
	}
	defer f.Close()

	// 获取第一个工作表
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return fmt.Errorf("no sheets found in excel file")
	}

	// 读取所有行
	rows, err := f.GetRows(sheets[0])
	if err != nil {
		return fmt.Errorf("error reading rows: %v", err)
	}

	if len(rows) < 2 { // 至少需要标题行和一行数据
		return fmt.Errorf("excel file must contain at least header row and one data row")
	}

	// 计算实际需要跳过的行数（标题行 + 用户指定的行数）
	actualSkipRows := 1 + skipRows // 2 是标题行和第一行数据

	// 处理每一行数据（跳过标题行和用户指定的行数）
	for i, row := range rows {
		if i < actualSkipRows { // 跳过标题行和用户指定的行数
			continue
		}

		// 将Excel行转换为UploadData结构
		data := parseExcelRow(row)

		// 检查 Property 是否为有效的 JSON 字符串
		propertyStr, ok := data.Property.(string)
		if !ok {
			propertyStr = ""
		}

		// 允许 propertyStr 为空，不做任何处理

		if strings.TrimSpace(propertyStr) != "" {
			var propertyJSON interface{}
			if err := json.Unmarshal([]byte(propertyStr), &propertyJSON); err != nil {
				return fmt.Errorf("invalid property JSON format in row %d: %v", i+1, err)
			}
		}

		// 上传作品
		if err := uploadWorkToServer(data, propertyStr); err != nil {
			return fmt.Errorf("error uploading work from row %d: %v", i+1, err)
		}

		fmt.Printf("Successfully processed row %d\n", i+1)
	}

	return nil
}

func parseExcelRow(row []string) *UploadData {
	data := &UploadData{
		Contents: []int{},
	}

	// 按照图片表头顺序依次赋值
	if len(row) > 0 {
		data.RoleId = convertToString(row[0])
	}
	if len(row) > 1 {
		data.Type = convertToString(row[1])
	}
	if len(row) > 2 {
		data.Name = convertToString(row[2])
	}
	if len(row) > 3 {
		data.Summary = convertToString(row[3])
	}
	if len(row) > 4 {
		data.Video = convertToString(row[4])
	}
	if len(row) > 5 {
		data.Images = convertToString(row[5])
	}
	if len(row) > 6 {
		contentsStr := convertToString(row[6])
		for _, c := range strings.Split(contentsStr, ",") {
			if num, err := strconv.Atoi(strings.TrimSpace(c)); err == nil {
				data.Contents = append(data.Contents, num)
			}
		}
	}
	if len(row) > 7 {
		data.Cover = convertToString(row[7])
	}
	if len(row) > 8 {
		data.Scene = convertToString(row[8])
	}
	if len(row) > 9 {
		data.Gender = convertToString(row[9])
	}
	if len(row) > 10 {
		data.Visibility = convertToString(row[10])
	}
	if len(row) > 11 {
		data.Property = convertToString(row[11])
	}

	// Visibility 默认值
	if data.Visibility == "" {
		data.Visibility = "1"
	}

	fmt.Println(data)
	return data
}

func processUploadWork(filePath string) error {
	bootstrap()

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("error reading file: %v", err)
	}

	// 解析数据
	data := parseUploadData(string(content))

	// 检查 Property 是否为有效的 JSON 字符串
	propertyStr, ok := data.Property.(string)
	if !ok {
		return fmt.Errorf("property data is not a string")
	}

	var propertyJSON interface{}
	if err := json.Unmarshal([]byte(propertyStr), &propertyJSON); err != nil {
		return fmt.Errorf("invalid property JSON format: %v", err)
	}

	return uploadWorkToServer(data, propertyStr)
}

func uploadWorkToServer(data *UploadData, propertyStr string) error {
	// 创建一个新的 FP service 实例
	fpService := service.NewFPService()

	// 上传 Property 到 FP
	propertyUrl := ""
	if strings.TrimSpace(propertyStr) != "" {
		var err error
		propertyUrl, err = fpService.UploadPassContent(context.Background(), propertyStr, data.RoleId)
		if err != nil {
			return fmt.Errorf("error uploading property: %v", err)
		}
	}

	// 准备创建作品的参数
	workService := service.NewWorkService()

	typeInt, _ := strconv.Atoi(data.Type)
	roleId, _ := strconv.ParseInt(strings.TrimSpace(data.RoleId), 10, 64)
	sceneInt, _ := strconv.ParseInt(data.Scene, 10, 64)

	// 创建 gin context
	ginCtx := &gin.Context{}
	// 初始化 Request 字段，避免空指针引用
	ginCtx.Request = &http.Request{}
	// 先查询角色信息
	roleInfo, err := repo.NewRoleRepo().FindOneByRoleId(ginCtx, roleId)
	if err != nil {
		return errors.New(errors.ServerError)
	}
	if roleInfo == nil {
		return errors.New(errors.RoleNotExist)
	}

	// 同步用户信息
	user, err := repo.NewUserRepo().SyncUser(ginCtx, roleInfo.Aid, roleId)
	if err != nil {
		return errors.New(errors.ServerError)
	}
	if user == nil {
		return errors.New(errors.UserNotExist)
	}

	// 处理 visibility 默认值
	visibilityInt := 1 // 默认为公开
	if data.Visibility != "" {
		visibilityInt, _ = strconv.Atoi(data.Visibility)
	}

	// 处理 gender 默认值
	genderInt := int(roleInfo.Gender) // 默认使用角色的性别
	if data.Gender != "" {
		if g, err := strconv.Atoi(data.Gender); err == nil {
			genderInt = g
		}
	} else {
		genderInt = int(roleInfo.Gender)
	}

	params := &schema.WorkCreateReq{
		UserBase: schema.UserBase{
			UserId: user.ID,
			RoleId: roleId,
		},
		Type:        typeInt,
		Name:        data.Name,
		Summary:     data.Summary,
		Cover:       data.Cover,
		Video:       data.Video,
		Images:      []string{},
		Contents:    data.Contents,
		Scene:       sceneInt,
		Gender:      genderInt,
		Property:    propertyUrl,
		Visibility:  visibilityInt,
		ResampleUrl: data.ResampleUrl,
	}

	// 创建作品
	res, errCreate := workService.WorkCreate(ginCtx, params)
	if errCreate != nil {
		return errCreate
	}

	fmt.Printf("Successfully created work with ID: %d\n", res.WorkId)
	return nil
}

func parseUploadData(content string) *UploadData {
	lines := strings.Split(content, "\n")
	data := &UploadData{
		Contents: []int{},
	}

	var propertyContent strings.Builder
	inProperty := false
	propertyContent.WriteString("{")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if strings.HasPrefix(line, "Property:") {
			inProperty = true
			continue
		}

		if inProperty {
			if strings.HasPrefix(line, "ResampleUrl:") {
				inProperty = false
			} else {
				// 跳过注释行
				if strings.HasPrefix(line, "--") {
					continue
				}

				// 处理属性行
				line = strings.TrimRight(line, ",") // 移除末尾的逗号

				// 如果是结束大括号，直接结束
				if line == "}" {
					// 如果最后有逗号，先移除
					propertyStr := propertyContent.String()
					if strings.HasSuffix(propertyStr, ",") {
						propertyContent.Reset()
						propertyContent.WriteString(propertyStr[:len(propertyStr)-1])
					}
					propertyContent.WriteString("}")
					inProperty = false
					continue
				}

				parts := strings.SplitN(line, "=", 2)
				if len(parts) == 2 {
					key := strings.TrimSpace(parts[0])
					value := strings.TrimSpace(parts[1])

					// 处理值中多余的引号
					value = strings.Trim(value, `""`)

					// 如果值已经是字符串（带引号），数字，布尔值或对象/数组，则直接使用
					if strings.HasPrefix(value, "\"") ||
						strings.HasPrefix(value, "{") ||
						strings.HasPrefix(value, "[") ||
						value == "true" ||
						value == "false" ||
						value == "null" ||
						isNumeric(value) {
						propertyContent.WriteString(fmt.Sprintf("\"%s\": %s", key, value))
					} else {
						// 否则将值作为字符串处理
						propertyContent.WriteString(fmt.Sprintf("\"%s\": \"%s\"", key, value))
					}

					// 如果不是最后一个属性，添加逗号
					if !strings.HasSuffix(line, "}") {
						propertyContent.WriteString(",")
					}
				}
				continue
			}
		}

		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.Trim(strings.TrimSpace(parts[1]), `"，"`)

		switch key {
		case "RoleId":
			data.RoleId = value
		case "Cover":
			data.Cover = value
		case "Type":
			data.Type = value
		case "Name":
			data.Name = value
		case "Summary":
			data.Summary = value
		case "Video":
			data.Video = value
		case "Images":
			data.Images = value
		case "Contents":
			for _, c := range strings.Split(value, " ") {
				if num, err := strconv.Atoi(c); err == nil {
					data.Contents = append(data.Contents, num)
				}
			}
		case "Scene":
			data.Scene = value
		case "Gender":
			data.Gender = value
		case "Visibility":
			data.Visibility = value
		case "ResampleUrl":
			data.ResampleUrl = value
		}
	}

	// 如果 Property 没有正确结束，添加结束大括号
	propertyStr := propertyContent.String()
	if !strings.HasSuffix(propertyStr, "}") {
		// 如果最后有逗号，先移除
		if strings.TrimSuffix(propertyStr, ",") == propertyStr {
			propertyStr = propertyStr[:len(propertyStr)-1]
		}
		propertyStr += "}"
	}

	// 保存原始字符串格式
	data.Property = propertyStr

	fmt.Println(data.Property)

	return data
}

// isNumeric 检查字符串是否为数字
func isNumeric(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}
