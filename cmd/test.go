package cmd

import (
	"fmt"

	"app/schema"
	"app/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(test)
}

var test = &cobra.Command{
	Use:   "test",
	Short: "Test",
	Long:  "Test",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		minId := 1499342
		maxId := 1499346

		gmService := service.NewGmService()
		for i := minId; i <= maxId; i++ {
			fmt.Println(i)
			ginCtx := &gin.Context{}
			err := gmService.GmWorkDel(ginCtx, &schema.GmWorkDelReq{
				WorkBase: schema.WorkBase{
					WorkId: int64(i),
				},
			})
			if err != nil {
				fmt.Println(err)
			}
		}
	},
}
