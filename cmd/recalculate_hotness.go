package cmd

import (
	"context"
	"fmt"
	"log"
	"math"
	"time"

	"app/config"
	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/util"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(recalculateHotnessCmd)
}

var recalculateHotnessCmd = &cobra.Command{
	Use:   "recalculate-hotness",
	Short: "Recalculate hotness for approved works",
	Long:  "Scans all approved works in l36_action_work and recalculates their hotness based on the formula defined in WorkCfg.",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		log.Println("Starting hotness recalculation...")
		startTime := time.Now()

		workRepo := repo.NewWorkRepo()
		workCfg := config.C.Work

		const batchSize = 1000
		var lastID int64 = 0
		processedCount := 0
		updatedCount := 0
		errorCount := 0

		todayStart := util.GetTodayStart()

		for {
			var works []model.L36ActionWork
			// Fetch approved works only (audit_status = 0)
			// Filter out deleted works (is_delete = 0)
			err := workRepo.NormalScope(context.Background()).
				Where("id > ? AND audit_status = ? AND is_delete = ?", lastID, constant.FpReviewStatusPass, constant.StatusNormal).
				Order("id asc").
				Limit(batchSize).
				Find(&works).Error
			if err != nil {
				log.Printf("Error fetching works batch (lastID: %d): %v", lastID, err)
				break
			}

			if len(works) == 0 {
				log.Println("No more approved works to process.")
				break
			}

			log.Printf("Processing batch of %d works starting from ID > %d...", len(works), lastID)

			for i := range works {
				work := works[i]

				// Calculate new hotness based on WorkCfg
				rawHot := work.LikeCount*int32(workCfg.LikeHot) +
					work.CommentCount*int32(workCfg.CommentHot) +
					work.CollectCount*int32(workCfg.CollectHot) +
					work.UseCount*int32(workCfg.UseHot) +
					work.ShareCount*int32(workCfg.ShareHot) +
					work.CompleteCount*int32(workCfg.CompletePlayHot)

				// 需要计算衰减系数

				// 计算作品发布距离现在多少天
				days := (todayStart - work.Ctime) / (24 * 60 * 60 * 1000)

				// 计算衰减系数
				decayCoefficient := math.Pow(workCfg.HotDecay, float64(days))

				// 计算衰减后的热度
				newHot := int32(float64(rawHot) * decayCoefficient)

				todayDecayHot := int32(float64(newHot) * (1 - workCfg.HotDecay))

				if newHot != work.Hot && newHot > 0 {
					// Update the hotness value in the database
					updateErr := workRepo.UpdateById(context.Background(), work.ID, map[string]interface{}{
						"hot":                 newHot,
						"yesterday_decay_hot": todayDecayHot,
						"last_decay_time":     startTime.UnixMilli(),
					})
					if updateErr != nil {
						log.Printf("Error updating work hotness for work %d: %v", work.ID, updateErr)
						errorCount++
					} else {
						updatedCount++

						// 更新缓存
						cache.GetWorkCache().Update(context.Background(), work.ID, map[string]interface{}{
							"hot": newHot,
						})

						// 更新es
						workEsRepo := es.NewWorkEsRepo()
						// Need to pass the updated work to IndexWork
						// Re-fetch the work or update the local copy before indexing
						// For simplicity, let's update the local copy for indexing
						updatedWork := work // Create a copy to avoid modifying the loop variable directly
						updatedWork.Hot = newHot
						updatedWork.YesterdayDecayHot = todayDecayHot
						updatedWork.LastDecayTime = startTime.UnixMilli()
						workEsRepo.IndexWork(context.Background(), &updatedWork, nil)
					}
				}
				processedCount++
			}

			lastID = works[len(works)-1].ID

			fmt.Println("processedCount", processedCount, "updatedCount", updatedCount, "errorCount", errorCount)
		}

		duration := time.Since(startTime)
		log.Printf("Hotness recalculation finished in %s.", duration)
		log.Printf("Processed %d approved works.", processedCount)
		log.Printf("Updated hotness for %d works.", updatedCount)
		if errorCount > 0 {
			log.Printf("Encountered %d errors during update.", errorCount)
		}
		fmt.Println("Hotness recalculation complete. See logs for details.")
	},
}
