package cmd

import (
	"context"
	"fmt"
	"time"

	"app/dao/cache"
	"app/dao/model"
	"app/dao/repo"

	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

func init() {
	root.AddCommand(setTopTopicCmd)
}

var setTopTopicCmd = &cobra.Command{
	Use:   "set-top-topic",
	Short: "Set a top topic",
	Long:  "Set a top topic (create if not exists, update if exists) with specified parameters",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		// 获取参数
		topicID, _ := cmd.Flags().GetInt64("topic_id")
		weight, _ := cmd.Flags().GetInt("weight")
		startTime, _ := cmd.Flags().GetString("start_time")
		endTime, _ := cmd.Flags().GetString("end_time")

		// 验证话题是否存在
		topicRepo := repo.NewTopicRepo()
		topic, err := topicRepo.FindById(context.Background(), topicID)
		if err != nil || topic == nil {
			fmt.Printf("Error: Topic with ID %d does not exist\n", topicID)
			return
		}

		// 解析时间
		start, err := time.Parse(time.DateTime, startTime)
		if err != nil {
			fmt.Printf("Invalid start time format: %v\n", err)
			return
		}
		end, err := time.Parse(time.DateTime, endTime)
		if err != nil {
			fmt.Printf("Invalid end time format: %v\n", err)
			return
		}

		// 验证时间范围
		if end.Before(start) {
			fmt.Println("Error: End time must be after start time")
			return
		}

		// 创建置顶话题对象
		topTopic := &model.L36ActionTopTopic{
			TopicID:   topicID,
			Weight:    int32(weight),
			StartTime: start.UnixMilli(),
			EndTime:   end.UnixMilli(),
		}

		// 保存到数据库
		repo := repo.NewTopTopicRepo()

		// 尝试查找是否存在
		_, err = repo.FindByTopicID(context.Background(), topicID)
		if err != nil {
			// 如果不存在，则创建
			err = repo.Create(context.Background(), topTopic)
			if err != nil {
				fmt.Printf("Failed to create top topic: %v\n", err)
				return
			}
			fmt.Printf("Successfully created top topic: ID=%d, Weight=%d, Name=%s\n", topicID, weight, topic.Name)
		} else {
			// 如果存在，则更新
			update := map[string]interface{}{
				"weight":     gorm.Expr("?", int32(weight)),
				"start_time": gorm.Expr("?", start.UnixMilli()),
				"end_time":   gorm.Expr("?", end.UnixMilli()),
				"utime":      gorm.Expr("UNIX_TIMESTAMP() * 1000"),
			}
			err = repo.UpdateById(context.Background(), topicID, update)
			if err != nil {
				fmt.Printf("Failed to update top topic: %v\n", err)
				return
			}
			fmt.Printf("Successfully updated top topic: ID=%d, Weight=%d, Name=%s\n", topicID, weight, topic.Name)
		}

		// 更新缓存
		_, err = cache.NewHotTopicCache().Refresh(context.Background(), 0)
		if err != nil {
			fmt.Printf("Warning: Failed to refresh cache: %v\n", err)
			return
		}
		fmt.Println("Successfully refreshed hot topic cache")
	},
}

func init() {
	// 设置置顶话题的参数
	setTopTopicCmd.Flags().Int64("topic_id", 0, "Topic ID")
	setTopTopicCmd.Flags().Int("weight", 0, "Weight of the topic")
	setTopTopicCmd.Flags().String("start_time", "", "Start time (format: 2006-01-02 15:04:05)")
	setTopTopicCmd.Flags().String("end_time", "", "End time (format: 2006-01-02 15:04:05)")
	setTopTopicCmd.MarkFlagRequired("topic_id")
	setTopTopicCmd.MarkFlagRequired("weight")
	setTopTopicCmd.MarkFlagRequired("start_time")
	setTopTopicCmd.MarkFlagRequired("end_time")
}
