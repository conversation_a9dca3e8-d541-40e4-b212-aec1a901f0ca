package cmd

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/service"
	"app/util"

	"github.com/spf13/cobra"
)

func init() {
	root.AddCommand(checkWorkAudit)
	checkWorkAudit.Flags().StringP("file", "f", "audit_status_report.csv", "指定输出文件路径")
	checkWorkAudit.Flags().BoolP("fix", "i", false, "执行修复")
}

var checkWorkAudit = &cobra.Command{
	Use:   "check-work-audit",
	Short: "检查所有未删除作品的审核状态",
	Long:  "扫描所有未删除的作品，检查视频、封面和图片的审核状态，并输出结果到CSV文件。",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()

		// 获取输出文件路径
		csvFileName, _ := cmd.Flags().GetString("file")
		fix, _ := cmd.Flags().GetBool("fix")
		ctx := context.Background()

		log.Println("开始扫描作品审核状态...")
		startTime := time.Now()

		workRepo := repo.NewWorkRepo()
		csvFile, err := os.Create(csvFileName)
		if err != nil {
			log.Fatalf("创建CSV文件失败 %s: %v", csvFileName, err)
		}
		defer csvFile.Close()

		csvWriter := csv.NewWriter(csvFile)
		defer csvWriter.Flush()

		// 写入CSV表头
		if err := csvWriter.Write([]string{"id", "type", "video", "cover", "images", "audit_status", "reason"}); err != nil {
			log.Fatalf("写入CSV表头失败: %v", err)
		}

		const batchSize = 1000
		var lastID int64 = 0
		httpClient := &http.Client{Timeout: 10 * time.Second}

		processedCount := 0
		auditFailedCount := 0

		for {
			var works []model.L36ActionWork
			err := workRepo.NormalScope(context.Background()).
				Where("id > ? AND is_delete = ?", lastID, constant.StatusNormal). // 只查询未删除的作品
				Order("id asc").
				Limit(batchSize).
				Find(&works).Error
			if err != nil {
				log.Printf("获取作品批次失败 (lastID: %d): %v", lastID, err)
				break
			}

			if len(works) == 0 {
				log.Println("没有更多作品需要处理。")
				break
			}

			log.Printf("正在处理从ID > %d 开始的 %d 个作品...", lastID, len(works))

			for _, w := range works {

				if w.ID == 3758 {
					fmt.Println(w.AuditInfo)
				}

				var auditInfo service.AuditInfo
				err := json.Unmarshal([]byte(w.AuditInfo), &auditInfo)
				if err != nil {
					log.Printf("解析审核信息失败 (作品ID: %d): %v", w.ID, err)
					return
				}

				needUpdate := false
				reason := ""

				// 检查视频
				if w.Video != "" {
					req, err := http.NewRequest("HEAD", w.Video, nil)
					if err != nil {
						log.Printf("创建请求失败 (作品ID: %d, 视频: %s): %v", w.ID, w.Video, err)
						return
					}
					req.Header.Set("User-Agent", "audit-check-script/1.0")

					resp, err := httpClient.Do(req)
					if err != nil {
						log.Printf("检查视频失败 (作品ID: %d, 视频: %s): %v", w.ID, w.Video, err)
						return
					}
					resp.Body.Close()

					if resp.StatusCode == http.StatusNotFound {
						auditInfo.Video = int(constant.FpReviewStatusReject)
						needUpdate = true
						reason = "video_404"
					} else {
						reviewStatus, err := service.NewFPService().GetFpFileReviewStatus(ctx, w.Video)
						if err != nil {
							log.Printf("获取视频审核状态失败 (作品ID: %d, 视频: %s): %v", w.ID, w.Video, err)
							return
						}
						if reviewStatus == constant.FpReviewStatusReject {
							auditInfo.Video = int(constant.FpReviewStatusReject)
							needUpdate = true
							reason = "video_rejected"
						}
					}
				}

				// 检查封面
				if w.Cover != "" {
					req, err := http.NewRequest("HEAD", w.Cover, nil)
					if err != nil {
						log.Printf("创建请求失败 (作品ID: %d, 封面: %s): %v", w.ID, w.Cover, err)
						return
					}
					req.Header.Set("User-Agent", "audit-check-script/1.0")

					resp, err := httpClient.Do(req)
					if err != nil {
						log.Printf("检查封面失败 (作品ID: %d, 封面: %s): %v", w.ID, w.Cover, err)
						return
					}
					resp.Body.Close()

					if resp.StatusCode == http.StatusNotFound {
						auditInfo.Cover = int(constant.FpReviewStatusReject)
						needUpdate = true
						reason = "cover_404"
					} else {
						reviewStatus, err := service.NewFPService().GetFpFileReviewStatus(ctx, w.Cover)
						if err != nil {
							log.Printf("获取封面审核状态失败 (作品ID: %d, 封面: %s): %v", w.ID, w.Cover, err)
							return
						}
						if reviewStatus == constant.FpReviewStatusReject {
							auditInfo.Cover = int(constant.FpReviewStatusReject)
							needUpdate = true
							reason = "cover_rejected"
						}
					}
				}

				// 检查图片
				images := util.JsonToStringArray(w.Images)
				for i, image := range images {
					if image == "" {
						continue
					}
					if w.ID == 3758 {
						fmt.Println(image)
					}
					req, err := http.NewRequest("HEAD", image, nil)
					if err != nil {
						log.Printf("创建请求失败 (作品ID: %d, 图片: %s): %v", w.ID, image, err)
						continue
					}
					req.Header.Set("User-Agent", "audit-check-script/1.0")

					resp, err := httpClient.Do(req)
					if err != nil {
						log.Printf("检查图片失败 (作品ID: %d, 图片: %s): %v", w.ID, image, err)
						continue
					}
					resp.Body.Close()

					if resp.StatusCode == http.StatusNotFound {
						auditInfo.Images[i] = int(constant.FpReviewStatusReject)
						needUpdate = true
						reason = "image_404"
					} else {
						reviewStatus, err := service.NewFPService().GetFpFileReviewStatus(ctx, image)
						if err != nil {
							log.Printf("获取图片审核状态失败 (作品ID: %d, 图片: %s): %v", w.ID, image, err)
							continue
						}
						if reviewStatus == constant.FpReviewStatusReject {
							auditInfo.Images[i] = int(constant.FpReviewStatusReject)
							needUpdate = true
							reason = "image_rejected"
						}
					}
				}

				if needUpdate {
					// 计算新的审核状态
					newAuditStatus := service.NewWorkService().GetAuditStatusByAuditInfo(&auditInfo)

					if err := csvWriter.Write([]string{
						strconv.FormatInt(w.ID, 10),
						strconv.FormatInt(int64(w.Type), 10),
						w.Video,
						w.Cover,
						w.Images,
						strconv.FormatInt(int64(newAuditStatus), 10),
						reason,
					}); err != nil {
						log.Printf("写入CSV记录失败: %v", err)
					}
					auditFailedCount++

					if fix {
						// 更新数据库
						err = workRepo.UpdateById(ctx, w.ID, map[string]interface{}{
							"audit_status": int(newAuditStatus),
							"audit_info":   util.StructToJson(auditInfo),
						})
						if err != nil {
							log.Printf("更新作品失败 (ID: %d): %v", w.ID, err)
						}

						// 更新缓存
						_, err = cache.GetWorkCache().Update(ctx, w.ID, map[string]interface{}{
							"audit_status": int(newAuditStatus),
							"audit_info":   util.StructToJson(auditInfo),
						})
						if err != nil {
							log.Printf("更新缓存失败 (ID: %d): %v", w.ID, err)
						}

						// 更新ES
						workEsRepo := es.NewWorkEsRepo()
						err = workEsRepo.Update(ctx, int(w.ID), map[string]any{
							"audit_status": int(newAuditStatus),
						})
						if err != nil {
							log.Printf("更新ES失败 (ID: %d): %v", w.ID, err)
						}
					}
				}
			}
			lastID = works[len(works)-1].ID
			csvWriter.Flush()
		}

		duration := time.Since(startTime)
		log.Printf("审核状态检查完成，耗时: %s", duration)
		log.Printf("检查了 %d 个作品", processedCount)
		log.Printf("发现 %d 个作品审核状态异常", auditFailedCount)
		log.Printf("结果已保存到 %s", csvFileName)

		fmt.Println("作品审核状态检查完成。请查看日志和CSV文件。")
	},
}
