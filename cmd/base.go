package cmd

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"app/config"
	"app/conn"
	"app/docs"
	"app/util/faketime"
	ulog "app/util/log"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	yunyinglog "ccc-gitlab.leihuo.netease.com/pkgo/yunying-log"
	"github.com/BurntSushi/toml"
	"github.com/spf13/cobra"

	_ "app/util/validator"
)

// cfgFile 全局配置文件地址
var cfgFile string

var root = &cobra.Command{
	Use:     "app",
	Short:   "App Root Command",
	Long:    "App Root Command",
	Example: "",
	Run: func(cmd *cobra.Command, args []string) {
		bootstrap()
		serverStart()
	},
}

func init() {
	root.PersistentFlags().StringVar(&cfgFile, "config", "", "config file(default is config/config.toml)")
}

func Execute() {
	if err := root.Execute(); nil != err {
		fmt.Fprintln(os.Stderr, "执行命令错误", err)
		os.Exit(1)
	}
}

func bootstrap() {
	// 加载并解析配置
	if "" == cfgFile {
		cfgFile = "conf/config.toml"
	}
	if _, err := toml.DecodeFile(cfgFile, config.C); err != nil {
		fmt.Fprintln(os.Stderr, "Failed to decode config file", err)
		os.Exit(1)
	}
	fmt.Println("start with config file : ", cfgFile)

	if config.C.Test.TestEnv {
		faketime.Init()
	}

	// 初始化资源
	InitLog(config.C)
	InitDB(config.C)
	InitRedis(config.C)
	InitSwagger(&config.C.Server)
	InitHttpClient(config.C)
	InitYunyingLog(config.C)
	conn.GetPusher()
}

// InitLog 初始化应用的日志资源
func InitLog(conf *config.Conf) {
	log := elog.New()
	log.SetLogPath(conf.Log.LogPath)
	log.SetFileName(conf.Log.FileName)
	log.SetReserveDays(conf.Log.ReserveDays)
	log.SetReserveCount(conf.Log.ReserveCount)
	log.SetMaxSize(conf.Log.MaxSize)
	log.SetLevel(conf.Log.Level)
	log.SetGlobalFields(elog.Fields{
		"schema": conf.Log.GlobalFields.Schema,
		"app":    conf.App.AppName,
		//"body":   elog.Fields{},
	})
	log.Start()
	log.AddHook(ulog.NewPopoLoggerHook())
	elog.SetStd(log)
}

// InitDB 初始化应用的DB资源
func InitDB(conf *config.Conf) {
	conn.ConnectDB1(&conf.Mysql)
	conn.ConnectSlaveDB(&conf.SlaveMysql)
	if conf.OnlineMysql.Host != "" {
		conn.ConnectOnlineDB(&conf.OnlineMysql)
	}
}

// InitRedis 初始化应用的Redis资源
func InitRedis(conf *config.Conf) {
	conn.ConnectRedis1(&conf.Redis)
	if conf.Redis.Debug {
		go conn.RedisMonitor(&conf.Redis)
	}
}

// InitHttpClient 初始化应用的Http Client资源
func InitHttpClient(conf *config.Conf) {
	conn.InitHttpClient1(&conf.HttpClient)
}

func InitSwagger(conf *config.ServerConfig) {
	if !config.C.Test.TestEnv {
		return
	}
	if conf.Host != "" {
		docs.SwaggerInfo.Host = conf.Host
	}
	if conf.BasePath != "" {
		docs.SwaggerInfo.BasePath = conf.BasePath
	}
}

func InitYunyingLog(conf *config.Conf) {
	log := yunyinglog.New()
	log.SetLogPath(conf.Log.YunyingLogPath)
	log.SetReserveDays(conf.Log.ReserveDays)
	log.SetReserveCount(conf.Log.ReserveCount)
	log.SetMaxSize(conf.Log.MaxSize)
	log.Start()
	yunyinglog.SetStd(log)
}

func listenStop(handler func() error) {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	err := handler()
	if nil != err {
		fmt.Fprintln(os.Stderr, "处理结束错误", err)
		os.Exit(1)
	}
}
