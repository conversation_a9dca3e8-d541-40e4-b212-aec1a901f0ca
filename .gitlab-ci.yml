variables:
  REPO: "hub.fuxi.netease.com/leihuo-ccc-l36/nshm-action-station-server"
  REPO2: "hub.fuxi.netease.com/leihuo-ccc-aliyun-l36/nshm-action-station-server"
  PROJECT_ID: 1152
  CRON_PROJECT_ID: 1233

stages:
  - build

build:
  stage: build
  tags:
    - ccc-gitlab-runner
  script:
    - echo "docker username $DOCKER_USER"
    - echo "$DOCKER_PASS" | docker login --username "$DOCKER_USER" --password-stdin hub.fuxi.netease.com
    - tag="${REPO}:${CI_COMMIT_REF_NAME}.${CI_PIPELINE_ID}.${CI_COMMIT_SHORT_SHA}"
    - tag2="${REPO2}:${CI_COMMIT_REF_NAME}.${CI_PIPELINE_ID}.${CI_COMMIT_SHORT_SHA}"
    - DOCKER_BUILDKIT=1 docker image build  -t $tag .
    - DOCKER_BUILDKIT=1 docker image build  -t $tag2 .
    - docker image push $tag
    - docker image push $tag2
    - docker rmi -f $tag
    - curl -X POST -d "receiver=2258086&msg=GO框架镜像打包成功%0Atag:${tag}%0A最终提交commit地址:${CI_PROJECT_URL}/-/commit/${CI_COMMIT_SHA}%0A提交信息：$(git log -1 --pretty=%B)" "http://qa.leihuo.netease.com:3316/popo_qatool"
    - curl -X POST -d "receiver=${GITLAB_USER_EMAIL}&project_id=${PROJECT_ID}&image=$tag&msg=$(git log -1 --pretty=%B)" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"
    - curl -X POST -d "receiver=${GITLAB_USER_EMAIL}&project_id=${CRON_PROJECT_ID}&image=$tag&msg=$(git log -1 --pretty=%B)" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"


