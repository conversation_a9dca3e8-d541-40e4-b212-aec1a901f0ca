package router

import (
	"app/api"
	"app/config"
	"app/middleware"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"     // swagger embed files
	ginSwagger "github.com/swaggo/gin-swagger" // gin-swagger middleware
)

func RegisterRoute(router *gin.Engine) {
	router.Use(middleware.Cors2())

	prefix := config.C.Server.Prefix
	if prefix == "" {
		prefix = config.C.Server.BasePath
	}
	r := router.Group(prefix)

	// 注册swagger文档
	if config.C.Test.TestEnv {
		r.Use(middleware.SetGameServerTime)
		r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		r.POST("/set_fake_time", api.SetFakeTime)
	}

	// 健康检查
	r.GET("/health", api.Health)

	authRouter := r.Group("/auth")
	{
		authRouter.POST("/login", api.AuthLogin)
	}

	workRouter := r.Group("/work", middleware.CheckSkey)
	{
		workRouter.POST("/create", middleware.Lock, middleware.YidunMiddleware, api.WorkCreate)
		workRouter.POST("/update", middleware.Lock, middleware.YidunMiddleware, api.WorkUpdate)
		workRouter.POST("/del", middleware.Lock, api.WorkDel)
		workRouter.GET("/list/recommend", api.WorkListRecommend)
		workRouter.POST("/like", middleware.Lock, api.WorkLike)
		workRouter.POST("/cancel_like", middleware.Lock, api.WorkCancelLike)
		workRouter.POST("/collect", middleware.Lock, api.WorkCollect)
		workRouter.POST("/cancel_collect", middleware.Lock, api.WorkCancelCollect)
		workRouter.GET("/list/search", api.WorkListSearch)
		workRouter.GET("/detail", api.WorkDetail)
		workRouter.GET("/list/collect", api.WorkListCollect)
		workRouter.GET("/list/designer", api.WorkListDesigner)
		workRouter.POST("/complete-play", middleware.Lock, api.WorkCompletePlay)
		workRouter.POST("/share", middleware.Lock, api.WorkShare)
		workRouter.POST("/share/visit", middleware.Lock, api.WorkEffectShare)
		workRouter.POST("/use", middleware.Lock, api.WorkUse)
		workRouter.GET("/list/mine", api.WorkListMine)
		workRouter.POST("/trace", api.WorkTrace)
	}

	commentRouter := r.Group("/comment", middleware.CheckSkey)
	{
		commentRouter.POST("/add", middleware.Lock, middleware.YidunMiddleware, api.CommentAdd)
		commentRouter.POST("/del", middleware.Lock, api.CommentDel)
		commentRouter.GET("/list", api.CommentList)
		commentRouter.GET("/reply/list", api.CommentReplyList)
		commentRouter.POST("/like", middleware.Lock, api.CommentLike)
		commentRouter.POST("/cancel_like", middleware.Lock, api.CommentCancelLike)
	}

	commonRouter := r.Group("/common", middleware.CheckSkey)
	{
		commonRouter.GET("/fp_token", api.CommonFpToken)
		commonRouter.GET("/fp_pass_token", api.CommonFpPassToken)
	}

	designerRouter := r.Group("/designer", middleware.CheckSkey)
	{
		designerRouter.GET("/list/search", api.DesignerListSearch)
		designerRouter.GET("/list/follow", api.DesignerListFollow)
		designerRouter.POST("/follow", middleware.Lock, api.DesignerFollow)
		designerRouter.POST("/cancel_follow", middleware.Lock, api.DesignerCancelFollow)
		designerRouter.GET("/detail", api.DesignerDetail)
	}

	// Add topic router group
	topicRouter := r.Group("/topic", middleware.CheckSkey)
	{
		topicRouter.GET("/search", api.TopicSearch)
		topicRouter.GET("/list", api.TopicList)
		topicRouter.GET("/list/recommend", api.TopicListRecommend)
	}

	// Add new rank router group
	rankRouter := r.Group("/rank")
	{
		rankRouter.GET("/designer/exp", api.RankDesignerExp)
		rankRouter.GET("/work/hot", api.RankWorkHot)
	}

	userRouter := r.Group("/user", middleware.CheckSkey)
	{
		userRouter.GET("/avatar/list", api.UserAvatarList)
		userRouter.POST("/avatar/update", middleware.Lock, api.UserAvatarUpdate)
		userRouter.GET("/name/list", api.UserNameList)
		userRouter.POST("/update", middleware.Lock, api.UserUpdate)
	}

	dressRouter := r.Group("/dress", middleware.CheckSkey)
	{
		dressRouter.GET("/list/recommend", api.DressListRecommend)
	}

	serverRouter := r.Group("/server")
	{
		serverRouter.POST("/fp_server_callback", middleware.CheckFpToken, api.ServerFpReviewCallback)
		serverRouter.GET("/designer/detail", api.ServerDesignerDetail)
		serverRouter.POST("/padding/trigger", api.ServerPaddingTrigger)
	}

	if config.C.Test.TestEnv {
		kafkaRouter := r.Group("/kafka")
		{
			kafkaRouter.POST("/success_view_work_action", api.KafkaSuccessViewWorkAction)
		}
	}

	gmRouter := r.Group("/gm", middleware.CheckGmIp)
	{
		gmRouter.POST("/comment/list/work", api.GmCommentListWork)
		gmRouter.POST("/work/del", api.GmWorkDel)
		gmRouter.POST("/work/recover", api.GmWorkRecover)
		gmRouter.POST("/work/set_visibility", api.GmWorkSetVisibility)
		gmRouter.POST("/creator/ban", api.GmCreatorBan)
		gmRouter.POST("/work/update_name", api.GmWorkUpdateName)
		gmRouter.POST("/work/update_summary", api.GmWorkUpdateSummary)
		gmRouter.POST("/comment/del", api.GmCommentDel)
		gmRouter.POST("/comment/ban", api.GmCommentBan)
		gmRouter.POST("/work/detail", api.GmWorkDetail)
		gmRouter.POST("/work/list/role", api.GmWorkListRole)
	}

	openRouter := r.Group("/open")
	{
		openRouter.GET("/work/detail", api.OpenWorkDetail)
	}
}
