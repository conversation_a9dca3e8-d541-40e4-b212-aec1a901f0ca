-- l36_action_work
CREATE TABLE `l36_action_work` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`type` SMALLINT(6) NOT NULL COMMENT '类型',
	`name` VARCHAR(20) NOT NULL COMMENT '名称' COLLATE 'utf8mb4_general_ci',
	`summary` VARCHAR(120) NOT NULL COMMENT '描述' COLLATE 'utf8mb4_general_ci',
	`cover` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '封面' COLLATE 'utf8mb4_general_ci',
	`video` VARCHAR(100) NOT NULL COMMENT '视频' COLLATE 'utf8mb4_general_ci',
	`images` VARCHAR(1000) NOT NULL COMMENT '图片' COLLATE 'utf8mb4_general_ci',
	`property` VARCHAR(100) NOT NULL COMMENT '属性' COLLATE 'utf8mb4_general_ci',
    `raw_property` varchar(255) NOT NULL DEFAULT '' COMMENT '客户端提交的属性url',
	`contents` INT(11) NOT NULL COMMENT '内容',
	`resample_url` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '重采样地址' COLLATE 'utf8mb4_general_ci',
	`scene` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '场景',
	`gender` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '性别 0=男 1=女',
	`visibility` TINYINT(4) NOT NULL COMMENT '可见性 1=所有人可见 2=仅自己可见',
	`is_delete` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '删除状态 0=正常 1=删除',
	`user_id` BIGINT(20) NOT NULL COMMENT '用户id',
	`role_id` BIGINT(20) NOT NULL COMMENT '角色id',
	`hot` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '热度',
	`day_hot` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '日热度',
	`week_hot` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '周热度',
	`like_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '点赞数',
	`comment_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '评论数',
	`collect_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '收藏数',
	`use_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '使用数',
	`share_count` INT(11) NOT NULL DEFAULT '0' COMMENT '分享数',
	`complete_count` INT(11) NOT NULL DEFAULT '0' COMMENT '有效浏览数',
	`audit_status` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '审核状态',
	`audit_info` VARCHAR(200) NOT NULL DEFAULT '0' COMMENT '审核信息' COLLATE 'utf8mb4_general_ci',
	`last_decay_time` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '上次衰减时间',
	`yesterday_decay_hot` INT(11) NOT NULL DEFAULT '0' COMMENT '昨日衰减热度',
	`ctime` BIGINT(20) NOT NULL COMMENT '创建时间',
	`utime` BIGINT(20) NOT NULL COMMENT '更新时间',
	`fake_like_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '假点赞数',
	`fake_comment_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '假评论数',
	`fake_collect_count` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '假收藏数',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `user_id` (`user_id`) USING BTREE,
	INDEX `ctime` (`ctime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品表';

-- l36_action_user
CREATE TABLE `l36_action_user` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`aid` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '计费id' COLLATE 'utf8mb4_general_ci',
	`role_id` BIGINT(20) NOT NULL COMMENT '角色id',
	`fans` INT(11) NOT NULL DEFAULT '0' COMMENT '粉丝数',
	`name` VARCHAR(63) NOT NULL COMMENT '名称' COLLATE 'utf8mb4_general_ci',
	`avatar_role_id` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '头像角色id',
	`name_role_id` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '名称头像id',
	`exp` INT(11) NOT NULL COMMENT '经验值',
	`fans_count` INT(11) NOT NULL DEFAULT '0' COMMENT '粉丝数',
	`follow_count` INT(11) NOT NULL DEFAULT '0' COMMENT '关注数',
	`work_count` INT(11) NOT NULL DEFAULT '0' COMMENT '作品数',
	`collect_count` INT(11) NOT NULL DEFAULT '0' COMMENT '收藏数',
	`liked_count` INT(11) NOT NULL DEFAULT '0' COMMENT '被点赞数',
	`ctime` BIGINT(20) NOT NULL COMMENT '创建时间',
	`utime` BIGINT(20) NOT NULL COMMENT '更新时间',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `aid` (`aid`) USING BTREE,
	INDEX `role_id` (`role_id`) USING BTREE,
	INDEX `exp` (`exp`) USING BTREE,
	INDEX `name_role_id` (`name_role_id`) USING BTREE,
	INDEX `avatar_role_id` (`avatar_role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站用户表';


-- l36_action_designer_follow
CREATE TABLE `l36_action_designer_follow`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `designer_id` bigint NOT NULL COMMENT '设计师id',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `ctime` bigint not NULL COMMENT '创建时间',
    `utime` bigint not NULL COMMENT '更新时间',
    PRIMARY KEY(`id`),
    UNIQUE `user_designer`(`user_id`, `designer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站设计师关注表';

-- l36_action_work_like
CREATE TABLE `l36_action_work_like`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `work_id` bigint NOT NULL COMMENT '作品id',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `user_work` (`user_id`, `work_id`),
    KEY `work_id`(`work_id`),
    KEY `ctime`(`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品点赞表';

-- l36_action_work_collect
CREATE TABLE `l36_action_work_collect`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `work_id` bigint NOT NULL COMMENT '作品id',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `user_work` (`user_id`, `work_id`),
    KEY `work_id`(`work_id`),
    KEY `ctime`(`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品收藏表';


-- l36_action_work_comment
CREATE TABLE `l36_action_work_comment`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `work_id` bigint NOT NULL COMMENT '作品id',
    `origin_comment_id` bigint NOT NULL DEFAULT 0 COMMENT '原始评论id',
    `reply_comment_id` bigint NOT NULL DEFAULT 0 COMMENT '回复评论id',
    `reply_user_id` bigint NOT NULL DEFAULT 0 COMMENT '回复用户id',
    `reply_role_id` bigint NOT NULL DEFAULT 0 COMMENT '回复角色id',
    `content` varchar(100) NOT NULL COMMENT '评论内容',
    `reply_count` int NOT NULL DEFAULT 0 COMMENT '回复数',
    `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `is_add_hot` tinyint NOT NULL DEFAULT 0 COMMENT '是否加热度 0=否 1=是',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `work_id`(`work_id`),
    KEY `origin_comment_id`(`origin_comment_id`),
    KEY `ctime`(`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品评论表';

-- l36_action_work_comment_like
CREATE TABLE `l36_action_work_comment_like`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `comment_id` bigint NOT NULL COMMENT '评论id',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `user_comment` (`user_id`, `comment_id`),
    KEY `comment_id`(`comment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品评论点赞表';

-- l36_action_review_media
CREATE TABLE `l36_action_review_media`(
    `id` bigint AUTO_INCREMENT,
    `work_id` bigint NOT NULL COMMENT '作品id',
    `file_id` varchar(50) NOT NULL COMMENT '文件ID',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    `audit_status` tinyint NOT NULL COMMENT '审核状态',
    PRIMARY KEY (`id`),
    KEY `work_id`(`work_id`),
    UNIQUE `file_id`(`file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站审核媒介表';

-- l36_action_topic
CREATE TABLE `l36_action_topic`(
    `id` bigint AUTO_INCREMENT,
    `name` varchar(10) NOT NULL COMMENT '名称',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `hot` int NOT NULL DEFAULT 0 COMMENT '热度',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `name`(`name`),
    KEY `hot`(`hot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站话题表';

-- l36_action_work_topic
CREATE TABLE `l36_action_work_topic`(
    `id` bigint AUTO_INCREMENT,
    `work_id` bigint NOT NULL COMMENT '作品id',
    `topic_id` bigint NOT NULL COMMENT '话题id',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `work_topic`(`work_id`, `topic_id`),
    KEY `topic_id`(`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品话题表';

-- l36_action_work_use
CREATE TABLE `l36_action_work_use`(
    `id` bigint AUTO_INCREMENT,
    `work_id` bigint NOT NULL COMMENT '作品id',
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY (`work_id`, `user_id`),
    KEY (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品使用表';

-- l36_action_work_play 按月分表 保留近两个月数据
CREATE TABLE `l36_action_work_play`(
    `id` bigint AUTO_INCREMENT,
    `work_id` bigint NOT NULL COMMENT '作品id',
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `date` bigint NOT NULL COMMENT '日期',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `user_id_work_id`(`user_id`, `work_id`),
    KEY `ctime`(`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品播放表';

-- l36_action_work_share
CREATE TABLE `l36_action_work_share`(
    `id` bigint AUTO_INCREMENT,
    `work_id` bigint NOT NULL COMMENT '作品id',
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',
    `is_effect` tinyint NOT NULL DEFAULT 0 COMMENT '是否有效 0=无效 1=有效',
    `effect_time` bigint not null DEFAULT 0 COMMENT '生效时间',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY (`work_id`, `user_id`),
    KEY (`effect_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品分享记录表';

-- l36_action_user_designer_exp_limit, 按 user_id 分表
CREATE TABLE `l36_action_user_designer_exp_limit`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `designer_id`  bigint NOT NULL COMMENT '设计师id',
    `work_count` int NOT NULL DEFAULT 0 COMMENT '作品数',
    `comment_count` int NOT NULL DEFAULT 0 COMMENT '评论数',
    `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
    `collect_count` int NOT NULL DEFAULT 0 COMMENT '收藏数',
    `share_count` int NOT NULL DEFAULT 0 COMMENT '分享数',
    `use_count` int NOT NULL DEFAULT 0 COMMENT '使用数',
    `complete_count` int NOT NULL DEFAULT 0 COMMENT '有效浏览数',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `user_id_designer_id`(`user_id`, `designer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站用户设计师经验限制表';

-- l36_action_work_delete
CREATE TABLE `l36_action_work_delete`(
    `id` bigint AUTO_INCREMENT,
    `work_id` bigint NOT NULL COMMENT '作品id',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `work_id`(`work_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站作品删除表';

-- l36_action_recommend_topic
CREATE TABLE `l36_action_recommend_topic`(
    `id` bigint AUTO_INCREMENT,
    `topic_id` bigint NOT NULL COMMENT '话题id',
    `weight` int NOT NULL DEFAULT 0 COMMENT '权重',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `topic_id`(`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站推荐话题表';

-- l36_action_top_topic
CREATE TABLE `l36_action_top_topic`(
    `id` bigint AUTO_INCREMENT,
    `topic_id` bigint NOT NULL COMMENT '话题id',
    `weight` int NOT NULL DEFAULT 0 COMMENT '权重',
    `start_time` bigint NOT NULL COMMENT '开始时间',
    `end_time` bigint NOT NULL COMMENT '结束时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 0=正常 1=删除',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `topic_id`(`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站置顶话题表';

-- l36_action_gm_creator_ban
CREATE TABLE `l36_action_gm_creator_ban`(
    `id` bigint AUTO_INCREMENT,
    `user_id` bigint NOT NULL COMMENT '用户id',
    `role_id` bigint NOT NULL COMMENT '角色id',    
    `ban_type` tinyint NOT NULL COMMENT '禁言类型',
    `expire_time` bigint not null COMMENT '过期时间',
    `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除',
    `ctime` bigint not null COMMENT '创建时间',
    `utime` bigint not null COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE `user_ban_type`(`user_id`, `ban_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动作站GM创作者禁言表';
