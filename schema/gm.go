package schema

type GmWorkDelReq struct {
	WorkBase
}
type GmWorkRecoverReq struct {
	WorkBase
}
type GmWorkSetVisibilityReq struct {
	WorkBase
	Visibility int `json:"visibility" form:"visibility" binding:"required,oneof=1 2"`
}
type GmCreatorBanReq struct {
	RoleBase
	// 过期时间. 单位: 秒
	ExpireTime int64 `json:"expire_time" form:"expire_time" example:"3600" binding:"required"`
}
type GmWorkUpdateNameReq struct {
	WorkBase
	Name string `json:"name" form:"name" binding:"required,max=20"`
}
type GmWorkUpdateSummaryReq struct {
	WorkBase
	Summary string `json:"summary" form:"summary" binding:"required,max=120"`
}
type GmCommentDelReq struct {
	CommentBase
}
type GmCommentBanReq struct {
	RoleBase
	// 过期时间. 单位: 秒
	ExpireTime int64 `json:"expire_time" form:"expire_time" example:"3600" binding:"required"`
}
type GmWorkDetailReq struct {
	WorkBase
}
type GmWorkItem struct {
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
	// 作品名称
	Name string `json:"name" example:"作品名称"`
	// 作品描述 #xx#表示标签
	Summary string `json:"Summary" example:"作品描述"`
	// 点赞数
	LikeCount int `json:"like_count" example:"1"`
	// 可见性 1=所有人可见 2=仅自己可见
	Visibility int `json:"visibility" example:"1"`
	// 视频
	Video string `json:"video" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 图片, 和视频二选一
	Images []string `json:"images" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
}
type GmWorkDetailRes struct {
	GmWorkItem
}
type GmWorkListRoleReq struct {
	RoleBase
}
type GmWorkListRoleRes struct {
	List []GmWorkItem `json:"list"`
}
type GmCommentListWorkReq struct {
	WorkBase
}
type GmCommentItem struct {
	// 评论ID
	ID int64 `json:"id" example:"1"`
	// 评论内容
	Content string `json:"content" example:"评论内容"`
	// 角色ID
	RoleID int64 `json:"role_id" example:"1"`
	// 原始评论ID
	OriginCommentID int64 `json:"origin_comment_id" example:"1"`
}

type GmCommentListWorkRes struct {
	List []GmCommentItem `json:"list"`
}
