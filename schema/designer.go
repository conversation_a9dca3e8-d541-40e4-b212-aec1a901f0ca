package schema

type DesignerListSearchReq struct {
	UserBase
	Pagination
	// 关键字
	Keyword string `json:"keyword" form:"keyword"  binding:"omitempty" example:"关键字"`
}

type DesignerItem struct {
	DesignerInfo
	// 作品数
	WorkCount int `json:"work_count" example:"1"`
	// 粉丝数
	FansCount int `json:"fans_count" example:"1"`
	// 被点赞数
	LikedCount int `json:"liked_count" example:"1"`
	// 设计师等级
	DesignerLevel int `json:"designer_level" example:"1"`
	// 热门作品
	Works []WorkItem `json:"works"`
}

type DesignerListSearchRes struct {
	// 设计师列表
	List []DesignerItem `json:"list"`
}

type DesignerBase struct {
	// 设计师ID
	DesignerId int64 `json:"designer_id" form:"designer_id" binding:"required" default:"1"`
}

type DesignerFollowReq struct {
	UserBase
	DesignerBase
}

type DesignerCancelFollowReq struct {
	UserBase
	DesignerBase
}

type DesignerDetailReq struct {
	UserBase
	DesignerBase
}

type DesignerDetailRes struct {
	DesignerInfo
	// 作品数
	WorkCount int `json:"work_count" example:"1"`
	// 粉丝数
	FansCount int `json:"fans_count" example:"1"`
	// 被点赞数
	LikedCount int `json:"liked_count" example:"1"`
	// 关注数
	FollowCount int `json:"follow_count" example:"1"`
	// 设计师等级
	DesignerLevel int `json:"designer_level" example:"1"`
	// 经验值
	Exp int `json:"exp" example:"1"`
	// 下一等级经验值
	ExpThreshold int `json:"exp_threshold" example:"1"`
}

type DesignerListFollowReq struct {
	UserBase
	Pagination
}

type DesignerListFollowRes struct {
	// 设计师列表
	List []DesignerInfo `json:"list"`
}
