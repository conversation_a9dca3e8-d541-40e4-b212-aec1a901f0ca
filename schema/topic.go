package schema

type TopicSearchReq struct {
	UserBase
	Pagination
	// 关键字
	Keyword string `json:"keyword" form:"keyword" binding:"required" example:"关键字"`
}

type TopicItem struct {
	// 话题ID
	ID int64 `json:"id" example:"1"`
	// 话题名称
	Name string `json:"name" example:"话题名称"`
}

type TopicSearchRes struct {
	// 话题列表
	List []TopicItem `json:"list"`
}

type TopicListReq struct {
	UserBase
	Pagination
}

type TopicListRes struct {
	// 话题列表
	List []TopicItem `json:"list"`
}

type TopicListRecommendReq struct {
	UserBase
}
type TopicListRecommendRes struct {
	// 话题列表
	List []TopicItem `json:"list"`
}
