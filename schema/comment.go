package schema

type CommentBase struct {
	// 评论ID
	CommentId int64 `json:"comment_id" form:"comment_id" binding:"required" example:"1"`
}

type CommentAddReq struct {
	UserBase
	TraceOptBase
	// 作品ID
	WorkId int64 `json:"work_id" binding:"required" example:"1"`
	// 评论内容
	Content string `json:"content" binding:"required,max=100" example:"评论内容"`
	// 回复评论ID
	ReplyCommentId int64 `json:"reply_comment_id" binding:"omitempty,min=0" example:"1"`
}

type CommentAddRes struct {
	// 评论ID
	CommentItem
}

type CommentDelReq struct {
	UserBase
	CommentBase
}

type CommentDelRes struct {
	// 评论ID
	CommentId int64 `json:"comment_id" example:"1"`
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
}

type CommentListReq struct {
	UserBase
	Pagination
	WorkBase
	// 排序方式
	Sort string `json:"sort" form:"sort" binding:"reply_sort" example:"new"`
}

type CommentItem struct {
	// 评论ID
	ID int64 `json:"id" example:"1"`
	// 评论内容
	Content string `json:"content" example:"评论内容"`
	// 评论时间
	Ctime int64 `json:"ctime" example:"1"`
	// 点赞数
	LikeCount int `json:"like_count" example:"1"`
	// 是否已点赞
	IsLiked bool `json:"is_liked" example:"true"`
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
	// 原评论ID
	OriginCommentId int64 `json:"origin_comment_id" example:"1"`
	// 回复评论ID
	ReplyCommentId int64 `json:"reply_comment_id" example:"1"`
	// 回复数
	ReplyCount int `json:"reply_count" example:"1"`
	// 评论人信息
	User *UserInfo `json:"user"`
	// 回复列表，默认返回2个, 只对一级评论有效
	ReplyList []CommentItem `json:"reply_list"`
	// 回复人，只对二级评论有效
	ReplyUser *UserInfo `json:"reply_user"`
	// 评论人RoleId
	RoleId int64 `json:"role_id" example:"1"`
}

type CommentListRes struct {
	// 评论列表
	List []CommentItem `json:"list"`
}

type CommentReplyListReq struct {
	UserBase
	Pagination
	WorkBase
	CommentBase
}

type CommentReplyListRes struct {
	// 回复列表
	List []CommentItem `json:"list"`
}

type CommentLikeReq struct {
	UserBase
	CommentBase
}

type CommentCancelLikeReq struct {
	UserBase
	CommentBase
}
