package schema

type DressListRecommendReq struct {
	UserBase
	Pagination
	WorkBase
}

type DressInfo struct {
	FashionInfo
	// 穿搭数据
	RelatedFashionWorks []FashionInfo `json:"related_fashion_works"`
}

type FashionInfo struct {
	// 装扮站作品ID
	ID int64 `json:"id" example:"1"`
	// 装扮站作品名称
	Name string `json:"name" example:"装扮站作品名称"`
	// 类别
	Type int `json:"type" example:"1"`
	// 子类别
	SubType int `json:"subtype" example:"1"`
	// 时装ID
	FashionID int `json:"fashion_id" example:"1"`
	// 封面图
	Cover string `json:"cover" example:"封面图"`
	// 属性文件
	Property string `json:"property" example:"属性文件"`
	// 作者ID
	RoleId int64 `json:"role_id" example:"1"`
	// 作者名称
	RoleName string `json:"role_name" example:"作者名称"`
}

type DressListRecommendRes struct {
	// 服饰列表
	List []DressInfo `json:"list"`
}
