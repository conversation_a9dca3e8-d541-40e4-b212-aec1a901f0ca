package schema

type UserInfo struct {
	// 用户ID
	ID int64 `json:"id" example:"1"`
	// 用户昵称
	Name string `json:"name" example:"昵称"`
	// 用户头像
	Avatar AvatarInfo `json:"avatar"`
	// 服务器ID
	ServerID int32 `json:"server_id" example:"1"`
}

type UserAvatarListReq struct {
	UserBase
}

type UserAvatarInfo struct {
	AvatarInfo
	// 是否是默认头像
	IsDefault bool `json:"is_default" example:"true"`
}

type UserAvatarListRes struct {
	List []UserAvatarInfo `json:"list"`
}

type UserAvatarUpdateReq struct {
	UserBase
	// 头像角色ID
	AvatarRoleId int64 `json:"avatar_role_id" form:"avatar_role_id" binding:"required" example:"1"`
}

type UserUpdateReq struct {
	UserBase
	// 昵称
	NameRoleId int64 `json:"name_role_id" form:"name_role_id" binding:"required" example:"1"`
}

type UserNameListReq struct {
	UserBase
}

type UserNameInfo struct {
	RoleID    int64  `json:"role_id" example:"1"`
	RoleName  string `json:"role_name" example:"角色名称"`
	IsDefault bool   `json:"is_default" example:"true"`
}

type UserNameListRes struct {
	List []UserNameInfo `json:"list"`
}
