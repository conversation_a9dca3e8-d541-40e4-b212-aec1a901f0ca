package schema

type WorkBase struct {
	// 作品ID
	WorkId int64 `json:"work_id" form:"work_id" binding:"required" example:"1"`
}

type WorkCreateReq struct {
	UserBase
	// 类型 1=模型 2=动作视频 3=纯视频
	Type int `json:"type" from:"type" binding:"required,oneof=1 2 3" example:"1"`
	// 作品名称
	Name string `json:"name" form:"name" binding:"required,max=14" example:"作品名称"`
	// 作品描述 #xx#表示标签
	Summary string `json:"summary" form:"summary" binding:"required,max=120" example:"作品描述"`
	// 封面
	Cover string `json:"cover" form:"cover" binding:"required,fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 视频，type=2时必填
	Video string `json:"video" from:"video" binding:"fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 图片, 和视频二选一
	Images []string `json:"images" form:"images" binding:"omitempty,dive,fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 内容 1=表情 2=动作 3=语音 4=外观 5=镜头
	Contents []int `json:"contents"  form:"contents" binding:"omitempty,dive,min=0,max=5" example:"1"`
	// 场景
	Scene int64 `json:"scene" form:"scene" binding:"required" example:"1"`
	// 上传人性别 0=男 1=女
	Gender int `json:"gender" form:"gender" binding:"oneof=0 1" example:"0"`
	// 可见性 1=所有人可见 2=仅自己可见
	Visibility int `json:"visibility" form:"gender"  binding:"required,oneof=1 2" example:"1"`
	// 属性文件Url
	Property string `json:"property" form:"property" binding:"fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 重采样Url
	ResampleUrl string `json:"resample_url" form:"resample_url" binding:"fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
}

type WorkCreateRes struct {
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
}

type WorkUpdateReq struct {
	UserBase
	WorkBase
	// 类型
	Type int `json:"type" binding:"required,oneof=1 2 3" example:"1"`
	// 场景
	Scene int64 `json:"scene" binding:"required" example:"1"`
	// 作品名称
	Name string `json:"name" binding:"required,max=20" example:"作品名称"`
	// 作品描述 #xx#表示标签
	Summary string `json:"Summary" binding:"required,max=120" example:"作品描述"`
	// 可见性 1=所有人可见 2=仅自己可见
	Visibility int `json:"visibility" binding:"required,oneof=1 2" example:"1"`
	// 封面
	Cover string `json:"cover" binding:"omitempty,fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 图片, 和视频二选一
	Images []string `json:"images" binding:"omitempty,dive,fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 视频
	Video string `json:"video" binding:"omitempty,fp_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
}

type WorkUpdateRes struct {
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
}

type WorkDelReq struct {
	UserBase
	WorkBase
}

type WorkListRecommendReq struct {
	UserBase
	Pagination
	// 类型
	Type int `json:"type" form:"type" binding:"required,oneof=1 2 3" example:"1"`
	// 跟踪id,用户单次请求生成的唯一id，由role_id+时间戳+三位随机整数组成
	TraceId string `json:"trace_id" form:"trace_id"  binding:"required" example:"trace_id"`
	// 链路追踪，每次打开推荐瀑布流是相同的sessionid，同一次打开瀑布流下的多次请求时是不同的traceid，生成方式role_id+时间戳
	SessionId string `json:"session_id" form:"session_id" binding:"required" example:"session_id"`
}

type AvatarInfo struct {
	// 头像角色ID
	RoleId int64 `json:"role_id" example:"1"`
	// 职业
	Job int8 `json:"job" example:"1"`
	// 性别
	Gender int8 `json:"gender" example:"1"`
	// 头像ID
	Avatar int32 `json:"avatar" example:"1"`
	// 头像框
	AvatarFrame int32 `json:"avatar_frame" example:"1"`
}

type DesignerInfo struct {
	// 设计师ID
	ID int64 `json:"id" example:"1"`
	// 设计师名称
	Name string `json:"name" example:"设计师名称"`
	// 和名字对应的角色ID
	RoleId int64 `json:"role_id" example:"1"`
	// 设计师头像
	Avatar AvatarInfo `json:"avatar"`
	// 是否已经关注
	IsFollow bool `json:"is_follow" example:"true"`
}

type WorkItem struct {
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
	// 作品名称
	Name string `json:"name" example:"作品名称"`
	// 作品描述 #xx#表示标签
	Summary string `json:"Summary" example:"作品描述"`
	// 作品类型 1=模型 2=视频
	Type int `json:"type" example:"1"`
	// 场景
	Scene int64 `json:"scene" example:"1"`
	// 封面
	Cover string `json:"cover" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 视频
	Video string `json:"video" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 图片, 和视频二选一
	Images []string `json:"images" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 属性文件Url
	Property string `json:"property" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 设计师
	Designer DesignerInfo `json:"designer"`
	// 是否已点赞
	IsLiked bool `json:"is_liked" example:"true"`
	// 点赞数
	LikeCount int `json:"like_count" example:"1"`
	// 是否已收藏
	IsCollected bool `json:"is_collected" example:"true"`
	// 收藏数
	CollectCount int `json:"collect_count" example:"1"`
	// 评论数
	CommentCount int `json:"comment_count" example:"1"`
	// 内容 1=表情 2=动作 3=语音 4=外观 5=镜头
	Contents []int `json:"contents" binding:"required,dive,min=1,max=5" example:"1"`
	// 热度
	Hot int `json:"hot" example:"1"`
	// TraceId
	TraceId string `json:"trace_id" example:"trace_id"`
	// Scm 实验组字段
	Scm string `json:"scm,omitempty" example:"scm"`
	// 可见性 1=所有人可见 2=仅自己可见
	Visibility int `json:"visibility" example:"1"`
	// 重采样Url
	ResampleUrl string `json:"resample_url" example:"https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"`
	// 上传时的角色ID
	RoleId int64 `json:"role_id" example:"1"`
	// 审核状态 0=审核通过 2=审核中 3=审核失败
	AuditStatus int `json:"audit_status"`
}

type WorkListRes struct {
	// 作品列表
	List []WorkItem `json:"list"`
}

type WorkLikeReq struct {
	UserBase
	WorkBase
	TraceOptBase
}

type WorkCancelLikeReq struct {
	UserBase
	WorkBase
}

type WorkCollectReq struct {
	UserBase
	WorkBase
	TraceOptBase
}

type WorkCancelCollectReq struct {
	UserBase
	WorkBase
}

type WorkListSearchReq struct {
	UserBase
	WorkFilter
	Pagination
	Topic string `json:"topic" form:"topic"  binding:"omitempty" example:"话题"`
}

type TraceBase struct {
	// 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
	SessionId string `json:"session_id" form:"session_id" binding:"omitempty"`
	// 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
	TraceId string `json:"trace_id" form:"trace_id" binding:"omitempty"`
}

type TraceOptBase struct {
	TraceBase
	// 埋点ID,实验组字段
	Scm string `json:"scm" form:"scm" binding:"omitempty"`
}

type WorkDetailReq struct {
	UserBase
	WorkBase
	TraceOptBase
}

type WorkDetailRes struct {
	WorkItem
}

type WorkListMineReq struct {
	UserBase
	Pagination
	WorkFilter
}

type WorkListCollectReq struct {
	UserBase
	Pagination
	WorkFilter
}

type WorkListDesignerReq struct {
	UserBase
	Pagination
	WorkFilter
	DesignerBase
}

type WorkFilter struct {
	// Keyword
	Keyword string `json:"keyword" form:"keyword"  binding:"omitempty" example:"关键词"`
	// 动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔
	Contents string `json:"contents" form:"contents" binding:"omitempty" example:"1,2,3"`
	// 性别 0=不限 1=男 2=女
	Gender int `json:"gender" form:"gender"  binding:"omitempty,oneof=0 1 2" example:"0"`
	// 排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数
	Sort string `json:"sort" form:"sort"  binding:"omitempty,oneof=hot new like collect comment" example:"hot"`
	// 顺序
	Order string `json:"order" form:"order"  binding:"work_order" example:"asc"`
}

func (f *WorkFilter) IsEmpty() bool {
	if f.Contents != "" {
		return false
	}
	if f.Gender != 0 {
		return false
	}
	if f.Keyword != "" {
		return false
	}
	return true
}

type WorkTraceReq struct {
	UserBase
	TraceOptBase
	WorkBase
	// 行为类型 tip=打赏 report=举报 remake=拍同款 tab=切换tab search=搜索
	BhvType string `json:"bhv_type" form:"bhv_type" binding:"required,oneof=tip report remake tab search" example:"1"`
}

type WorkUseReq struct {
	UserBase
	WorkBase
	TraceOptBase
}

type WorkCompletePlayReq struct {
	UserBase
	WorkBase
	TraceOptBase
}

type WorkEffectShareReq struct {
	UserBase
	WorkBase
	// 分享ID
	ShareId int64 `json:"share_id" form:"share_id" binding:"required" example:"1"`
}

type WorkShareReq struct {
	UserBase
	WorkBase
}

type WorkShareRes struct {
	// 分享ID
	ShareId int64 `json:"share_id" example:"1"`
}
