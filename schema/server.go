package schema

import "app/constant"

type FpReviewCallbackFile struct {
	// 文件id
	FileId string `json:"file_id" binding:"required"`
	// 原始状态
	FromStatus constant.FpReviewStatus `json:"from_status" binding:"required,oneof=0 1 2 3"`
	// 当前状态
	ToStatus constant.FpReviewStatus `json:"to_status" binding:"required,oneof=0 1 2 3"`
}

type ServerFpReviewCallbackReq struct {
	// 项目名称
	ProjectName string `json:"project_name" binding:"required"`
	// 操作者
	Operator string `json:"operator" binding:"required"`
	// 文件列表
	Files []FpReviewCallbackFile `json:"files" binding:"required"`
}

type ServerDesignerDetailReq struct {
	RoleBase
}

type ServerDesignerDetailRes struct {
	DesignerInfo
	// 作品数
	WorkCount int `json:"work_count" example:"1"`
	// 粉丝数
	FansCount int `json:"fans_count" example:"1"`
	// 被点赞数
	LikedCount int `json:"liked_count" example:"1"`
	// 关注数
	FollowCount int `json:"follow_count" example:"1"`
	// 设计师等级
	DesignerLevel int `json:"designer_level" example:"1"`
	// 经验值
	Exp int `json:"exp" example:"1"`
	// 下一等级经验值
	ExpThreshold int `json:"exp_threshold" example:"1"`
}

type PaddingItem struct {
	OpenStatus       int32 `json:"openStatus"`       // 0: 关闭 1: 开启
	AlarmThreshold   int32 `json:"alarmThreshold"`   // 报警阈值
	ReleaseHour      int32 `json:"releaseHour"`      // 释放时间
	MinRealThreshold int32 `json:"minRealThreshold"` // 最小真实阈值
	CdMinute         int32 `json:"cdMinute"`         // 数据增长cd, 每x分钟增长一次
	GteMinRatio      int32 `json:"gteMinRatio"`      // 最小增长比例
	GteMaxRatio      int32 `json:"gteMaxRatio"`      // 最大增长比例
	GtePercent       int32 `json:"gtePercent"`       // 最高增长百分比(注水上限)
	GteThresholdsMin int32 `json:"gteThresholdsMin"` // 注水阈值下限
	GteThresholdsMax int32 `json:"gteThresholdsMax"` // 注水阈值上限
}

type ServerPaddingTriggerReq struct {
	LikeCfg    PaddingItem `json:"likeCfg"`
	CommentCfg PaddingItem `json:"commentCfg"`
	CollectCfg PaddingItem `json:"collectCfg"`
}

type PaddingResItem struct {
	AllCount         int32  `json:"allCount"`         // 总数量
	RealCount        int32  `json:"realCount"`        // 真实数量
	PaddingCount     int32  `json:"paddingCount"`     // 注水数量
	WorkName         string `json:"workName"`         // 作品名称
	WorkId           int64  `json:"workId"`           // 作品id
	WorkCreateTime   int64  `json:"workCreateTime"`   // 作品创建时间
	GetThresholdsMin int32  `json:"getThresholdsMin"` // 注水阈值下限
	GetThresholdsMax int32  `json:"getThresholdsMax"` // 注水阈值上限
}

type ServerPaddingTriggerRes struct {
	LikeData    PaddingResItem `json:"likeData"`
	CommentData PaddingResItem `json:"commentData"`
	CollectData PaddingResItem `json:"collectData"`
}
