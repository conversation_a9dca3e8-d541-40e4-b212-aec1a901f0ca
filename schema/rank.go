package schema

type RankDesignerExpReq struct {
	RoleBase
}

type RankDesignerItem struct {
	RoleId        int64  `json:"role_id"`
	RoleName      string `json:"role_name"`
	Job           int    `json:"job"`
	Gender        int    `json:"gender"`
	Level         int    `json:"level"`
	Rank          int    `json:"rank"`
	Exp           int64  `json:"exp"`
	DesignerLevel int    `json:"designer_level"`
}

type RankDesignerExpRes struct {
	List []RankDesignerItem `json:"list"`
	Self *RankDesignerItem  `json:"self"`
}

type RankWorkHotReq struct {
	RoleId int64 `form:"role_id" binding:"required"`
}

type RankWorkItem struct {
	WorkId   int64        `json:"work_id"`
	Name     string       `json:"name"`
	Summary  string       `json:"summary"`
	Video    string       `json:"video"`
	Images   []string     `json:"images"`
	Hot      int          `json:"hot"`
	Rank     int          `json:"rank"`
	Designer DesignerInfo `json:"designer"`
}

type RankWorkHotRes struct {
	List []RankWorkItem `json:"list"`
}
