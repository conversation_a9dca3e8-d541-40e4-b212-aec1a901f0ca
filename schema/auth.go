package schema

type AuthLoginReq struct {
	RoleBase
	// AID 账号aid
	Aid string `json:"aid" binding:"required" example:"1"`
	// 时间戳,单位秒
	Time int64 `json:"time" binding:"required" example:"1712041848"`
	// token,计算方式md5(aid+role_id+time+secret_key)
	Token string `json:"token" binding:"required" example:"token"`
}

type AuthLoginResData struct {
	// 调用API凭证 skey
	Skey string `json:"skey" example:"skey"`
	// 用户ID
	UserId int64 `json:"user_id" example:"1"`
}
