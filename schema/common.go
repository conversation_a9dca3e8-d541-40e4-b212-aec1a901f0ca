package schema

type CommonFpTokenReq struct {
	UserBase
}

type CommonFpTokenRes struct {
	// 过期时间
	Expires int64 `json:"expires" example:"1634025600"`
	// 项目
	Project string `json:"project" example:"l36-action"`
	// 授权Token
	Token string `json:"token" example:"token"`
	// 上传地址
	UploadUrl string `json:"upload_url" example:"https://l36-action.fp.ps.netease.com/file/new"`
}

type CommonFpPassTokenReq struct {
	UserBase
}

type CommonFpPassTokenRes struct {
	// 过期时间
	Expires int64 `json:"expires" example:"1634025600"`
	// 项目
	Project string `json:"project" example:"l36-action"`
	// 授权Token
	Token string `json:"token" example:"token"`
	// 上传地址
	UploadUrl string `json:"upload_url" example:"https://l36-action.fp.ps.netease.com/file/new"`
}
