package schema

type Response struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

type CommonResData struct {
	IsOk bool `json:"is_ok" example:"true"`
}

type Pagination struct {
	Page     int `json:"page" form:"page,default=1" binding:"required,min=1" default:"1"`                    // 当前页
	PageSize int `json:"page_size" form:"page_size,default=10" binding:"required,min=1,max=20" default:"10"` // 每页个数
}

type RoleBase struct {
	// 角色Id
	RoleId int64 `json:"role_id" form:"role_id" binding:"required" default:"1"`
}

type SetFakeTimeReq struct {
	// 伪造时间 支持 +1h, -1h, +1d, -1d, +1w, -1w, +1M, -1M, +1y, -1y,也支持时间 "2006-01-02 15:04:05"
	FakeTime string `json:"fake_time" binding:"required" example:"2024-11-01 00:00:00"` // 伪造时间
}

type SearchBase struct {
	// 关键字
	Keyword string `json:"keyword" form:"keyword" binding:"omitempty" example:"关键字"`
}

type UserBase struct {
	// 用户ID
	UserId int64 `json:"user_id" form:"user_id" binding:"required" default:"1"`
	// 角色Id
	RoleId int64 `json:"role_id" form:"role_id" binding:"required" default:"1"`
}

type WorkCounter struct {
	// 数量
	Count int `json:"count" example:"1"`
	// 作品ID
	WorkId int64 `json:"work_id" example:"1"`
}
