package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// CommonFpToken
// @Summary 获取上传fp文件token，用于上传图片等需要审核的文件
// @Tags common
// @Accept json
// @Produce json
// @Param req query schema.CommonFpTokenReq true "请求参数"
// @Security skeyAuth
// @Success 200 {object} schema.Response{data=schema.CommonFpTokenRes} "成功"
// @Router /common/fp_token [get]
func CommonFpToken(ctx *gin.Context) {
	var req schema.CommonFpTokenReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commonService := service.NewCommonService()
	res, err := commonService.CommonFpToken(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// CommonFpPassToken
// @Summary 获取上传fp文件token，用于上传property等不需要审核的文件
// @Tags common
// @Accept json
// @Produce json
// @Param req query schema.CommonFpPassTokenReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonFpPassTokenRes} "成功"
// @Security skeyAuth
// @Router /common/fp_pass_token [get]
func CommonFpPassToken(ctx *gin.Context) {
	var req schema.CommonFpPassTokenReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commonService := service.NewCommonService()
	res, err := commonService.CommonFpPassToken(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
