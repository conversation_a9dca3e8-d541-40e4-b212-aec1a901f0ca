package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// OpenWorkDetail
// @Summary 获取作品详情(无需登录)
// @Tags open
// @Accept json
// @Produce json
// @Param req query schema.OpenWorkDetailReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.OpenWorkDetailRes} "成功"
// @Router /open/work/detail [get]
func OpenWorkDetail(ctx *gin.Context) {
	var req schema.OpenWorkDetailReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	openService := service.NewOpenService()
	res, err := openService.OpenWorkDetail(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
