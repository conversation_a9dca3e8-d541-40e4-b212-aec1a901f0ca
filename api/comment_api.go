package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// CommentAdd
// @Summary 添加评论
// @Tags comment
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.CommentAddReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommentAddRes} "成功"
// @Router /comment/add [post]
func CommentAdd(ctx *gin.Context) {
	var req schema.CommentAddReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commentService := service.NewCommentService()
	res, err := commentService.CommentAdd(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// CommentDel
// @Summary 删除评论
// @Tags comment
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.CommentDelReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommentDelRes} "成功"
// @Router /comment/del [post]
func CommentDel(ctx *gin.Context) {
	var req schema.CommentDelReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commentService := service.NewCommentService()
	res, err := commentService.CommentDel(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// CommentList
// @Summary 获取作品评论列表
// @Tags comment
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.CommentListReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommentListRes} "成功"
// @Router /comment/list [get]
func CommentList(ctx *gin.Context) {
	var req schema.CommentListReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commentService := service.NewCommentService()
	res, err := commentService.CommentList(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// CommentReplyList
// @Summary 获取作品回复列表
// @Tags comment
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.CommentReplyListReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommentReplyListRes} "成功"
// @Router /comment/reply/list [get]
func CommentReplyList(ctx *gin.Context) {
	var req schema.CommentReplyListReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commentService := service.NewCommentService()
	res, err := commentService.CommentReplyList(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// CommentLike
// @Summary 点赞评论
// @Tags comment
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.CommentLikeReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /comment/like [post]
func CommentLike(ctx *gin.Context) {
	var req schema.CommentLikeReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commentService := service.NewCommentService()
	err := commentService.CommentLike(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// CommentCancelLike
// @Summary 取消点赞评论
// @Tags comment
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.CommentCancelLikeReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /comment/cancel_like [post]
func CommentCancelLike(ctx *gin.Context) {
	var req schema.CommentCancelLikeReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	commentService := service.NewCommentService()
	err := commentService.CommentCancelLike(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}
