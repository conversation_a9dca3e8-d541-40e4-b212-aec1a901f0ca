package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// GmWorkDel
// @Summary 删除剧本站作品
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkDelReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/del [post]
func GmWorkDel(ctx *gin.Context) {
	var req schema.GmWorkDelReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmWorkDel(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmWorkRecover
// @Summary 回复剧本站作品
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkRecoverReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/recover [post]
func GmWorkRecover(ctx *gin.Context) {
	var req schema.GmWorkRecoverReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmWorkRecover(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmWorkSetVisibility
// @Summary 设置剧组站作品可见范围
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkSetVisibilityReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/set_visibility [post]
func GmWorkSetVisibility(ctx *gin.Context) {
	var req schema.GmWorkSetVisibilityReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmWorkSetVisibility(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmCreatorBan
// @Summary 禁止上传剧组站作品
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmCreatorBanReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/creator/ban [post]
func GmCreatorBan(ctx *gin.Context) {
	var req schema.GmCreatorBanReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmCreatorBan(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmWorkUpdateName
// @Summary 修改剧组站作品名
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkUpdateNameReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/update_name [post]
func GmWorkUpdateName(ctx *gin.Context) {
	var req schema.GmWorkUpdateNameReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmWorkUpdateName(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmWorkUpdateSummary
// @Summary 修改剧组站作品描述
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkUpdateSummaryReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/update_summary [post]
func GmWorkUpdateSummary(ctx *gin.Context) {
	var req schema.GmWorkUpdateSummaryReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmWorkUpdateSummary(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmCommentDel
// @Summary 删除剧组站评论
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmCommentDelReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/comment/del [post]
func GmCommentDel(ctx *gin.Context) {
	var req schema.GmCommentDelReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmCommentDel(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmCommentBan
// @Summary 禁止剧组站评论
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmCommentBanReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/comment/ban [post]
func GmCommentBan(ctx *gin.Context) {
	var req schema.GmCommentBanReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	err := gmService.GmCommentBan(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// GmWorkDetail
// @Summary 根据作品id查询作品情况
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkDetailReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/detail [post]
func GmWorkDetail(ctx *gin.Context) {
	var req schema.GmWorkDetailReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	res, err := gmService.GmWorkDetail(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// GmWorkListRole
// @Summary 根据角色id查询作品清空
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmWorkListRoleReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /gm/work/list/role [post]
func GmWorkListRole(ctx *gin.Context) {
	var req schema.GmWorkListRoleReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	res, err := gmService.GmWorkListRole(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// GmCommentListWork
// @Summary 根据作品id查询评论内容
// @Tags gm
// @Accept json
// @Produce json
// @Param req body schema.GmCommentListWorkReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.GmCommentListWorkRes} "成功"
// @Router /gm/comment/list/work [post]
func GmCommentListWork(ctx *gin.Context) {
	var req schema.GmCommentListWorkReq
	if err := util.BindForGm(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	gmService := service.NewGmService()
	res, err := gmService.GmCommentListWork(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
