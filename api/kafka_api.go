package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// KafkaSuccessViewWorkAction
// @Summary 有效浏览
// @Tags kafka
// @Accept json
// @Produce json
// @Param req body schema.KafkaSuccessViewWorkActionReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /kafka/success_view_work_action [post]
func KafkaSuccessViewWorkAction(ctx *gin.Context) {
	var req schema.KafkaSuccessViewWorkActionReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	kafkaService := service.NewKafkaService()
	err := kafkaService.KafkaSuccessViewWorkAction(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}
