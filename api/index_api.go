package api

import (
	"os"
	"time"

	"app/config"
	"app/schema"
	"app/util"
	"app/util/faketime"

	"github.com/gin-gonic/gin"
)

// Health godoc
//
//	@Summary		健康检查
//	@Description	健康检查
//	@Tags			health
//	@Produce		json
//	@Success		200	{object}	schema.Response{data=schema.CommonResData}
//	@Router			/health [get]
func Health(ctx *gin.Context) {
	result := map[string]any{
		"is_ok":        true,
		"current_time": time.UnixMilli(util.GetGameServerTime(ctx)).Format(time.DateTime),
	}
	if config.C.Test.TestEnv {
		result["fake_time"] = os.Getenv("FAKETIME")
		result["patch_time"] = os.Getenv("PATCHTIME")
	}
	JsonReturn(ctx, result, nil)
}

// SetFakeTime godoc
//
//	@Summary		设置假时间
//	@Description	设置假时间
//	@Tags			health
//	@Accept			json
//	@Produce		json
//	@Param			req	body schema.SetFakeTimeReq	true	"请求参数"
//	@Success		200	{object}	schema.Response{data=schema.CommonResData}
//	@Router			/set_fake_time [post]
func SetFakeTime(ctx *gin.Context) {
	var req schema.SetFakeTimeReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	faketime.SetFakeTime(req.FakeTime)
	JsonReturnSuccess(ctx, map[string]any{
		"req_fake_time": req.FakeTime,
		"fake_time":     os.Getenv("FAKETIME"),
		"current_time":  time.Now().Format("2006-01-02 15:04:05"),
		"patch_time":    os.Getenv("PATCHTIME"),
	})
}
