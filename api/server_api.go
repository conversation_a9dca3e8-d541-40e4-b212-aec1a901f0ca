package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// ServerFpReviewCallback
// @Summary FP回调
// @Tags server
// @Accept json
// @Security fpAuth
// @Produce json
// @Param req body schema.ServerFpReviewCallbackReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /server/fp_server_callback [post]
func ServerFpReviewCallback(ctx *gin.Context) {
	var req schema.ServerFpReviewCallbackReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	serverService := service.NewServerService()
	err := serverService.ServerFpReviewCallback(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// ServerDesignerDetail
// @Summary 获取设计师详情
// @Tags server
// @Accept json
// @Produce json
// @Param req query schema.ServerDesignerDetailReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.ServerDesignerDetailRes} "成功"
// @Router /server/designer/detail [get]
func ServerDesignerDetail(ctx *gin.Context) {
	var req schema.ServerDesignerDetailReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	serverService := service.NewServerService()
	res, err := serverService.ServerDesignerDetail(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// ServerPaddingTrigger
// @Summary 触发注水
// @Tags server
// @Accept json
// @Produce json
// @Param req body schema.ServerPaddingTriggerReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.ServerPaddingTriggerRes} "成功"
// @Router /server/padding/trigger [post]
func ServerPaddingTrigger(ctx *gin.Context) {
	var req schema.ServerPaddingTriggerReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	serverService := service.NewServerService()
	res, err := serverService.ServerPaddingTrigger(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
