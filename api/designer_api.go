package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// DesignerListSearch
// @Summary 搜索作者
// @Tags designer
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.DesignerListSearchReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.DesignerListSearchRes} "成功"
// @Router /designer/list/search [get]
func DesignerListSearch(ctx *gin.Context) {
	var req schema.DesignerListSearchReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	designerService := service.NewDesignerService()
	res, err := designerService.DesignerListSearch(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// DesignerFollow
// @Summary 关注设计师
// @Tags designer
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.DesignerFollowReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /designer/follow [post]
func DesignerFollow(ctx *gin.Context) {
	var req schema.DesignerFollowReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	designerService := service.NewDesignerService()
	err := designerService.DesignerFollow(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// DesignerCancelFollow
// @Summary 取消关注设计师
// @Tags designer
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.DesignerCancelFollowReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /designer/cancel_follow [post]
func DesignerCancelFollow(ctx *gin.Context) {
	var req schema.DesignerCancelFollowReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	designerService := service.NewDesignerService()
	err := designerService.DesignerCancelFollow(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// DesignerDetail
// @Summary 获取设计师详细信息
// @Tags designer
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.DesignerDetailReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.DesignerDetailRes} "成功"
// @Router /designer/detail [get]
func DesignerDetail(ctx *gin.Context) {
	var req schema.DesignerDetailReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	designerService := service.NewDesignerService()
	res, err := designerService.DesignerDetail(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// DesignerListFollow
// @Summary 获取关注的设计师列表
// @Tags designer
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.DesignerListFollowReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.DesignerListFollowRes} "成功"
// @Router /designer/list/follow [get]
func DesignerListFollow(ctx *gin.Context) {
	var req schema.DesignerListFollowReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	designerService := service.NewDesignerService()
	res, err := designerService.DesignerListFollow(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
