package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// TopicSearch
// @Summary 搜索话题
// @Tags topic
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.TopicSearchReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.TopicSearchRes} "成功"
// @Router /topic/search [get]
func TopicSearch(ctx *gin.Context) {
	var req schema.TopicSearchReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	topicService := service.NewTopicService()
	res, err := topicService.TopicSearch(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// TopicList
// @Summary 热门话题列表
// @Tags topic
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.TopicListReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.TopicListRes} "成功"
// @Router /topic/list [get]
func TopicList(ctx *gin.Context) {
	var req schema.TopicListReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	topicService := service.NewTopicService()
	res, err := topicService.TopicList(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// TopicListRecommend
// @Summary 获取推荐的话题列表
// @Tags topic
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.TopicListRecommendReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.TopicListRecommendRes} "成功"
// @Router /topic/list/recommend [get]
func TopicListRecommend(ctx *gin.Context) {
	var req schema.TopicListRecommendReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	topicService := service.NewTopicService()
	res, err := topicService.TopicListRecommend(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
