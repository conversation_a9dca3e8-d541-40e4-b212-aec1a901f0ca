package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// DressListRecommend
// @Summary 获取推荐穿搭
// @Tags dress
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.DressListRecommendReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.DressListRecommendRes} "成功"
// @Router /dress/list/recommend [get]
func DressListRecommend(ctx *gin.Context) {
	var req schema.DressListRecommendReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	DressService := service.NewDressService()
	res, err := DressService.DressListRecommend(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
