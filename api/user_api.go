package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// UserAvatarList
// @Summary 玩家头像列表
// @Tags user
// @Accept json
// @Produce json
// @Security skeyAuth
// @Param req query schema.UserAvatarListReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.UserAvatarListRes} "成功"
// @Router /user/avatar/list [get]
func UserAvatarList(ctx *gin.Context) {
	var req schema.UserAvatarListReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	userService := service.NewUserService()
	res, err := userService.UserAvatarList(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// UserAvatarUpdate
// @Summary 更新玩家头像
// @Tags user
// @Accept json
// @Produce json
// @Security skeyAuth
// @Param req body schema.UserAvatarUpdateReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /user/avatar/update [post]
func UserAvatarUpdate(ctx *gin.Context) {
	var req schema.UserAvatarUpdateReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	userService := service.NewUserService()
	err := userService.UserAvatarUpdate(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// UserUpdate
// @Summary 更新玩家信息
// @Tags user
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.UserUpdateReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /user/update [post]
func UserUpdate(ctx *gin.Context) {
	var req schema.UserUpdateReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	userService := service.NewUserService()
	err := userService.UserUpdate(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// UserNameList
// @Summary 角色名称列表
// @Tags user
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.UserNameListReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.UserNameListRes} "成功"
// @Router /user/name/list [get]
func UserNameList(ctx *gin.Context) {
	var req schema.UserNameListReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	userService := service.NewUserService()
	res, err := userService.UserNameList(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
