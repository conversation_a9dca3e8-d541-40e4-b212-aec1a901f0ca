package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// RankDesignerExp
// @Summary 设计师经验排行榜
// @Tags rank
// @Accept json
// @Produce json
// @Param req query schema.RankDesignerExpReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.RankDesignerExpRes} "成功"
// @Router /rank/designer/exp [get]
func RankDesignerExp(ctx *gin.Context) {
	var req schema.RankDesignerExpReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	rankService := service.NewRankService()
	res, err := rankService.RankDesignerExp(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// RankWorkHot
// @Summary 作品热度排行榜
// @Tags rank
// @Accept json
// @Produce json
// @Param req query schema.RankWorkHotReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.RankWorkHotRes} "成功"
// @Router /rank/work/hot [get]
func RankWorkHot(ctx *gin.Context) {
	var req schema.RankWorkHotReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	rankService := service.NewRankService()
	res, err := rankService.RankWorkHot(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
