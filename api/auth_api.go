package api

import (
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// AuthLogin 获取API凭证skey(游戏服务端调用下发给客户端使用)
// @Router /auth/login [post]
// @Summary 获取API凭证skey
// @Description 获取API凭证skey
// @Tags auth
// @Accept json
// @Produce json
// @Param req body schema.AuthLoginReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.AuthLoginResData} "成功"
func AuthLogin(ctx *gin.Context) {
	req := &schema.AuthLoginReq{}
	err := util.Bind(ctx, req)
	if err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	skeyService := service.NewSkeyService()
	isTokenValid := skeyService.CheckSkeyToken(req.Aid, req.RoleId, req.Time, req.Token)
	if !isTokenValid {
		JsonReturnError(ctx, errors.New(errors.TokenError))
		return
	}
	user, err := repo.NewUserRepo().SyncUser(ctx, req.Aid, req.RoleId)
	if err != nil {
		JsonReturnError(ctx, errors.New(errors.ServerError))
		return
	}
	skey := skeyService.GenerateSkey(user.ID, req.RoleId, req.Time)
	JsonReturnSuccess(ctx, schema.AuthLoginResData{
		Skey:   skey,
		UserId: user.ID,
	})
}
