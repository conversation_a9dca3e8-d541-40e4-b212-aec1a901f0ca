package api

import (
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

// WorkCreate
// @Summary 上传作品
// @Tags work
// @Accept json
// @Produce json
// @Param req body schema.WorkCreateReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkCreateRes} "成功"
// @Security skeyAuth
// @Router /work/create [post]
func WorkCreate(ctx *gin.Context) {
	var req schema.WorkCreateReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkCreate(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkUpdate
// @Summary 更新作品
// @Tags work
// @Accept json
// @Produce json
// @Param req body schema.WorkUpdateReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkUpdateRes} "成功"
// @Security skeyAuth
// @Router /work/update [post]
func WorkUpdate(ctx *gin.Context) {
	var req schema.WorkUpdateReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkUpdate(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkDel
// @Summary 删除作品
// @Tags work
// @Accept json
// @Produce json
// @Param req body schema.WorkDelReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Security skeyAuth
// @Router /work/del [post]
func WorkDel(ctx *gin.Context) {
	var req schema.WorkDelReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkDel(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkListRecommend
// @Summary 推荐作品列表
// @Tags work
// @Accept json
// @Produce json
// @Param req query schema.WorkListRecommendReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkListRes} "成功"
// @Security skeyAuth
// @Router /work/list/recommend [get]
func WorkListRecommend(ctx *gin.Context) {
	var req schema.WorkListRecommendReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkListRecommend(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkLike
// @Summary 点赞
// @Tags work
// @Accept json
// @Produce json
// @Param req body schema.WorkLikeReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Security skeyAuth
// @Router /work/like [post]
func WorkLike(ctx *gin.Context) {
	var req schema.WorkLikeReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkLike(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkCancelLike
// @Summary 取消点赞
// @Tags work
// @Accept json
// @Produce json
// @Param req body schema.WorkCancelLikeReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Security skeyAuth
// @Router /work/cancel_like [post]
func WorkCancelLike(ctx *gin.Context) {
	var req schema.WorkCancelLikeReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkCancelLike(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkCollect
// @Summary 收藏
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.WorkCollectReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /work/collect [post]
func WorkCollect(ctx *gin.Context) {
	var req schema.WorkCollectReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkCollect(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkCancelCollect
// @Summary 取消收藏
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.WorkCancelCollectReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /work/cancel_collect [post]
func WorkCancelCollect(ctx *gin.Context) {
	var req schema.WorkCancelCollectReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkCancelCollect(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkListSearch
// @Summary 搜索作品
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.WorkListSearchReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkListRes} "成功"
// @Router /work/list/search [get]
func WorkListSearch(ctx *gin.Context) {
	var req schema.WorkListSearchReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkListSearch(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkDetail
// @Summary 获取作品详情
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.WorkDetailReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkDetailRes} "成功"
// @Router /work/detail [get]
func WorkDetail(ctx *gin.Context) {
	var req schema.WorkDetailReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkDetail(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkListMine
// @Summary 获取本人作品列表
// @Tags work
// @Accept json
// @Produce json
// @Security skeyAuth
// @Param req query schema.WorkListMineReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkListRes} "成功"
// @Router /work/list/mine [get]
func WorkListMine(ctx *gin.Context) {
	var req schema.WorkListMineReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkListMine(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkListCollect
// @Summary 获取收藏作品列表
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.WorkListCollectReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkListRes} "成功"
// @Router /work/list/collect [get]
func WorkListCollect(ctx *gin.Context) {
	var req schema.WorkListCollectReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkListCollect(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkListDesigner
// @Summary 获取设计师作品列表
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req query schema.WorkListDesignerReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkListRes} "成功"
// @Router /work/list/designer [get]
func WorkListDesigner(ctx *gin.Context) {
	var req schema.WorkListDesignerReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkListDesigner(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}

// WorkTrace
// @Summary 推送埋点日志
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.WorkTraceReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /work/trace [post]
func WorkTrace(ctx *gin.Context) {
	var req schema.WorkTraceReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkTrace(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkUse
// @Summary 应用作品
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.WorkUseReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /work/use [post]
func WorkUse(ctx *gin.Context) {
	var req schema.WorkUseReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkUse(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkCompletePlay
// @Summary 有效浏览(废弃，通过日志同步）
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Deprecated 2025-04-21
// @Param req body schema.WorkCompletePlayReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /work/complete-play [post]
func WorkCompletePlay(ctx *gin.Context) {
	var req schema.WorkCompletePlayReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkCompletePlay(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkEffectShare
// @Summary 有效分享
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.WorkEffectShareReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.CommonResData} "成功"
// @Router /work/share/visit [post]
func WorkEffectShare(ctx *gin.Context) {
	var req schema.WorkEffectShareReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	err := workService.WorkEffectShare(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnCommonSuccess(ctx)
}

// WorkShare
// @Summary 获取分享ID
// @Tags work
// @Accept json
// @Security skeyAuth
// @Produce json
// @Param req body schema.WorkShareReq true "请求参数"
// @Success 200 {object} schema.Response{data=schema.WorkShareRes} "成功"
// @Router /work/share [post]
func WorkShare(ctx *gin.Context) {
	var req schema.WorkShareReq
	if err := util.Bind(ctx, &req); err != nil {
		JsonReturnValidateError(ctx, err)
		return
	}
	workService := service.NewWorkService()
	res, err := workService.WorkShare(ctx, &req)
	if err != nil {
		JsonReturnError(ctx, err)
		return
	}
	JsonReturnSuccess(ctx, res)
}
