package api

import (
	"fmt"
	"strings"

	"app/config"
	"app/errors"
	"app/schema"
	"app/util"
	"app/util/log"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator"
)

// Response 通用标准响应
type Response struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

func NewResponse() *Response {
	return &Response{
		Code:    0,
		Message: "success",
	}
}

func JsonReturn(c *gin.Context, d interface{}, err *errors.Err) {
	res := NewResponse()
	res.Code, res.Message = resolveErr(c, err)
	res.Data = d
	c.JSON(200, res)
}

func JsonReturnError(c *gin.Context, err *errors.Err) {
	res := NewResponse()
	log.LogWithContext(c).WithField("err", err).Warn("JsonReturnError")
	res.Code, res.Message = resolveErr(c, err)
	c.<PERSON>(200, res)
}

func getFieldErrorMessage(err validator.FieldError) string {
	var sb strings.Builder
	field := err.Field()
	if field == "ID" {
		field = "id"
	} else {
		field = util.LowercaseFirstLetter(field)
		field = util.CamelToSnake(field)
	}
	sb.WriteString("validation failed on field '" + field + "'")
	sb.WriteString(", condition: " + err.ActualTag())

	// Print condition parameters, e.g. oneof=red blue -> { red blue }
	if err.Param() != "" {
		sb.WriteString(" { " + err.Param() + " }")
	}

	if err.Value() != nil && err.Value() != "" {
		sb.WriteString(fmt.Sprintf(", actual: %v", err.Value()))
	}
	return sb.String()
}

func getFormErrorMessage(err error) string {
	Message := err.Error()
	index := strings.Index(Message, ":")
	if index == -1 {
		return Message
	}
	return "validation failed by type error, " + Message[index+2:]
}

func JsonReturnValidateError(c *gin.Context, err error) {
	res := NewResponse()
	res.Code = errors.InvalidParams
	res.Message = errors.GetMessage(errors.InvalidParams)
	if config.C.Test.TestEnv {
		res.Message = err.Error()
		if errs, ok := err.(validator.ValidationErrors); ok {
			for _, e := range errs {
				res.Message = getFieldErrorMessage(e)
				break
			}
		} else {
			res.Message = getFormErrorMessage(err)
		}
	}
	c.JSON(200, res)
}

func JsonReturnSuccess(c *gin.Context, d interface{}) {
	res := NewResponse()
	res.Data = d
	c.JSON(200, res)
}

func JsonReturnCommonSuccess(c *gin.Context) {
	res := NewResponse()
	res.Code = errors.OK
	res.Message = errors.GetMessage(errors.OK)
	res.Data = schema.CommonResData{
		IsOk: true,
	}
	c.JSON(200, res)
}

func resolveErr(c *gin.Context, err *errors.Err) (int64, string) {
	var (
		code    int64
		Message string
	)
	if err == nil {
		code = errors.OK
		Message = errors.GetMessage(errors.OK)
	} else {
		code = err.ErrCode
		Message = err.ErrMessage
		params := util.GetParamsFromContext(c)
		if code == errors.ServerError {
			log.LogError(c, err, params).WithField("url", c.Request.URL.Path).Error("server error")
		}
	}
	return code, Message
}
