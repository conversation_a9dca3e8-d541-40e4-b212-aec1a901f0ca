package service

import (
	"context"
	"sync"

	"app/config"
	"app/conn"
	"app/util/log"

	envsdk_tools "ccc-gitlab.leihuo.netease.com/pkgo/envsdk-tools"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
)

var (
	Envsdk     *envsdk_tools.EnvsdkClient
	envsdkOnce sync.Once
)

func getEnvsdkClient() *envsdk_tools.EnvsdkClient {
	envsdkOnce.Do(func() {
		options := envsdk_tools.NewInitOptionsBuilder().Host(config.C.EnvSdk.Host).Timeout(config.C.EnvSdk.Timeout).HttpClient(conn.GetClient()).Build()
		Envsdk = envsdk_tools.NewEnvsdkClient(&options)
	})
	return Envsdk
}

type EnvsdkService struct {
	envsdk *envsdk_tools.EnvsdkClient
}

func NewEnvsdkService(envsdk ...*envsdk_tools.EnvsdkClient) *EnvsdkService {
	client := getEnvsdkClient()
	if len(envsdk) > 0 {
		client = envsdk[0]
	}
	return &EnvsdkService{
		envsdk: client,
	}
}

// ReviewWords 敏感词审核
func (s *EnvsdkService) ReviewWords(ctx context.Context, word string) bool {
	if config.C.EnvSdk.Disable {
		return true
	}
	ret, err := s.envsdk.ReviewWords(ctx, word, config.C.EnvSdk.Channel, config.C.EnvSdk.Level)
	if err != nil {
		log.LogError(ctx, err, elog.Fields{
			"word": word,
		}).Error("review words error")
		return false
	}
	return ret.IsPass
}

// ReviewNickname 昵称审核
func (s *EnvsdkService) ReviewNickname(ctx context.Context, nickname string) bool {
	if config.C.EnvSdk.Disable {
		return true
	}
	ret, err := s.envsdk.ReviewNickname(ctx, nickname)
	if err != nil {
		log.LogError(ctx, err, elog.Fields{
			"nickname": nickname,
		}).Error("review nickname error")
		return false
	}
	return ret.IsPass
}
