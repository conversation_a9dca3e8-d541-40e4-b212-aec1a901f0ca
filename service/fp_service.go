package service

import (
	"context"
	"regexp"
	"strings"
	"sync"

	"app/config"
	"app/conn"
	"app/constant"

	filepicker_tools "ccc-gitlab.leihuo.netease.com/pkgo/filepicker-tools"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
)

var (
	fpClient *filepicker_tools.FPClient
	fpOnce   sync.Once
)

func getFPClient() *filepicker_tools.FPClient {
	conf := config.C
	builder := filepicker_tools.NewInitOptionBuilder(conf.Filepicker.Project, conf.Filepicker.SecretKey, conf.Filepicker.DevMode)
	if conf.Filepicker.Policy != nil {
		builder.WithPolicy(&filepicker_tools.PolicyInitOptions{
			FsizeLimit: conf.Filepicker.Policy.FsizeLimit,
			MimeLimit:  conf.Filepicker.Policy.MimeLimit,
		})
	}
	builder.WithTokenExpire(conf.Filepicker.TokenExpire).WithRegion(filepicker_tools.Region(conf.Filepicker.Region)).WithReview((filepicker_tools.ReviewStatus)(conf.Filepicker.Review))
	options := builder.Build()
	fpOnce.Do(func() {
		fpClient = filepicker_tools.NewFPClient(*options)
	})
	return fpClient
}

type FPService struct {
	fp *filepicker_tools.FPClient
}

func NewFPService(fp ...*filepicker_tools.FPClient) *FPService {
	client := getFPClient()
	if len(fp) > 0 {
		client = fp[0]
	}
	return &FPService{
		fp: client,
	}
}

// IsFpFile 是否是Fp服务的文件
func (s *FPService) IsFpFile(url string) bool {
	if config.C.Filepicker.BasePath == "" {
		return true
	}
	re := regexp.MustCompile(`https?:\/\/l36.*\.fp\.ps\.netease\.com\/file\/\w+`)
	return re.MatchString(url)
}

// GetFpFileId 获取文件的id
func (s *FPService) GetFpFileId(url string) string {
	lastSlash := strings.LastIndex(url, "/")
	if lastSlash == -1 {
		return url
	}
	return url[lastSlash+1:]
}

type FileReviewStatusRet struct {
	ReviewStatus constant.FpReviewStatus `json:"review_status"`
}

// GetFpFileReviewStatus 获取文件的审核状态
func (s *FPService) GetFpFileReviewStatus(ctx context.Context, url string) (constant.FpReviewStatus, error) {
	if !s.IsFpFile(url) {
		return constant.FpReviewStatusPass, nil
	}
	reviewPath := url + "/review_status"
	var ret FileReviewStatusRet
	r, err := kit.HttpGet2(ctx, &ret, reviewPath, conn.GetClient())
	if r != nil && r.StatusCode == 404 {
		return constant.FpReviewStatusReject, nil
	}
	if err != nil {
		return constant.FpReviewStatusReviewing, err
	}
	if r.StatusCode != 200 {
		return constant.FpReviewStatusReject, nil
	}
	return ret.ReviewStatus, nil
}

// GetFpUrlByFileId 通过fileId获取url
func (s *FPService) GetFpUrlByFileId(fileId string) string {
	return config.C.Filepicker.BasePath + "/" + fileId
}

func (s *FPService) IsReviewingStatus(status constant.FpReviewStatus) bool {
	return status == constant.FpReviewStatusReviewing || status == constant.FpReviewStatusPrePass
}

func (s *FPService) UploadContent(ctx context.Context, content string, uid string) (string, error) {
	client := getFPClient()
	uploadUrl, err := client.UploadBytes(ctx, []byte(content), &filepicker_tools.ExtraPolicy{
		UID: &uid,
	})
	if err != nil {
		return "", err
	}
	return uploadUrl.URL, nil
}

func (s *FPService) IsFuxiFpUrl(url string) bool {
	// 判断是 fp-url， 且包含 l36-fuxi
	if !s.IsFpFile(url) {
		return false
	}
	return strings.Contains(url, "l36-fuxi")
}

func (s *FPService) IsActionFpUrl(url string) bool {
	// 判断是 fp-url， 且包含 l36-action
	if !s.IsFpFile(url) {
		return false
	}
	return strings.Contains(url, "l36-action")
}

func (s *FPService) DelFile(ctx context.Context, url string) error {
	_, err := s.fp.DelFile(ctx, url, nil)
	return err
}

func (s *FPService) CloneFile(ctx context.Context, url string, policy *filepicker_tools.ExtraPolicy) (string, error) {
	cloneUrl, err := s.fp.CloneFile(ctx, url, policy)
	return cloneUrl.URL, err
}

// UploadPassContent 上传文件，并设置审核状态为已通过
func (s *FPService) UploadPassContent(ctx context.Context, content string, uid string) (string, error) {
	client := getFPClient()
	review := filepicker_tools.ReviewStatusPassed
	uploadUrl, err := client.UploadBytes(ctx, []byte(content), &filepicker_tools.ExtraPolicy{
		UID:    &uid,
		Review: &review,
	})
	if err != nil {
		return "", err
	}
	return uploadUrl.URL, nil
}
