package service

import (
	"app/errors"
	"app/schema"

	"github.com/gin-gonic/gin"
)

type KafkaService struct{}

func NewKafkaService() *KafkaService {
	return &KafkaService{}
}

func (s *KafkaService) KafkaSuccessViewWorkAction(ctx *gin.Context, params *schema.KafkaSuccessViewWorkActionReq) *errors.Err {
	return NewWorkService().OnWorkSuccessView(ctx, &schema.WorkCompletePlayReq{
		UserBase: schema.UserBase{
			UserId: params.UserID,
			RoleId: params.RoleID,
		},
		WorkBase: schema.WorkBase{
			WorkId: params.WorkID,
		},
		TraceOptBase: schema.TraceOptBase{
			TraceBase: schema.TraceBase{
				TraceId: params.TraceId,
			},
			Scm: params.Scm,
		},
	})
}
