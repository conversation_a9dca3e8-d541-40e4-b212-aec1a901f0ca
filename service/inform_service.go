package service

import (
	"context"

	"app/external"
)

type InformService struct{}

func NewInformService() *InformService {
	return &InformService{}
}

// formatComment 格式化评论内容
func (s *InformService) formatComment(content string) string {
	if len(content) > 50 {
		return content[:47] + "..."
	}
	return content
}

// AddWorkCollectInform 添加作品收藏通知
func (s *InformService) AddWorkCollectInform(ctx context.Context, userRoleID int64, workRoleID int64, workID int64, workName string, collectID int64) error {
	if workRoleID <= 0 {
		return nil
	}

	params := &external.InformAddParams{
		TargetID:       workRoleID,
		ObjectID:       collectID,
		ResourceID:     workID,
		ResourceType:   external.ActionResourceType,
		ImgList:        []string{},
		Type:           external.InformTypeActionCollect,
		ActionWorkName: workName,
	}

	return external.SendInformAdd(ctx, userRoleID, params)
}

// AddWorkCommentInform 添加作品评论通知
func (s *InformService) AddWorkCommentInform(ctx context.Context, userRoleID int64, workRoleID int64, workID int64, workName string, commentID int64, commentText string) error {
	if workRoleID <= 0 {
		return nil
	}

	params := &external.InformAddParams{
		TargetID:       workRoleID,
		ObjectID:       commentID,
		ResourceID:     workID,
		ResourceType:   external.ActionResourceType,
		Text:           s.formatComment(commentText),
		ImgList:        []string{},
		Type:           external.InformTypeActionComment,
		ActionWorkName: workName,
	}

	return external.SendInformAdd(ctx, userRoleID, params)
}

// AddWorkReplyInform 添加作品评论回复通知
func (s *InformService) AddWorkReplyInform(ctx context.Context, userRoleID int64, replyRoleID int64, workID int64, workName string, commentID int64, replyContent string, originalComment string) error {
	if replyRoleID <= 0 {
		return nil
	}

	params := &external.InformAddParams{
		TargetID:       replyRoleID,
		ObjectID:       commentID,
		ResourceID:     workID,
		ResourceType:   external.ActionResourceType,
		Text:           s.formatComment(replyContent),
		ImgList:        []string{},
		Type:           external.InformTypeActionCommentReply,
		ActionWorkName: workName,
		OtherInfo: &external.InformOtherInfo{
			OriginText: s.formatComment(originalComment),
		},
	}

	return external.SendInformAdd(ctx, userRoleID, params)
}

// DelWorkCollectInform 删除作品收藏通知
func (s *InformService) DelWorkCollectInform(ctx context.Context, workRoleID int64, workID int64, collectID int64) error {
	if workRoleID <= 0 {
		return nil
	}

	informType := external.InformTypeActionCollect
	params := &external.InformDelParams{
		ObjectID:     &collectID,
		ResourceID:   workID,
		ResourceType: external.ActionResourceType,
		Type:         &informType,
	}

	return external.SendInformDel(ctx, workRoleID, params)
}

// DelWorkCommentInform 删除作品评论通知
func (s *InformService) DelWorkCommentInform(ctx context.Context, workRoleID int64, workID int64, commentID int64) error {
	if workRoleID <= 0 {
		return nil
	}

	informType := external.InformTypeActionComment
	params := &external.InformDelParams{
		ObjectID:     &commentID,
		ResourceID:   workID,
		ResourceType: external.ActionResourceType,
		Type:         &informType,
	}

	return external.SendInformDel(ctx, workRoleID, params)
}

// DelWorkInform 删除作品相关所有通知
func (s *InformService) DelWorkInform(ctx context.Context, workRoleID int64, workID int64) error {
	if workRoleID == 0 {
		return nil
	}

	params := &external.InformDelParams{
		ResourceID:   workID,
		ResourceType: external.ActionResourceType,
	}

	return external.SendInformDel(ctx, workRoleID, params)
}

// DelWorkReplyInform 删除作品评论回复通知
func (s *InformService) DelWorkReplyInform(ctx context.Context, workRoleID int64, workID int64, commentID int64) error {
	if workRoleID == 0 {
		return nil
	}

	informType := external.InformTypeActionCommentReply
	params := &external.InformDelParams{
		ObjectID:     &commentID,
		ResourceID:   workID,
		ResourceType: external.ActionResourceType,
		Type:         &informType,
	}

	return external.SendInformDel(ctx, workRoleID, params)
}

// AddWorkCommentLikeInform 添加评论点赞通知
func (s *InformService) AddWorkCommentLikeInform(ctx context.Context, userRoleID int64, commentRoleID int64, workID int64, workName string, likeID int64, commentContent string) error {
	if commentRoleID == 0 {
		return nil
	}

	params := &external.InformAddParams{
		TargetID:       commentRoleID,
		ObjectID:       likeID,
		ResourceID:     workID,
		ResourceType:   external.ActionResourceType,
		ImgList:        []string{},
		Type:           external.InformTypeActionCommentLike,
		ActionWorkName: workName,
		OtherInfo: &external.InformOtherInfo{
			OriginText: s.formatComment(commentContent),
		},
	}

	return external.SendInformAdd(ctx, userRoleID, params)
}

// AddWorkLikeInform 添加作品点赞通知
func (s *InformService) AddWorkLikeInform(ctx context.Context, userRoleID int64, workRoleID int64, workID int64, workName string, likeID int64) error {
	if workRoleID == 0 || userRoleID == 0 || likeID == 0 {
		return nil
	}

	params := &external.InformAddParams{
		TargetID:       workRoleID,
		ObjectID:       likeID,
		ResourceID:     workID,
		ResourceType:   external.ActionResourceType,
		ImgList:        []string{},
		Type:           external.InformTypeActionLike,
		ActionWorkName: workName,
	}

	return external.SendInformAdd(ctx, userRoleID, params)
}

// AddWorkShareInform 添加作品分享通知
func (s *InformService) AddWorkShareInform(ctx context.Context, userRoleID int64, workRoleID int64, workID int64, workName string) error {
	if workRoleID == 0 || userRoleID == 0 {
		return nil
	}

	params := &external.InformAddParams{
		TargetID:       workRoleID,
		ObjectID:       workID,
		ResourceID:     workID,
		ResourceType:   external.ActionResourceType,
		ImgList:        []string{},
		Type:           external.InformTypeActionShare,
		ActionWorkName: workName,
	}

	return external.SendInformAdd(ctx, userRoleID, params)
}

// DelWorkCommentLikeInform 删除评论点赞通知
func (s *InformService) DelWorkCommentLikeInform(ctx context.Context, workRoleID int64, workID int64, likeID int64) error {
	if workRoleID == 0 {
		return nil
	}

	informType := external.InformTypeActionCommentLike
	params := &external.InformDelParams{
		ObjectID:     &likeID,
		ResourceID:   workID,
		ResourceType: external.ActionResourceType,
		Type:         &informType,
	}

	return external.SendInformDel(ctx, workRoleID, params)
}

// DelWorkLikeInform 删除作品点赞通知
func (s *InformService) DelWorkLikeInform(ctx context.Context, workRoleID int64, workID int64, likeID int64) error {
	if workRoleID == 0 {
		return nil
	}

	informType := external.InformTypeActionLike
	params := &external.InformDelParams{
		ResourceID:   workID,
		ResourceType: external.ActionResourceType,
		Type:         &informType,
		ObjectID:     &likeID,
	}

	return external.SendInformDel(ctx, workRoleID, params)
}
