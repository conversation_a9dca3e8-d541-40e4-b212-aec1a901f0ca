package service

import (
	"context"

	"app/config"
	"app/dao/cache"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util"
	"app/util/log"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type DesignerService struct{}

func NewDesignerService() *DesignerService {
	return &DesignerService{}
}

// fillDesignerInfo 填充设计师信息
// 参数:
//   - user: 用户信息
//   - role: 角色信息
//   - isFollow: 是否关注
//   - avatarRole: 头像角色信息
//   - nameRole: 昵称角色信息
//   - isFollow: 是否关注
//
// 返回:
//   - 设计师信息
func (s *DesignerService) fillDesignerInfo(user *model.L36ActionUser, avatarRole *model.L36RoleInfo, nameRole *model.L36RoleInfo, isFollow bool) *schema.DesignerInfo {
	if user == nil {
		return nil
	}
	return &schema.DesignerInfo{
		ID:       user.ID,
		RoleId:   user.NameRoleID,
		IsFollow: isFollow,
		Avatar:   avatarRole.GetAvatarInfo(),
		Name:     nameRole.GetRoleName(user),
	}
}

// getDesignerLevel 获取设计师等级
// 参数:
//   - exp: 经验值
//
// 返回:
//   - 设计师等级
func (s *DesignerService) getDesignerLevel(exp int64) int {
	level, _ := s.getDesignerLevelInfo(exp)
	return level
}

// getDesignerLevelInfo 获取设计师等级信息
// 参数:
//   - exp: 经验值
//
// 返回:
//   - level: 等级
//   - threshold: 升级所需经验阈值
func (s *DesignerService) getDesignerLevelInfo(exp int64) (level int, threshold int64) {
	userLevelCfg := config.C.UserLevel
	maxLevel := userLevelCfg.MaxLevel
	for _, levelCfg := range userLevelCfg.Exp {
		if levelCfg.Level >= maxLevel || exp < int64(levelCfg.MaxExp) {
			level = levelCfg.Level
			threshold = int64(levelCfg.MaxExp)
			break
		}
	}
	return
}

func (s *DesignerService) searchDesigner(ctx context.Context, name string) ([]model.L36ActionUser, error) {
	roles, err := repo.NewRoleRepo().FindByName(ctx, name)
	if err != nil {
		return nil, err
	}
	if len(roles) == 0 {
		return nil, nil
	}
	roleIds := lo.Map(roles, func(role model.L36RoleInfo, i int) int64 {
		return role.RoleID
	})
	return repo.NewUserRepo().FindByNameRoleId(ctx, roleIds)
}

// DesignerListSearch 搜索设计师列表
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数
//
// 返回:
//   - 设计师列表搜索结果
//   - 错误信息
func (s *DesignerService) DesignerListSearch(ctx *gin.Context, params *schema.DesignerListSearchReq) (*schema.DesignerListSearchRes, *errors.Err) {
	users, err := s.searchDesigner(ctx, params.Keyword)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("find users fail")
		return nil, errors.New(errors.ServerError)
	}
	userIds := lo.Map(users, func(user model.L36ActionUser, i int) int64 {
		return user.ID
	})
	userDict := lo.KeyBy(users, func(user model.L36ActionUser) int64 {
		return user.ID
	})
	roleDict, err := repo.NewRoleRepo().FindRelatedRoleMap(ctx, users)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("find roles fail")
		return nil, errors.New(errors.ServerError)
	}

	designerWorks, err := cache.NewUserHotWorkCache().Gets(ctx, userIds)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("get designer works fail")
		return nil, errors.New(errors.ServerError)
	}
	designerWorksDict := map[int64][]model.L36ActionWork{}
	for i, userId := range userIds {
		designerWorksDict[userId] = designerWorks[i]
	}

	followMap, err := repo.NewDesignerFollowRepo().GetFollowMap(ctx, params.UserId, userIds)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("get follow map fail")
		return nil, errors.New(errors.ServerError)
	}

	likeMap, err := repo.NewWorkLikeRepo().GetLikeMap(ctx, params.UserId, userIds)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("get like map fail")
		return nil, errors.New(errors.ServerError)
	}

	collectMap, err := repo.NewWorkCollectRepo().GetCollectMap(ctx, params.UserId, userIds)
	if err != nil {
		elog.WithContext(ctx).WithError(err).Error("get collect map fail")
		return nil, errors.New(errors.ServerError)
	}

	workService, designerService := NewWorkService(), NewDesignerService()
	list := []schema.DesignerItem{}
	for _, user := range users {
		avatarRole := roleDict[user.AvatarRoleID]
		nameRole := roleDict[user.NameRoleID]
		isFollow := followMap[user.ID]
		designerWorks := designerWorksDict[user.ID]
		list = append(list, schema.DesignerItem{
			DesignerInfo:  *s.fillDesignerInfo(&user, &avatarRole, &nameRole, isFollow),
			WorkCount:     int(user.WorkCount),
			FansCount:     int(user.FansCount),
			DesignerLevel: s.getDesignerLevel(int64(user.Exp)),
			Works: lo.Map(designerWorks, func(work model.L36ActionWork, i int) schema.WorkItem {
				user := userDict[work.UserID]
				avatarRole := roleDict[user.AvatarRoleID]
				nameRole := roleDict[user.NameRoleID]
				return schema.WorkItem{
					WorkId:       work.ID,
					Type:         int(work.Type),
					Name:         work.Name,
					Summary:      work.Summary,
					Video:        work.Video,
					Cover:        work.Cover,
					Images:       util.JsonParseStrArray(work.Images),
					Scene:        work.Scene,
					Property:     work.Property,
					IsLiked:      likeMap[work.ID],
					IsCollected:  collectMap[work.ID],
					LikeCount:    work.GetLikeCount(),
					CollectCount: work.GetCollectCount(),
					CommentCount: work.GetCommentCount(),
					Contents:     workService.DecodeContents(int(work.Contents)),
					Hot:          work.GetHot(),
					Designer:     *designerService.fillDesignerInfo(&user, &avatarRole, &nameRole, followMap[user.ID]),
					Visibility:   int(work.Visibility),
					AuditStatus:  int(work.AuditStatus),
					ResampleUrl:  work.ResampleURL,
					RoleId:       work.RoleID,
				}
			}),
		})
	}

	return &schema.DesignerListSearchRes{
		List: list,
	}, nil
}

// DesignerFollow 关注设计师
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数
//
// 返回:
//   - 错误信息
func (s *DesignerService) DesignerFollow(ctx *gin.Context, params *schema.DesignerFollowReq) *errors.Err {
	if params.UserId == params.DesignerId {
		return errors.New(errors.CannotFollowSelf)
	}
	designer, err := repo.NewUserRepo().FindById(ctx, params.DesignerId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find designer fail")
		return errors.New(errors.ServerError)
	}
	if designer == nil {
		return errors.New(errors.DesignerNotExist)
	}
	follow, err := repo.NewDesignerFollowRepo().FindOneByUserDesigner(ctx, params.UserId, params.DesignerId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find designer follow record fail")
		return errors.New(errors.ServerError)
	}
	if follow != nil && follow.IsDelete == 0 {
		return nil
	}
	if err := repo.NewDesignerFollowRepo().Follow(ctx, params.UserId, params.RoleId, params.DesignerId); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("follow designer fail")
		return errors.New(errors.ServerError)
	}
	userRepo := repo.NewUserRepo()
	// 更新设计师粉丝数
	if err := userRepo.UpdateById(ctx, params.DesignerId, map[string]interface{}{"fans_count": gorm.Expr("fans_count + 1")}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update designer fans count fail")
		return errors.New(errors.ServerError)
	}
	// 更新用户关注数
	if err := userRepo.UpdateById(ctx, params.UserId, map[string]interface{}{"follow_count": gorm.Expr("follow_count + 1")}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user follow count fail")
		return errors.New(errors.ServerError)
	}
	util.LogYunying("l36_action_designer_follow", map[string]interface{}{
		"user_id":          params.UserId,
		"role_id":          params.RoleId,
		"designer_id":      params.DesignerId,
		"designer_role_id": designer.RoleID,
	})
	return nil
}

// DesignerCancelFollow 取消关注设计师
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数
//
// 返回:
//   - 错误信息
func (s *DesignerService) DesignerCancelFollow(ctx *gin.Context, params *schema.DesignerCancelFollowReq) *errors.Err {
	designerFollowRepo := repo.NewDesignerFollowRepo()
	record, err := designerFollowRepo.FindOneByUserDesigner(ctx, params.UserId, params.DesignerId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find designer follow record fail")
		return errors.New(errors.ServerError)
	}
	if record == nil || record.IsDelete == 1 {
		return errors.New(errors.DesignerNotFollow)
	}
	designer, err := repo.NewUserRepo().FindById(ctx, params.DesignerId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find designer fail")
		return errors.New(errors.ServerError)
	}
	if designer == nil {
		return errors.New(errors.DesignerNotExist)
	}
	if err := designerFollowRepo.UpdateById(ctx, record.ID, map[string]interface{}{"is_delete": 1}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("cancel follow designer fail")
		return errors.New(errors.ServerError)
	}
	userRepo := repo.NewUserRepo()
	if err := userRepo.UpdateById(ctx, params.DesignerId, map[string]interface{}{"fans_count": gorm.Expr("if(fans_count > 0, fans_count - 1, 0)")}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update designer fans count fail")
		return errors.New(errors.ServerError)
	}
	// 更新用户的关注数
	if err := userRepo.UpdateById(ctx, params.UserId, map[string]interface{}{"follow_count": gorm.Expr("if(follow_count > 0, follow_count - 1, 0)")}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user follow count fail")
		return errors.New(errors.ServerError)
	}
	util.LogYunying("l36_action_designer_cancel_follow", map[string]interface{}{
		"user_id":          params.UserId,
		"role_id":          params.RoleId,
		"designer_id":      params.DesignerId,
		"designer_role_id": designer.RoleID,
	})
	return nil
}

// DesignerDetail 获取设计师详情
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数
//
// 返回:
//   - 设计师详情
//   - 错误信息
func (s *DesignerService) DesignerDetail(ctx *gin.Context, params *schema.DesignerDetailReq) (*schema.DesignerDetailRes, *errors.Err) {
	designer, err := repo.NewUserRepo().FindById(ctx, params.DesignerId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find designer fail")
		return nil, errors.New(errors.ServerError)
	}
	if designer == nil {
		return nil, errors.New(errors.DesignerNotExist)
	}
	roleIds := []int64{designer.AvatarRoleID, designer.NameRoleID}
	roles, err := repo.NewRoleRepo().FindByRoleIds(ctx, roleIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find role fail")
		return nil, errors.New(errors.ServerError)
	}
	roleDict := lo.KeyBy(roles, func(role model.L36RoleInfo) int64 {
		return role.RoleID
	})
	followMap, err := repo.NewDesignerFollowRepo().GetFollowMap(ctx, params.UserId, []int64{params.DesignerId})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get follow map fail")
		return nil, errors.New(errors.ServerError)
	}
	level, threshold := s.getDesignerLevelInfo(int64(designer.Exp))
	avatarRole := roleDict[designer.AvatarRoleID]
	nameRole := roleDict[designer.NameRoleID]
	return &schema.DesignerDetailRes{
		DesignerInfo:  *s.fillDesignerInfo(designer, &avatarRole, &nameRole, followMap[params.DesignerId]),
		WorkCount:     int(designer.WorkCount),
		FansCount:     int(designer.FansCount),
		LikedCount:    designer.GetLikedCount(),
		FollowCount:   int(designer.FollowCount),
		DesignerLevel: level,
		Exp:           int(designer.Exp),
		ExpThreshold:  int(threshold),
	}, nil
}

// DesignerListFollow 获取关注的设计师列表
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数
//
// 返回:
//   - 关注的设计师列表
//   - 错误信息
func (s *DesignerService) DesignerListFollow(ctx *gin.Context, params *schema.DesignerListFollowReq) (*schema.DesignerListFollowRes, *errors.Err) {
	follows, err := repo.NewDesignerFollowRepo().ListDesignerFollow(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list designer follow fail")
		return nil, errors.New(errors.ServerError)
	}
	designerIds := lo.Map(follows, func(follow model.L36ActionDesignerFollow, i int) int64 {
		return follow.DesignerID
	})
	users, err := repo.NewUserRepo().FindByIds(ctx, designerIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find users fail")
		return nil, errors.New(errors.ServerError)
	}
	userDict := lo.KeyBy(users, func(user model.L36ActionUser) int64 {
		return user.ID
	})
	roleDict, err := repo.NewRoleRepo().FindRelatedRoleMap(ctx, users)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find related roles fail")
		return nil, errors.New(errors.ServerError)
	}

	return &schema.DesignerListFollowRes{
		List: lo.Map(follows, func(follow model.L36ActionDesignerFollow, i int) schema.DesignerInfo {
			user := userDict[follow.DesignerID]
			avatarRole := roleDict[user.AvatarRoleID]
			nameRole := roleDict[user.NameRoleID]
			return *s.fillDesignerInfo(&user, &avatarRole, &nameRole, true)
		}),
	}, nil
}
