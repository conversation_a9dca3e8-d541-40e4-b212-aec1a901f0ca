package service

import (
	"errors"
	"strconv"
	"strings"

	"app/config"
	"app/util/crypto"

	"github.com/golang-jwt/jwt"
)

type SkeyClaims struct {
	UserId int64 `json:"user_id"`
	RoleId int64 `json:"role_id"`
	Time   int64 `json:"time"`
}

func (s *SkeyClaims) Valid() error {
	return nil
}

type SkeyService struct{}

func NewSkeyService() *SkeyService {
	return &SkeyService{}
}

func getToken(aid string, roleId int64, time int64) string {
	signature := strings.Join([]string{aid, strconv.Itoa(int(roleId)), strconv.Itoa(int(time)), config.C.Skey.TokenSecret}, "")
	return crypto.HexMd5(signature)
}

// GenerateSkey generates a secure key (skey) for user authentication
//
// Parameters:
//   - userId: user's unique identifier
//   - roleId: role identifier of the user
//   - time: timestamp for key generation
//
// Returns:
//   - string: generated secure key
func (s *SkeyService) GenerateSkey(userId int64, roleId int64, time int64) string {
	claims := SkeyClaims{
		UserId: userId,
		RoleId: roleId,
		Time:   time,
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, &claims)
	skey, _ := token.SignedString([]byte(config.C.Skey.JwtSecret))
	return skey
}

// ParseSkey 解析skey
func (s *SkeyService) ParseSkey(skey string) (*SkeyClaims, error) {
	token, err := jwt.ParseWithClaims(skey, &SkeyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.C.Skey.JwtSecret), nil
	})
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*SkeyClaims)
	if !ok {
		return nil, errors.New("parse skey failed")
	}
	return claims, nil
}

// CheckSkeyToken 检查skey token是否合法
func (s *SkeyService) CheckSkeyToken(aid string, roleId int64, time int64, token string) bool {
	if !config.C.Skey.EnableToken {
		return true
	}
	expectToken := getToken(aid, roleId, time)
	return token == expectToken
}

// CheckSkey 检查skey是否合法
func (s *SkeyService) CheckSkey(skey string) (*SkeyClaims, error) {
	token, err := jwt.ParseWithClaims(skey, &SkeyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.C.Skey.JwtSecret), nil
	})
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*SkeyClaims)
	if !ok {
		return nil, errors.New("parse skey failed")
	}
	return claims, nil
}
