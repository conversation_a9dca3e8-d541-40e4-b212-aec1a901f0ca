package service

import (
	"app/dao/cache"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util/log"

	"github.com/gin-gonic/gin"
)

type RankService struct{}

func NewRankService() *RankService {
	return &RankService{}
}

// RankDesignerExp 获取设计师经验值排行榜
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数，包含角色ID
//
// 返回:
//   - 设计师经验值排行榜信息
//   - 错误信息
func (s *RankService) RankDesignerExp(ctx *gin.Context, params *schema.RankDesignerExpReq) (*schema.RankDesignerExpRes, *errors.Err) {
	list, err := cache.NewDesignerExpRankCache().Get(ctx, 0)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get designer exp rank failed")
		return nil, errors.New(errors.ServerError)
	}
	designerService := NewDesignerService()
	self := &schema.RankDesignerItem{}
	for i, item := range list {
		list[i].DesignerLevel = designerService.getDesignerLevel(item.Exp)
		if item.RoleId == params.RoleId {
			self = &item
		}
	}
	if self.Rank == 0 {
		user, err := repo.NewUserRepo().FindOneByRoleId(ctx, params.RoleId)
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Error("find user by role id failed")
			return nil, errors.New(errors.ServerError)
		}
		role, err := repo.NewRoleRepo().FindOneByRoleId(ctx, params.RoleId)
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Error("find role by role id failed")
			return nil, errors.New(errors.ServerError)
		}
		if user == nil || role == nil {
			// 用户或角色不存在时，返回空的自身信息
			self = nil
		} else {
			self = &schema.RankDesignerItem{
				RoleId:        user.RoleID,
				RoleName:      user.Name,
				Job:           int(role.Job),
				Gender:        int(role.Gender),
				Level:         int(role.Level),
				Exp:           int64(user.Exp),
				DesignerLevel: designerService.getDesignerLevel(int64(user.Exp)),
			}
		}
	}

	return &schema.RankDesignerExpRes{List: list, Self: self}, nil
}

// RankWorkHot 获取作品热度排行榜
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数
//
// 返回:
//   - 作品热度排行榜信息
//   - 错误信息
func (s *RankService) RankWorkHot(ctx *gin.Context, params *schema.RankWorkHotReq) (*schema.RankWorkHotRes, *errors.Err) {
	items, err := cache.NewWorkHotRankCache().Get(ctx, 0)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get work hot rank failed")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.RankWorkHotRes{
		List: items,
	}, nil
}
