package service

import (
	"strconv"

	"app/errors"
	"app/schema"
	"app/util/log"

	filepicker_tools "ccc-gitlab.leihuo.netease.com/pkgo/filepicker-tools"
	"github.com/gin-gonic/gin"
)

type CommonService struct{}

func NewCommonService() *CommonService {
	return &CommonService{}
}

// CommonFpToken 获取预审通过状态的文件上传token
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数，包含角色ID
//
// 返回:
//   - 文件上传token信息
//   - 错误信息
func (s *CommonService) CommonFpToken(ctx *gin.Context, params *schema.CommonFpTokenReq) (*schema.CommonFpTokenRes, *errors.Err) {
	fpService := NewFPService()
	policy := filepicker_tools.NewExtraPolicyBuilder().SetUID(strconv.Itoa(int(params.RoleId))).SetReviewStatus(filepicker_tools.ReviewStatusPrepassed).Build()
	token, err := fpService.fp.GetToken(policy)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("获取文件上传token失败")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.CommonFpTokenRes{
		Expires:   token.Expires,
		Project:   token.Project,
		Token:     token.Token,
		UploadUrl: token.UploadURL,
	}, nil
}

// CommonFpPassToken 获取已审核通过状态的文件上传token
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数，包含角色ID
//
// 返回:
//   - 文件上传token信息
//   - 错误信息
func (s *CommonService) CommonFpPassToken(ctx *gin.Context, params *schema.CommonFpPassTokenReq) (*schema.CommonFpPassTokenRes, *errors.Err) {
	fpService := NewFPService()
	policy := filepicker_tools.NewExtraPolicyBuilder().SetUID(strconv.Itoa(int(params.RoleId))).SetReviewStatus(filepicker_tools.ReviewStatusPassed).Build()
	token, err := fpService.fp.GetToken(policy)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("获取文件上传token失败")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.CommonFpPassTokenRes{
		Expires:   token.Expires,
		Project:   token.Project,
		Token:     token.Token,
		UploadUrl: token.UploadURL,
	}, nil
}
