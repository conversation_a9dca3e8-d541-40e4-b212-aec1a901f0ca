package service

import (
	"app/config"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util/log"

	"github.com/gin-gonic/gin"
)

type UserService struct{}

func NewUserService() *UserService {
	return &UserService{}
}

// fillUserInfo 填充用户信息
// 参数:
//   - user: 用户信息
//   - role: 角色信息
//
// 返回:
//   - 用户信息
func (s *UserService) fillUserInfo(user *model.L36ActionUser, avatarRole *model.L36RoleInfo, nameRole *model.L36RoleInfo) *schema.UserInfo {
	if user == nil {
		return nil
	}
	return &schema.UserInfo{
		ID:       user.ID,
		Name:     nameRole.GetRoleName(user),
		Avatar:   avatarRole.GetAvatarInfo(),
		ServerID: avatarRole.ServerID,
	}
}

// UserAvatarList 获取用户头像列表
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数，包含用户ID
//
// 返回:
//   - 用户头像列表
//   - 错误信息
func (s *UserService) UserAvatarList(ctx *gin.Context, params *schema.UserAvatarListReq) (*schema.UserAvatarListRes, *errors.Err) {
	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("userRepo.FindById failed")
		return nil, errors.New(errors.ServerError)
	}
	roleRepo := repo.NewRoleRepo()
	userRole, err := roleRepo.FindOneByRoleId(ctx, user.RoleID)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("roleRepo.FindOneByRoleId failed")
		return nil, errors.New(errors.ServerError)
	}
	if userRole == nil {
		return &schema.UserAvatarListRes{
			List: []schema.UserAvatarInfo{},
		}, nil
	}
	roles, err := roleRepo.FindByAid(ctx, userRole.Aid)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("roleRepo.FindByAid failed")
		return nil, errors.New(errors.ServerError)
	}
	list := make([]schema.UserAvatarInfo, 0, len(roles))
	for _, role := range roles {
		list = append(list, schema.UserAvatarInfo{
			AvatarInfo: schema.AvatarInfo{
				RoleId:      role.RoleID,
				Job:         role.Job,
				Gender:      role.Gender,
				Avatar:      role.Avatar,
				AvatarFrame: role.AvatarFrame,
			},
			IsDefault: role.RoleID == user.AvatarRoleID,
		})
	}
	// 将默认头像放在第一个
	for i, item := range list {
		if item.IsDefault {
			list[0], list[i] = list[i], list[0]
			break
		}
	}
	return &schema.UserAvatarListRes{
		List: list,
	}, nil
}

func (s *UserService) checkRoleAid(role *model.L36RoleInfo, aid string) bool {
	if role == nil {
		return false
	}
	if role.Aid == aid {
		return true
	}
	if config.C.Test.TestEnv {
		return true
	}
	return false
}

// UserAvatarUpdate 更新用户头像
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数，包含用户ID和头像角色ID
//
// 返回:
//   - 错误信息
func (s *UserService) UserAvatarUpdate(ctx *gin.Context, params *schema.UserAvatarUpdateReq) *errors.Err {
	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("userRepo.FindById failed")
		return errors.New(errors.ServerError)
	}
	if user.AvatarRoleID == params.AvatarRoleId {
		return nil
	}
	role, err := repo.NewRoleRepo().FindOneByRoleId(ctx, params.AvatarRoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("roleRepo.FindOneByRoleId failed")
		return errors.New(errors.ServerError)
	}
	if !s.checkRoleAid(role, user.Aid) {
		return errors.New(errors.InvalidAvatarRoleId)
	}
	if err := repo.NewUserRepo().UpdateById(ctx, user.ID, map[string]interface{}{
		"avatar_role_id": role.RoleID,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("userRepo.UpdateById failed")
		return errors.New(errors.ServerError)
	}

	cache.NewDesignerRoleCache().Del(ctx, user.ID)
	return nil
}

// UserUpdate 更新用户信息
// 参数:
//   - ctx: gin上下文
//   - params: 请求参数，包含用户ID和新的用户名
//
// 返回:
//   - 错误信息
func (s *UserService) UserUpdate(ctx *gin.Context, params *schema.UserUpdateReq) *errors.Err {
	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("userRepo.FindById failed")
		return errors.New(errors.ServerError)
	}
	role, err := repo.NewRoleRepo().FindOneByRoleId(ctx, params.NameRoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("roleRepo.FindOneByRoleId failed")
		return errors.New(errors.ServerError)
	}
	if !s.checkRoleAid(role, user.Aid) {
		return errors.New(errors.InvalidAvatarRoleId)
	}
	if err := repo.NewUserRepo().UpdateById(ctx, user.ID, map[string]interface{}{
		"name_role_id": params.NameRoleId,
		"name":         role.RoleName,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("userRepo.UpdateById failed")
		return errors.New(errors.ServerError)
	}
	// 同步到 es
	if err := es.NewDesignerEsRepo().Index(ctx, int(user.ID), es.Designer{
		ID:   user.ID,
		Name: role.RoleName,
		Exp:  user.Exp,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("designerEsRepo.Index failed")
	}
	cache.NewDesignerRoleCache().Del(ctx, user.ID)
	return nil
}

func (s *UserService) UserNameList(ctx *gin.Context, params *schema.UserNameListReq) (*schema.UserNameListRes, *errors.Err) {
	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("userRepo.FindById failed")
		return nil, errors.New(errors.ServerError)
	}
	roles, err := repo.NewRoleRepo().FindByAid(ctx, user.Aid)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("roleRepo.FindByAid failed")
		return nil, errors.New(errors.ServerError)
	}
	list := make([]schema.UserNameInfo, 0, len(roles))
	for _, role := range roles {
		list = append(list, schema.UserNameInfo{
			RoleID:    role.RoleID,
			RoleName:  role.RoleName,
			IsDefault: role.RoleID == user.NameRoleID,
		})
	}
	// 将默认昵称放在第一个
	for i, item := range list {
		if item.IsDefault {
			list[0], list[i] = list[i], list[0]
			break
		}
	}
	return &schema.UserNameListRes{
		List: list,
	}, nil
}
