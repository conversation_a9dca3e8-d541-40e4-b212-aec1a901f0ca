package service

import (
	"time"

	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util"
	"app/util/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type GmService struct{}

func NewGmService() *GmService {
	return &GmService{}
}

func (s *GmService) GmWorkDel(ctx *gin.Context, params *schema.GmWorkDelReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkDel.FindById")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}
	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, map[string]interface{}{
		"is_delete": 1,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("delete work error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
		"is_delete": 1,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work is_delete failed")
	}

	if err = repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"work_count": gorm.Expr("if(work_count > 1, work_count - 1, 0)"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user work count error")
		return errors.New(errors.ServerError)
	}
	// 删除 ES 中的作品
	if err := es.NewWorkEsRepo().Delete(ctx, int(work.ID)); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("delete work from es error")
	}
	// 写入到作品删除表，用于异步处理作品删除以后，收藏数据
	if err := repo.NewWorkDeleteRepo().Save(ctx, &model.L36ActionWorkDelete{
		WorkID: work.ID,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create work delete error")
	}
	util.LogYunying("l36_action_work_del", map[string]interface{}{
		"work_id": work.ID,
		"user_id": work.UserID,
		"role_id": work.RoleID,
		"source":  "gm",
	})

	// 删除作品通知
	informService := NewInformService()
	if err := informService.DelWorkInform(ctx, work.RoleID, work.ID); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("del work inform failed")
	}

	return nil
}

func (s *GmService) GmWorkRecover(ctx *gin.Context, params *schema.GmWorkRecoverReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkRecover.FindById")
		return errors.New(errors.ServerError)
	}
	if work == nil {
		return errors.New(errors.WorkNotExist)
	}
	if work.IsDelete == 0 {
		return errors.New(errors.WorkNotDelete)
	}

	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, map[string]interface{}{
		"is_delete": 0,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("recover work error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
		"is_delete": 0,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work is_delete failed")
	}

	if err = repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"work_count": gorm.Expr("work_count + 1"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user work count error")
		return errors.New(errors.ServerError)
	}

	// 重新索引到 ES
	if err := es.NewWorkEsRepo().IndexWork(ctx, work, nil); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("index work to es error")
	}

	util.LogYunying("l36_action_work_recover", map[string]interface{}{
		"work_id": work.ID,
		"user_id": work.UserID,
		"role_id": work.RoleID,
	})
	return nil
}

func (s *GmService) GmWorkSetVisibility(ctx *gin.Context, params *schema.GmWorkSetVisibilityReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkSetVisibility.FindById")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}
	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, map[string]interface{}{
		"visibility": params.Visibility,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work visibility error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
		"visibility": params.Visibility,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work visibility error")
		return errors.New(errors.ServerError)
	}

	if err := es.NewWorkEsRepo().Update(ctx, int(work.ID), map[string]interface{}{
		"visibility": params.Visibility,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work visibility error")
		return errors.New(errors.ServerError)
	}

	util.LogYunying("l36_action_work_update", map[string]interface{}{
		"work_id": work.ID,
		"user_id": work.UserID,
		"role_id": work.RoleID,
		"source":  "gm",
		"update_map": map[string]interface{}{
			"visibility": params.Visibility,
		},
	})

	return nil
}

func (s *GmService) GmCreatorBan(ctx *gin.Context, params *schema.GmCreatorBanReq) *errors.Err {
	user, err := repo.NewUserRepo().FindOneByRoleId(ctx, params.RoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCreatorBan.FindOneByRoleId")
		return errors.New(errors.ServerError)
	}
	if user == nil {
		return errors.New(errors.UserNotExist)
	}
	expireTime := 24 * 60 * 60 * 7
	if params.ExpireTime > 0 {
		expireTime = int(params.ExpireTime)
	}
	expireAt := time.Now().Add(time.Duration(expireTime) * time.Second).UnixMilli()

	ban := &model.L36ActionGmCreatorBan{
		UserID:     user.ID,
		RoleID:     params.RoleId,
		BanType:    constant.BanTypeWork,
		ExpireTime: expireAt,
	}
	if err := repo.NewGmCreatorBanRepo().UpdateBan(ctx, ban); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCreatorBan.UpdateBan")
		return errors.New(errors.ServerError)
	}
	return nil
}

func (s *GmService) GmWorkUpdateName(ctx *gin.Context, params *schema.GmWorkUpdateNameReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkUpdateName.FindById")
		return errors.New(errors.ServerError)
	}
	if work == nil {
		return errors.New(errors.WorkNotExist)
	}
	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, map[string]interface{}{
		"name": params.Name,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work name error")
		return errors.New(errors.ServerError)
	}

	if _, err := cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
		"name": params.Name,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work name error")
		return errors.New(errors.ServerError)
	}
	// 更新 ES 中的作品名称
	if err := es.NewWorkEsRepo().Update(ctx, int(work.ID), map[string]interface{}{
		"name": params.Name,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work name error")
		return errors.New(errors.ServerError)
	}
	return nil
}

func (s *GmService) GmWorkUpdateSummary(ctx *gin.Context, params *schema.GmWorkUpdateSummaryReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkUpdateSummary.FindById")
		return errors.New(errors.ServerError)
	}
	if work == nil {
		return errors.New(errors.WorkNotExist)
	}
	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, map[string]interface{}{
		"summary": params.Summary,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work summary error")
		return errors.New(errors.ServerError)
	}

	if _, err := cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
		"summary": params.Summary,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work summary error")
		return errors.New(errors.ServerError)
	}

	// 更新 ES 中的作品描述
	if err := es.NewWorkEsRepo().Update(ctx, int(work.ID), map[string]interface{}{
		"summary": params.Summary,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work summary error")
		return errors.New(errors.ServerError)
	}

	return nil
}

func (s *GmService) GmCommentDel(ctx *gin.Context, params *schema.GmCommentDelReq) *errors.Err {
	commentRepo := repo.NewCommentRepo()
	comment, err := commentRepo.FindById(ctx, params.CommentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCommentDel.FindById")
		return errors.New(errors.ServerError)
	}
	if comment == nil {
		return errors.New(errors.CommentNotExist)
	}
	workRepo := repo.NewWorkRepo()
	work, err := workRepo.FindById(ctx, comment.WorkID)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work failed")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}

	// 删除评论本身
	if err := commentRepo.UpdateById(ctx, params.CommentId, map[string]interface{}{"is_delete": 1}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("delete comment failed")
		return errors.New(errors.ServerError)
	}

	// 如果是二级评论，更新原评论的回复数
	if comment.ReplyCommentID > 0 {
		if err := commentRepo.UpdateById(ctx, comment.ReplyCommentID, map[string]interface{}{"reply_count": gorm.Expr("if(reply_count > 0, reply_count - 1, 0)")}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update reply count failed")
			return errors.New(errors.ServerError)
		}
	}

	// 更新作品评论总数
	commentCount := 1
	if comment.ReplyCommentID <= 0 {
		// 如果是一级评论，需要加上其下的所有回复数
		commentCount += int(comment.ReplyCount)
	}

	if err := workRepo.UpdateById(ctx, comment.WorkID, map[string]interface{}{
		"comment_count": gorm.Expr("if(comment_count > ?, comment_count - ?, 0)", commentCount, commentCount),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work comment count failed")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Incr(ctx, comment.WorkID, map[string]interface{}{
		"comment_count": -commentCount,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work comment count failed")
		return errors.New(errors.ServerError)
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(comment.WorkID), map[string]interface{}{
		"comment_count": int32(work.CommentCount + work.FakeCommentCount - int32(commentCount)),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work comment count error")
	}

	util.LogYunying("l36_action_work_comment_del", map[string]interface{}{
		"work_id":    comment.WorkID,
		"user_id":    comment.UserID,
		"role_id":    comment.RoleID,
		"comment_id": params.CommentId,
		"source":     "gm",
	})
	return nil
}

func (s *GmService) GmCommentBan(ctx *gin.Context, params *schema.GmCommentBanReq) *errors.Err {
	user, err := repo.NewUserRepo().FindOneByRoleId(ctx, params.RoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCreatorBan.FindOneByRoleId")
		return errors.New(errors.ServerError)
	}
	if user == nil {
		return errors.New(errors.UserNotExist)
	}
	expireAt := time.Now().Add(time.Duration(params.ExpireTime) * time.Second).UnixMilli()
	if err := repo.NewGmCreatorBanRepo().UpdateBan(ctx, &model.L36ActionGmCreatorBan{
		UserID:     user.ID,
		RoleID:     params.RoleId,
		BanType:    constant.BanTypeComment,
		ExpireTime: expireAt,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCommentBan.UpdateBan")
		return errors.New(errors.ServerError)
	}
	return nil
}

func (s *GmService) GmWorkDetail(ctx *gin.Context, params *schema.GmWorkDetailReq) (*schema.GmWorkDetailRes, *errors.Err) {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkDetail.FindById")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil {
		return nil, errors.New(errors.WorkNotExist)
	}
	workInfo := schema.GmWorkItem{
		WorkId:     work.ID,
		Name:       work.Name,
		Summary:    work.Summary,
		LikeCount:  int(work.LikeCount),
		Visibility: int(work.Visibility),
		Video:      work.Video,
		Images:     util.JsonToStringArray(work.Images),
	}
	return &schema.GmWorkDetailRes{
		GmWorkItem: workInfo,
	}, nil
}

func (s *GmService) GmWorkListRole(ctx *gin.Context, params *schema.GmWorkListRoleReq) (*schema.GmWorkListRoleRes, *errors.Err) {
	user, err := repo.NewUserRepo().FindOneByRoleId(ctx, params.RoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkListRole.FindOneByRoleId")
		return nil, errors.New(errors.ServerError)
	}
	if user == nil {
		return nil, errors.New(errors.UserNotExist)
	}

	works, err := repo.NewWorkRepo().FindByUserId(ctx, user.ID, 100)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmWorkListRole.ListWorkDesigner")
		return nil, errors.New(errors.ServerError)
	}

	workInfos := make([]schema.GmWorkItem, 0)
	for _, work := range works {
		workInfos = append(workInfos, schema.GmWorkItem{
			WorkId:     work.ID,
			Name:       work.Name,
			Summary:    work.Summary,
			LikeCount:  int(work.LikeCount),
			Visibility: int(work.Visibility),
			Video:      work.Video,
			Images:     util.JsonToStringArray(work.Images),
		})
	}
	return &schema.GmWorkListRoleRes{
		List: workInfos,
	}, nil
}

func (s *GmService) GmCommentListWork(ctx *gin.Context, params *schema.GmCommentListWorkReq) (*schema.GmCommentListWorkRes, *errors.Err) {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCommentListWork.FindById")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil {
		return nil, errors.New(errors.WorkNotExist)
	}
	comments, err := repo.NewCommentRepo().FindByWorkId(ctx, work.ID, 100)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("GmService.GmCommentListWork.FindByWorkId")
		return nil, errors.New(errors.ServerError)
	}

	commentInfos := make([]schema.GmCommentItem, 0)
	for _, comment := range comments {
		commentInfos = append(commentInfos, schema.GmCommentItem{
			ID:              comment.ID,
			Content:         comment.Content,
			OriginCommentID: comment.ReplyCommentID,
			RoleID:          comment.RoleID,
		})
	}
	return &schema.GmCommentListWorkRes{
		List: commentInfos,
	}, nil
}
