package service

import (
	"strings"
	"time"

	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util"
	"app/util/log"
	"app/util/wait"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// CommentService 提供评论相关的服务
type CommentService struct{}

// NewCommentService 创建一个新的评论服务实例
func NewCommentService() *CommentService {
	return &CommentService{}
}

// checkText 检查评论文本是否合法
// ctx: 上下文
// text: 评论文本
// 返回: 错误信息，如果文本合法则返回nil
func (s *CommentService) checkText(ctx *gin.Context, text string) *errors.Err {
	str := strings.TrimSpace(text)
	if str == "" {
		return errors.New(errors.StringIsEmptyCodeErr)
	}
	if len(text) > 100 {
		return errors.New(errors.CommentTextTooLong)
	}
	isValid := NewEnvsdkService().ReviewWords(ctx, str)
	if !isValid {
		return errors.New(errors.InvalidCommentText)
	}
	return nil
}

// checkComment 检查评论是否存在且有效
// ctx: 上下文
// commentId: 评论ID
// 返回: 评论对象和错误信息
func (s *CommentService) checkComment(ctx *gin.Context, commentId int64) (*model.L36ActionWorkComment, *errors.Err) {
	commentRepo := repo.NewCommentRepo()
	comment, err := commentRepo.FindById(ctx, commentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find comment failed")
		return nil, errors.New(errors.ServerError)
	}
	if comment == nil || comment.IsDelete == 1 {
		return nil, errors.New(errors.CommentNotExist)
	}
	return comment, nil
}

func (s *CommentService) getCommentsRoleMap(ctx *gin.Context, users []model.L36ActionUser, comments []model.L36ActionWorkComment) (map[int64]model.L36RoleInfo, error) {
	roleIds := []int64{}
	for _, comment := range comments {
		roleIds = append(roleIds, comment.RoleID)
		if comment.ReplyRoleID > 0 {
			roleIds = append(roleIds, comment.ReplyRoleID)
		}
	}
	for _, user := range users {
		if user.AvatarRoleID > 0 {
			roleIds = append(roleIds, user.AvatarRoleID)
		}
		if user.NameRoleID > 0 {
			roleIds = append(roleIds, user.NameRoleID)
		}
		if user.RoleID > 0 {
			roleIds = append(roleIds, user.RoleID)
		}
	}
	roleIds = lo.Uniq(roleIds)
	roles, err := repo.NewRoleRepo().FindByRoleIds(ctx, roleIds)
	roleMap := map[int64]model.L36RoleInfo{}
	if err != nil {
		return roleMap, err
	}
	for _, role := range roles {
		roleMap[role.RoleID] = role
	}
	return roleMap, nil
}

// CommentAdd 添加评论
// ctx: 上下文
// params: 添加评论的请求参数
// 返回: 添加评论的响应和错误信息
func (s *CommentService) CommentAdd(ctx *gin.Context, params *schema.CommentAddReq) (*schema.CommentAddRes, *errors.Err) {
	checkError := s.checkText(ctx, params.Content)
	if checkError != nil {
		return nil, checkError
	}
	workRepo, commentRepo := repo.NewWorkRepo(), repo.NewCommentRepo()
	work, err := workRepo.FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work failed")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return nil, errors.New(errors.WorkNotExist)
	}

	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find user failed")
		return nil, errors.New(errors.ServerError)
	}
	if user == nil {
		return nil, errors.New(errors.UserNotExist)
	}
	if isBanned, err := repo.NewGmCreatorBanRepo().IsBanned(ctx, work.UserID, constant.BanTypeComment); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("check creator banned error")
		return nil, errors.New(errors.ServerError)
	} else if isBanned {
		return nil, errors.New(errors.CreatorBannedComment)
	}

	oldComment, err := commentRepo.FindOneByUserWork(ctx, params.UserId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find comment failed")
		return nil, errors.New(errors.ServerError)
	}
	originCommentId, replyCommentId, replyUserId, replyRoleId, replyContent := int64(-1), int64(-1), int64(-1), int64(-1), ""
	if params.ReplyCommentId != 0 {
		replyComment, err := commentRepo.FindById(ctx, params.ReplyCommentId)
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Error("find reply comment failed")
			return nil, errors.New(errors.ServerError)
		}
		if replyComment == nil || replyComment.IsDelete == 1 || replyComment.WorkID != params.WorkId {
			return nil, errors.New(errors.ReplyNotExist)
		}
		originCommentId = lo.If(replyComment.OriginCommentID == -1, replyComment.ID).Else(replyComment.OriginCommentID)
		replyCommentId = replyComment.ID
		replyUserId = replyComment.UserID
		replyRoleId = replyComment.RoleID
		replyContent = replyComment.Content
	}
	// 每天只有第一个评论会增加热度
	isAddHot := oldComment == nil || oldComment.Ctime < util.GetTodayStart()
	comment := model.L36ActionWorkComment{
		UserID:          params.UserId,
		RoleID:          user.NameRoleID,
		WorkID:          params.WorkId,
		OriginCommentID: originCommentId,
		ReplyCommentID:  replyCommentId,
		ReplyUserID:     replyUserId,
		ReplyRoleID:     replyRoleId,
		Content:         params.Content,
		IsAddHot:        int8(lo.If(isAddHot, 1).Else(0)),
	}
	if err := commentRepo.Create(ctx, &comment); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create comment failed")
		return nil, errors.New(errors.ServerError)
	}

	// 添加通知
	informService := NewInformService()
	if comment.ReplyCommentID > 0 {
		// 如果是回复评论，发送回复通知
		if err := informService.AddWorkReplyInform(ctx, user.NameRoleID, replyRoleId, params.WorkId, work.Name, comment.ID, params.Content, replyContent); err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("add work reply inform failed")
		}
	} else {
		// 如果是普通评论，发送评论通知
		if err := informService.AddWorkCommentInform(ctx, user.NameRoleID, work.RoleID, params.WorkId, work.Name, comment.ID, params.Content); err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("add work comment inform failed")
		}
	}

	if originCommentId > 0 {
		if err := commentRepo.UpdateById(ctx, originCommentId, map[string]interface{}{"reply_count": gorm.Expr("reply_count + 1")}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update reply count failed")
			return nil, errors.New(errors.ServerError)
		}
	}

	hot, exp, err := NewWorkService().calculateHotAndExp(ctx, params.UserId, work.UserID, work, repo.FieldCommentCount, !isAddHot)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("calculate hot and exp for comment failed")
		return nil, errors.New(errors.ServerError)
	}

	// Update work with calculated hot value
	if err := workRepo.UpdateById(ctx, params.WorkId, map[string]interface{}{
		"comment_count": gorm.Expr("comment_count + 1"),
		"hot":           gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work comment count and hot failed")
		return nil, errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Incr(ctx, params.WorkId, map[string]interface{}{
		"comment_count": 1,
		"hot":           hot,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work comment count and hot failed")
		return nil, errors.New(errors.ServerError)
	}

	// Update author's exp if applicable
	if exp > 0 {
		if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{"exp": gorm.Expr("exp + ?", exp)}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update user exp failed")
			return nil, errors.New(errors.ServerError)
		}
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(params.WorkId), map[string]interface{}{
		"comment_count": work.CommentCount + work.FakeCommentCount + 1,
		"hot":           work.Hot + int32(hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work comment count error")
	}

	util.LogYunying("l36_action_work_comment", map[string]interface{}{
		"work_id":          params.WorkId,
		"user_id":          params.UserId,
		"role_id":          params.RoleId,
		"reply_comment_id": replyCommentId,
		"content":          params.Content,
		"comment_id":       comment.ID,
	})
	if params.Scm != "" {
		util.LogYunying("l36_action_bhv", map[string]interface{}{
			"sessionid": params.SessionId,
			"traceid":   params.TraceId,
			"role_id":   params.RoleId,
			"id":        params.WorkId,
			"scm":       params.Scm,
			"bhv_time":  time.Now().Unix(),
			"bhv_type":  "comment",
		})
	}
	userService := NewUserService()

	userIds := []int64{params.UserId}
	if replyUserId > 0 {
		userIds = append(userIds, replyUserId)
	}
	users, err := repo.NewUserRepo().FindByIds(ctx, userIds)
	if err != nil {
		return nil, errors.New(errors.ServerError)
	}
	userDict := lo.KeyBy(users, func(user model.L36ActionUser) int64 {
		return user.ID
	})

	roleMap, err := s.getCommentsRoleMap(ctx, users, []model.L36ActionWorkComment{comment})
	if err != nil {
		return nil, errors.New(errors.ServerError)
	}

	commentRole := roleMap[comment.RoleID]
	replyUser := userDict[replyUserId]
	replyRole, ok := roleMap[replyRoleId]
	if !ok && replyUserId > 0 {
		replyRole = roleMap[replyUser.NameRoleID]
	}

	return &schema.CommentAddRes{
		CommentItem: schema.CommentItem{
			ID:              comment.ID,
			Content:         comment.Content,
			Ctime:           comment.Ctime,
			LikeCount:       int(comment.LikeCount),
			WorkId:          comment.WorkID,
			RoleId:          comment.RoleID,
			OriginCommentId: comment.OriginCommentID,
			ReplyCommentId:  comment.ReplyCommentID,
			ReplyCount:      int(comment.ReplyCount),
			User:            userService.fillUserInfo(user, &commentRole, &commentRole),
			ReplyUser:       userService.fillUserInfo(&replyUser, &replyRole, &replyRole),
		},
	}, nil
}

func (s *CommentService) checkCommentAuth(work *model.L36ActionWork, comment *model.L36ActionWorkComment, userId int64) bool {
	if comment.UserID == userId {
		return true
	}
	if work.UserID == userId {
		return true
	}
	return false
}

// CommentDel 删除评论
// ctx: 上下文
// params: 删除评论的请求参数
// 返回: 错误信息
func (s *CommentService) CommentDel(ctx *gin.Context, params *schema.CommentDelReq) (*schema.CommentDelRes, *errors.Err) {
	commentRepo, workRepo := repo.NewCommentRepo(), repo.NewWorkRepo()
	comment, err := commentRepo.FindById(ctx, params.CommentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find comment failed")
		return nil, errors.New(errors.ServerError)
	}
	if comment == nil || comment.IsDelete == 1 {
		return nil, errors.New(errors.CommentNotExist)
	}
	work, err := workRepo.FindById(ctx, comment.WorkID)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work failed")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return nil, errors.New(errors.WorkNotExist)
	}
	if !s.checkCommentAuth(work, comment, params.UserId) {
		return nil, errors.New(errors.NoCommentAuth)
	}

	// 删除评论本身
	if err := commentRepo.UpdateById(ctx, params.CommentId, map[string]interface{}{"is_delete": 1}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("delete comment failed")
		return nil, errors.New(errors.ServerError)
	}

	// 删除通知
	informService := NewInformService()
	if comment.ReplyCommentID > 0 {
		// 如果是回复评论，删除回复通知
		if err := informService.DelWorkReplyInform(ctx, work.RoleID, comment.WorkID, comment.ID); err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("del work reply inform failed")
		}
	} else {
		// 如果是普通评论，删除评论通知
		if err := informService.DelWorkCommentInform(ctx, work.RoleID, comment.WorkID, comment.ID); err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("del work comment inform failed")
		}
	}

	// 如果是二级评论，更新原评论的回复数
	if comment.ReplyCommentID > 0 {
		if err := commentRepo.UpdateById(ctx, comment.ReplyCommentID, map[string]interface{}{"reply_count": gorm.Expr("if(reply_count > 0, reply_count - 1, 0)")}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update reply count failed")
			return nil, errors.New(errors.ServerError)
		}
	}

	// 更新作品评论总数
	commentCount := 1
	if comment.ReplyCommentID <= 0 {
		// 如果是一级评论，需要加上其下的所有回复数
		commentCount += int(comment.ReplyCount)
	}

	if err := workRepo.UpdateById(ctx, comment.WorkID, map[string]interface{}{
		"comment_count": gorm.Expr("if(comment_count > ?, comment_count - ?, 0)", commentCount, commentCount),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work comment count failed")
		return nil, errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Incr(ctx, comment.WorkID, map[string]interface{}{
		"comment_count": -commentCount,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work comment count failed")
		return nil, errors.New(errors.ServerError)
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(comment.WorkID), map[string]interface{}{
		"comment_count": int32(work.CommentCount + work.FakeCommentCount - int32(commentCount)),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work comment count error")
	}

	util.LogYunying("l36_action_work_comment_del", map[string]interface{}{
		"work_id":    comment.WorkID,
		"user_id":    params.UserId,
		"role_id":    params.RoleId,
		"comment_id": params.CommentId,
	})
	return &schema.CommentDelRes{
		CommentId: params.CommentId,
		WorkId:    comment.WorkID,
	}, nil
}

// fillComments 填充评论信息
// ctx: 上下文
// comments: 评论列表
// userId: 用户ID
// fillSubComment: 是否填充子评论
// 返回: 填充后的评论列表和错误
func (s *CommentService) fillComments(ctx *gin.Context, comments []model.L36ActionWorkComment, userId int64, fillSubComment bool) ([]schema.CommentItem, error) {
	originCommentIds := []int64{}
	for _, comment := range comments {
		if comment.ReplyCount > 0 && comment.OriginCommentID == -1 {
			originCommentIds = append(originCommentIds, comment.ID)
		}
	}

	subCommentListMap := map[int64][]model.L36ActionWorkComment{}
	if fillSubComment && len(originCommentIds) > 0 {
		res, err := cache.NewCommentHotCommentCache().Gets(ctx, originCommentIds)
		if err != nil {
			return nil, err
		}
		for i, originId := range originCommentIds {
			subCommentListMap[originId] = res[i]
		}
	}
	subComments := lo.Flatten(lo.Values(subCommentListMap))

	commentIds, userIds, allComments := s.getRelatedIds(comments, subComments)

	// 使用wait.Group并行获取数据
	var wg wait.Group
	var users []model.L36ActionUser
	var roleDict map[int64]model.L36RoleInfo
	var likeMap map[int64]bool
	var userErr, likeErr, roleErr error

	// 并行获取用户信息
	wg.Start(func() {
		users, userErr = repo.NewUserRepo().FindByIds(ctx, userIds)
	})

	// 获取点赞信息的goroutine会在用户和角色信息获取后执行
	wg.Start(func() {
		// 并行获取点赞信息
		wg.Start(func() {
			likeMap, likeErr = repo.NewCommentLikeRepo().GetUserCommentLikeMap(ctx, userId, commentIds)
		})
	})

	// 并行获取角色信息
	wg.Start(func() {
		roleDict, roleErr = s.getCommentsRoleMap(ctx, users, allComments)
	})

	// 等待所有goroutine完成
	wg.Wait()

	// 检查错误
	if userErr != nil {
		log.LogWithContext(ctx).WithError(userErr).Error("get user info failed")
		return nil, userErr
	}
	if likeErr != nil {
		log.LogWithContext(ctx).WithError(likeErr).Error("get like info failed")
		return nil, likeErr
	}
	if roleErr != nil {
		log.LogWithContext(ctx).WithError(roleErr).Error("get role info failed")
		return nil, roleErr
	}

	userDict := lo.KeyBy(users, func(user model.L36ActionUser) int64 {
		return user.ID
	})
	userService := NewUserService()
	commentItems := []schema.CommentItem{}
	for _, comment := range comments {
		subComments := []schema.CommentItem{}
		if fillSubComment && subCommentListMap[comment.ID] != nil {
			for _, subComment := range subCommentListMap[comment.ID] {
				user := userDict[subComment.UserID]
				commentRole := roleDict[subComment.RoleID]
				replyUser := userDict[subComment.ReplyUserID]
				replyRole, ok := roleDict[subComment.ReplyRoleID]
				if !ok && replyUser.NameRoleID > 0 {
					replyRole = roleDict[replyUser.NameRoleID]
				}
				subItem := schema.CommentItem{
					ID:              subComment.ID,
					Content:         subComment.Content,
					Ctime:           subComment.Ctime,
					WorkId:          subComment.WorkID,
					OriginCommentId: subComment.OriginCommentID,
					ReplyCommentId:  subComment.ReplyCommentID,
					LikeCount:       int(subComment.LikeCount),
					ReplyCount:      int(subComment.ReplyCount),
					IsLiked:         likeMap[subComment.ID],
					User:            userService.fillUserInfo(&user, &commentRole, &commentRole),
					ReplyUser:       userService.fillUserInfo(&replyUser, &replyRole, &replyRole),
					RoleId:          subComment.RoleID,
				}
				subComments = append(subComments, subItem)
			}
		}

		user := userDict[comment.UserID]
		commentRole := roleDict[comment.RoleID]
		item := schema.CommentItem{
			ID:              comment.ID,
			Content:         comment.Content,
			Ctime:           comment.Ctime,
			LikeCount:       int(comment.LikeCount),
			WorkId:          comment.WorkID,
			OriginCommentId: comment.OriginCommentID,
			ReplyCommentId:  comment.ReplyCommentID,
			ReplyCount:      int(comment.ReplyCount),
			IsLiked:         likeMap[comment.ID],
			User:            userService.fillUserInfo(&user, &commentRole, &commentRole),
			ReplyList:       subComments,
			RoleId:          comment.RoleID,
		}
		commentItems = append(commentItems, item)
	}

	return commentItems, nil
}

func (*CommentService) getRelatedIds(comments []model.L36ActionWorkComment, subComments []model.L36ActionWorkComment) ([]int64, []int64, []model.L36ActionWorkComment) {
	var (
		commentIds  []int64
		userIds     []int64
		allComments []model.L36ActionWorkComment
	)

	for _, comment := range comments {
		if !lo.Contains(commentIds, comment.ID) {
			commentIds = append(commentIds, comment.ID)
		}
		if !lo.Contains(userIds, comment.UserID) {
			userIds = append(userIds, comment.UserID)
		}
		if comment.ReplyUserID > 0 && !lo.Contains(userIds, comment.ReplyUserID) {
			userIds = append(userIds, comment.ReplyUserID)
		}
		allComments = append(allComments, comment)
	}
	for _, subComment := range subComments {
		if !lo.Contains(commentIds, subComment.ID) {
			commentIds = append(commentIds, subComment.ID)
		}
		if !lo.Contains(userIds, subComment.UserID) {
			userIds = append(userIds, subComment.UserID)
		}
		if subComment.ReplyUserID > 0 && !lo.Contains(userIds, subComment.ReplyUserID) {
			userIds = append(userIds, subComment.ReplyUserID)
		}
		allComments = append(allComments, subComment)
	}
	return commentIds, userIds, allComments
}

// CommentList 获取评论列表
// ctx: 上下文
// params: 获取评论列表的请求参数
// 返回: 评论列表响应和错误信息
func (s *CommentService) CommentList(ctx *gin.Context, params *schema.CommentListReq) (*schema.CommentListRes, *errors.Err) {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work failed")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return nil, errors.New(errors.WorkNotExist)
	}
	records, err := repo.NewCommentRepo().ListWorkComments(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list work comments failed")
		return nil, errors.New(errors.ServerError)
	}
	list, err := s.fillComments(ctx, records, params.UserId, true)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill comments failed")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.CommentListRes{
		List: list,
	}, nil
}

// CommentReplyList 获取评论回复列表
// ctx: 上下文
// params: 获取评论回复列表的请求参数
// 返回: 评论回复列表响应和错误信息
func (s *CommentService) CommentReplyList(ctx *gin.Context, params *schema.CommentReplyListReq) (*schema.CommentReplyListRes, *errors.Err) {
	comment, err := repo.NewCommentRepo().FindById(ctx, params.CommentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find comment failed")
		return nil, errors.New(errors.ServerError)
	}
	if comment == nil || comment.IsDelete == 1 {
		return nil, errors.New(errors.CommentNotExist)
	}
	// 检查评论是否属于请求的作品
	if comment.WorkID != params.WorkId {
		return nil, errors.New(errors.CommentNotExist)
	}
	work, err := repo.NewWorkRepo().FindById(ctx, comment.WorkID)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work failed")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return nil, errors.New(errors.WorkNotExist)
	}
	records, err := repo.NewCommentRepo().ListReplyComments(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list reply comments failed")
		return nil, errors.New(errors.ServerError)
	}
	list, err := s.fillComments(ctx, records, params.UserId, false)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill comments failed")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.CommentReplyListRes{
		List: list,
	}, nil
}

// CommentLike 点赞评论
// ctx: 上下文
// params: 点赞评论的请求参数
// 返回: 错误信息
func (s *CommentService) CommentLike(ctx *gin.Context, params *schema.CommentLikeReq) *errors.Err {
	comment, commentErr := s.checkComment(ctx, params.CommentId)
	if commentErr != nil {
		return commentErr
	}

	work, err := repo.NewWorkRepo().FindById(ctx, comment.WorkID)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("find work failed")
		return errors.New(errors.ServerError)
	}
	if work == nil {
		return errors.New(errors.WorkNotExist)
	}

	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("find user failed")
		return errors.New(errors.ServerError)
	}
	if user == nil {
		return errors.New(errors.UserNotExist)
	}

	record, err := repo.NewCommentLikeRepo().FindOneByUserComment(ctx, params.UserId, params.CommentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find comment like failed")
		return errors.New(errors.ServerError)
	}
	if record != nil && record.IsDelete == 0 {
		return errors.New(errors.CommentLikeExist)
	}
	likeID, err := repo.NewCommentLikeRepo().Like(ctx, params.UserId, params.RoleId, params.CommentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("like comment failed")
		return errors.New(errors.ServerError)
	}
	if err := repo.NewCommentRepo().UpdateById(ctx, comment.ID, map[string]interface{}{"like_count": gorm.Expr("like_count + 1")}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update comment like count failed")
		return errors.New(errors.ServerError)
	}

	// 添加点赞通知
	informService := NewInformService()
	if err := informService.AddWorkCommentLikeInform(ctx, user.NameRoleID, comment.RoleID, comment.WorkID, work.Name, likeID, comment.Content); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("add work comment like inform failed")
	}

	// 如果是回复评论，刷新原评论的热门回复缓存
	if comment.OriginCommentID > 0 {
		_, err := cache.NewCommentHotCommentCache().Refresh(ctx, comment.OriginCommentID)
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("refresh comment hot comment cache failed")
		}
	}

	util.LogYunying("l36_action_work_comment_like", map[string]interface{}{
		"work_id":    comment.WorkID,
		"user_id":    params.UserId,
		"role_id":    params.RoleId,
		"comment_id": params.CommentId,
	})
	return nil
}

// CommentCancelLike 取消点赞评论
// ctx: 上下文
// params: 取消点赞评论的请求参数
// 返回: 错误信息
func (s *CommentService) CommentCancelLike(ctx *gin.Context, params *schema.CommentCancelLikeReq) *errors.Err {
	record, err := repo.NewCommentLikeRepo().FindOneByUserComment(ctx, params.UserId, params.CommentId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find comment like failed")
		return errors.New(errors.ServerError)
	}
	if record == nil || record.IsDelete == 1 {
		return errors.New(errors.CommentLikeNotExist)
	}
	if err := repo.NewCommentLikeRepo().UpdateById(ctx, record.ID, map[string]interface{}{"is_delete": 1}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("cancel like comment failed")
		return errors.New(errors.ServerError)
	}
	if err := repo.NewCommentRepo().UpdateById(ctx, record.CommentID, map[string]interface{}{"like_count": gorm.Expr("if(like_count > 0, like_count - 1, 0)")}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update comment like count failed")
		return errors.New(errors.ServerError)
	}

	// 删除点赞通知
	comment, err := repo.NewCommentRepo().FindById(ctx, record.CommentID)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("find comment failed")
	} else {
		informService := NewInformService()
		if err := informService.DelWorkCommentLikeInform(ctx, comment.RoleID, comment.WorkID, record.ID); err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("del work comment like inform failed")
		}
	}

	// 如果是回复评论，刷新原评论的热门回复缓存
	if comment.OriginCommentID > 0 {
		_, err := cache.NewCommentHotCommentCache().Refresh(ctx, comment.OriginCommentID)
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Warn("refresh comment hot comment cache failed")
		}
	}

	util.LogYunying("l36_action_work_comment_cancel_like", map[string]interface{}{
		"user_id":    params.UserId,
		"role_id":    params.RoleId,
		"comment_id": params.CommentId,
	})
	return nil
}
