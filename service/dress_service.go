package service

import (
	"strconv"

	"app/constant"

	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/external"
	"app/schema"
	"app/util"
	"app/util/log"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type DressService struct{}

func NewDressService() *DressService {
	return &DressService{}
}

func (s *DressService) fillOutfitInfo(work model.L36FashionWork, role *model.L36RoleInfo) schema.FashionInfo {
	if role == nil {
		role = &model.L36RoleInfo{}
	}
	return schema.FashionInfo{
		ID:        work.ID,
		Name:      work.Name,
		Type:      int(work.Type),
		SubType:   int(work.Subtype),
		FashionID: int(work.FashionID),
		Cover:     work.PreviewURL,
		Property:  work.Property,
		RoleId:    role.RoleID,
		RoleName:  role.RoleName,
	}
}

func (s *DressService) isShowFashionWork(fashionWork model.L36FashionWork) bool {
	if fashionWork.FashionID <= 0 {
		return false
	}
	if fashionWork.Type == constant.FashionTypeHair {
		return true
	}
	if fashionWork.Type == constant.FashionTypeFashion {
		return true
	}
	return false
}

func (s *DressService) DressListRecommend(ctx *gin.Context, params *schema.DressListRecommendReq) (*schema.DressListRecommendRes, *errors.Err) {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("find work by id error")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return nil, errors.New(errors.WorkNotExist)
	}

	fashionWorks, err := s.getRecommendFashionWorks(ctx, params.RoleId, work)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("get recommend fashion work error")
		return &schema.DressListRecommendRes{
			List: []schema.DressInfo{},
		}, nil
	}
	fashionRoleIds := lo.Map(fashionWorks, func(fashionWork model.L36FashionWork, _ int) int64 {
		return fashionWork.RoleID
	})
	outfitRelatedWorkMap, err := repo.GetFashionOutfitRelatedWorkRepo().FindMapByWorkIds(ctx, lo.Map(fashionWorks, func(fashionWork model.L36FashionWork, _ int) int64 {
		return fashionWork.ID
	}))
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("get recommend fashion work error")
		return &schema.DressListRecommendRes{
			List: []schema.DressInfo{},
		}, nil
	}
	outfitRelatedWorks := lo.Flatten(lo.Values(outfitRelatedWorkMap))
	relatedWorkIds := lo.Map(outfitRelatedWorks, func(relatedWork model.L36FashionOutfitRelatedWork, _ int) int64 {
		return relatedWork.RelatedWorkID
	})
	relatedFashionWorks, err := repo.GetFashionWorkRepo().FindNormalByIds(ctx, relatedWorkIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("get recommend fashion work error")
		return &schema.DressListRecommendRes{
			List: []schema.DressInfo{},
		}, nil
	}
	relatedFashionWorkMap := make(map[int64]model.L36FashionWork)
	for _, relatedFashionWork := range relatedFashionWorks {
		relatedFashionWorkMap[relatedFashionWork.ID] = relatedFashionWork
		fashionRoleIds = append(fashionRoleIds, relatedFashionWork.RoleID)
	}
	fashionRoleIds = lo.Uniq(fashionRoleIds)
	fashionRoles, err := repo.GetOnlineRoleRepo().FindByRoleIds(ctx, fashionRoleIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("get recommend fashion work error")
		return &schema.DressListRecommendRes{
			List: []schema.DressInfo{},
		}, nil
	}
	fashionRoleMap := lo.KeyBy(fashionRoles, func(fashionRole model.L36RoleInfo) int64 {
		return fashionRole.RoleID
	})
	dressInfos := lo.Map(fashionWorks, func(fashionWork model.L36FashionWork, _ int) schema.DressInfo {
		role := fashionRoleMap[fashionWork.RoleID]
		dressInfo := s.fillOutfitInfo(fashionWork, &role)
		outfits := []schema.FashionInfo{}
		for _, relatedWork := range outfitRelatedWorkMap[fashionWork.ID] {
			work, ok := relatedFashionWorkMap[relatedWork.RelatedWorkID]
			if !ok {
				continue
			}
			if !s.isShowFashionWork(work) {
				continue
			}
			role := fashionRoleMap[work.RoleID]
			outfits = append(outfits, s.fillOutfitInfo(work, &role))
		}
		return schema.DressInfo{
			FashionInfo:         dressInfo,
			RelatedFashionWorks: outfits,
		}
	})
	return &schema.DressListRecommendRes{
		List: dressInfos,
	}, nil
}

func (*DressService) getRecommendFashionWorks(ctx *gin.Context, roleId int64, work *model.L36ActionWork) ([]model.L36FashionWork, error) {
	role, err := repo.NewRoleRepo().FindOneByRoleId(ctx, roleId)
	if err != nil {
		return nil, err
	}
	var fashionUser *model.L36FashionUser
	if role != nil {
		fashionUser, err = repo.NewFashionUserRepo().FindOneByAid(ctx, role.Aid)
		if err != nil {
			return nil, err
		}
	}
	images := util.JsonToStringArray(work.Images)
	image := ""
	if len(images) > 0 {
		image = images[0]
	}
	stationId := ""
	if fashionUser != nil {
		stationId = strconv.FormatInt(fashionUser.ID, 10)
	}
	gender := "230"
	if role != nil && role.Gender == 1 {
		gender = "233"
	}
	recommend, err := external.GetFashionRecommend(ctx, &external.FashionRecommendParams{
		Summary:     work.Summary,
		ResampleUrl: work.ResampleURL,
		ImageUrl:    image,
		Mode:        "official_station",
		Property:    work.Property,
		Video:       work.Video,
		PlayerId:    strconv.FormatInt(roleId, 10),
		StationId:   stationId,
		Role:        gender,
	})
	if err != nil {
		return nil, err
	}

	fashionWorkIds, ok := recommend.Data.([]interface{})
	if !ok {
		return []model.L36FashionWork{}, nil
	}
	log.LogWithContext(ctx).WithField("fashionWorkIds", fashionWorkIds).Info("get recommend fashion works")
	fashionWorkRepo := repo.GetFashionWorkRepo()
	realFashionWorkIds := lo.Map(fashionWorkIds, func(fashionWorkId interface{}, _ int) int {
		id, ok := fashionWorkId.(string)
		if !ok {
			return 0
		}
		fashionWorkIdInt, err := strconv.Atoi(id)
		if err != nil {
			return 0
		}
		return fashionWorkIdInt
	})
	fashionWorks, err := fashionWorkRepo.FindNormalByIds(ctx, realFashionWorkIds)
	if err != nil {
		return nil, err
	}

	// 按照推荐顺序重新排序结果
	fashionWorkMap := lo.KeyBy(fashionWorks, func(work model.L36FashionWork) int {
		return int(work.ID)
	})
	orderedFashionWorks := make([]model.L36FashionWork, 0, len(realFashionWorkIds))
	for _, id := range realFashionWorkIds {
		if id > 0 {
			if work, exists := fashionWorkMap[id]; exists {
				orderedFashionWorks = append(orderedFashionWorks, work)
			}
		}
	}

	return orderedFashionWorks, nil
}
