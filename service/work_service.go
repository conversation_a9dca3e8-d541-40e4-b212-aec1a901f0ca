package service

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"sync"
	"time"

	"app/config"
	"app/conn"
	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/external"
	"app/schema"
	"app/util"
	"app/util/lock"
	"app/util/log"
	"app/util/wait"

	filepicker_tools "ccc-gitlab.leihuo.netease.com/pkgo/filepicker-tools"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type WorkService struct{}

func NewWorkService() *WorkService {
	return &WorkService{}
}

// checkMedia 检查媒体文件是否有效
// 参数:
//   - video: 视频链接
//   - images: 图片链接列表
//   - resampleUrl: 重采样链接
//
// 返回:
//   - 错误信息
func (s *WorkService) checkMedia(video string, images []string, resampleUrl string) *errors.Err {
	if video == "" && len(images) == 0 || config.C.Test.TestEnv {
		return nil
	}
	fpService := NewFPService()
	if video != "" && !fpService.IsFpFile(video) {
		return errors.New(errors.InvalidLink)
	}
	for _, image := range images {
		if !fpService.IsFpFile(image) {
			return errors.New(errors.InvalidLink)
		}
	}
	if resampleUrl != "" && !fpService.IsFpFile(resampleUrl) {
		return errors.New(errors.InvalidLink)
	}
	return nil
}

type AuditInfo struct {
	Images []int `json:"images"`
	Video  int   `json:"video"`
	Cover  int   `json:"cover"`
}

// GetAuditStatusByAuditInfo 根据审核信息获取审核状态
// 参数:
//   - auditInfo: 审核信息
//
// 返回:
//   - 审核状态
func (s *WorkService) GetAuditStatusByAuditInfo(auditInfo *AuditInfo) constant.FpReviewStatus {
	if auditInfo.Video == int(constant.FpReviewStatusReject) {
		return constant.FpReviewStatusReject
	}
	fpService := NewFPService()
	auditStatus := constant.FpReviewStatusPass
	if fpService.IsReviewingStatus(constant.FpReviewStatus(auditInfo.Video)) {
		auditStatus = constant.FpReviewStatusReviewing
	}
	for _, image := range auditInfo.Images {
		if image == int(constant.FpReviewStatusReject) {
			return constant.FpReviewStatusReject
		}
		if fpService.IsReviewingStatus(constant.FpReviewStatus(image)) {
			auditStatus = constant.FpReviewStatusReviewing
		}
	}
	if auditInfo.Cover == int(constant.FpReviewStatusReject) {
		return constant.FpReviewStatusReject
	}
	if fpService.IsReviewingStatus(constant.FpReviewStatus(auditInfo.Cover)) {
		auditStatus = constant.FpReviewStatusReviewing
	}
	return auditStatus
}

// getAuditStatus 获取审核状态
// 参数:
//   - ctx: gin上下文
//   - video: 视频链接
//   - images: 图片链接列表
//
// 返回:
//   - 审核状态
//   - 审核信息
//   - 缺失的审核媒体
//   - 错误信息
func (s *WorkService) getAuditStatus(ctx *gin.Context, video string, images []string, cover string) (auditStatus int, auditInfo *AuditInfo, missReviewMedias []model.L36ActionReviewMedium, err error) {
	fpService := NewFPService()

	auditInfo = &AuditInfo{
		Images: make([]int, len(images)),
		Video:  int(constant.FpReviewStatusPass),
		Cover:  int(constant.FpReviewStatusPass),
	}

	if config.C.Filepicker.SkipAudit {
		return int(constant.FpReviewStatusPass), auditInfo, missReviewMedias, nil
	}

	fileIds := make([]string, 0)
	if video != "" {
		fileIds = append(fileIds, fpService.GetFpFileId(video))
	}
	for _, image := range images {
		fileIds = append(fileIds, fpService.GetFpFileId(image))
	}

	reviewMedias, err := repo.NewReviewMediaRepo().FindByFileIds(ctx, fileIds)
	if err != nil {
		return int(constant.FpReviewStatusReviewing), nil, nil, err
	}

	reviewMediaMap := lo.KeyBy(reviewMedias, func(reviewMedia model.L36ActionReviewMedium) string {
		return reviewMedia.FileID
	})

	auditStatusMap := make(map[string]int)

	// 使用wait.Group并发获取文件审核状态
	if len(fileIds) > 0 {
		var wg wait.Group
		var mutex sync.Mutex

		for _, fileId := range fileIds {
			if _, ok := reviewMediaMap[fileId]; !ok {
				// 对于每个缺失的审核媒体，启动一个goroutine获取状态
				fileIdCopy := fileId // 创建副本以避免闭包问题
				wg.Start(func() {
					url := fpService.GetFpUrlByFileId(fileIdCopy)
					reviewStatus, err := fpService.GetFpFileReviewStatus(ctx, url)
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Warn("get file review status error: %s", fileIdCopy)
						return
					}

					mutex.Lock()
					defer mutex.Unlock()

					missReviewMedias = append(missReviewMedias, model.L36ActionReviewMedium{
						FileID:      fileIdCopy,
						AuditStatus: int8(reviewStatus),
					})
					auditStatusMap[fileIdCopy] = int(reviewStatus)
				})
			} else {
				// 已有的审核媒体直接使用
				auditStatusMap[fileId] = int(reviewMediaMap[fileId].AuditStatus)
			}
		}

		wg.Wait()
	}

	if video != "" {
		videoAuditStatus := auditStatusMap[fpService.GetFpFileId(video)]
		auditInfo.Video = videoAuditStatus
	}

	if cover != "" {
		coverAuditStatus := auditStatusMap[fpService.GetFpFileId(cover)]
		auditInfo.Cover = coverAuditStatus
	}

	for _, image := range images {
		imageAuditStatus := auditStatusMap[fpService.GetFpFileId(image)]
		auditInfo.Images = append(auditInfo.Images, imageAuditStatus)
	}

	auditStatus = int(s.GetAuditStatusByAuditInfo(auditInfo))
	return auditStatus, auditInfo, missReviewMedias, nil
}

// EncodeContents 编码内容类型
// 参数:
//   - contents: 内容类型列表
//
// 返回:
//   - 编码后的内容类型
func (s *WorkService) EncodeContents(contents []int) int {
	// 按位或
	result := 0
	for _, content := range contents {
		result |= 1 << (content - 1)
	}
	return result
}

// DecodeContents 解码内容类型
// 参数:
//   - contents: 编码后的内容类型
//
// 返回:
//   - 内容类型列表
func (s *WorkService) DecodeContents(contents int) []int {
	// 按位与
	result := make([]int, 0)
	if contents == 0 {
		return result
	}
	for i := 1; i <= 5; i++ {
		if contents&(1<<(i-1)) != 0 {
			result = append(result, i)
		}
	}
	return result
}

// updateTopicHot更新话题热度
func (s *WorkService) updateTopicHot(ctx *gin.Context, topicIds []int64, hot int) error {
	if len(topicIds) == 0 {
		return nil
	}
	if err := repo.NewTopicRepo().UpdateByIds(ctx, topicIds, map[string]interface{}{
		"hot": gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update topic hot error")
		return err // 返回错误以便上层处理
	}
	return nil
}

func (s *WorkService) checkCreateWorkParams(params *schema.WorkCreateReq) *errors.Err {
	if params.Type == int(constant.WorkTypePureVideo) {
		if params.Video == "" && len(params.Images) == 0 {
			return errors.New(errors.VideoAndImagesIsEmpty)
		}
		return nil
	}
	if params.Property == "" {
		return errors.New(errors.PropertyIsEmpty)
	}
	if params.ResampleUrl == "" {
		return errors.New(errors.ResampleUrlIsEmpty)
	}
	return nil
}

func (s *WorkService) checkUpdateWorkParams(params *schema.WorkUpdateReq) *errors.Err {
	if params.Type == int(constant.WorkTypePureVideo) {
		if params.Video == "" && len(params.Images) == 0 {
			return errors.New(errors.VideoAndImagesIsEmpty)
		}
	}
	return nil
}

func (s *WorkService) checkTexts(ctx *gin.Context, name string, summary string) *errors.Err {
	wg := wait.Group{}
	var summaryError error
	var nameError error
	wg.Start(func() {
		if err := s.checkText(ctx, name); err != nil {
			nameError = err
		}
	})
	wg.Start(func() {
		if err := s.checkText(ctx, summary); err != nil {
			summaryError = err
		}
	})
	wg.Wait()
	if nameError != nil {
		log.LogWithContext(ctx).WithError(nameError).Info("check name failed")
		return errors.New(errors.InvalidWorkName)
	}
	if summaryError != nil {
		log.LogWithContext(ctx).WithError(summaryError).Info("check summary failed")
		return errors.New(errors.InvalidWorkSummary)
	}
	return nil
}

func (s *WorkService) checkText(ctx context.Context, text string) error {
	if text == "" {
		return nil
	}
	envsdkService := NewEnvsdkService()
	if !envsdkService.ReviewWords(ctx, text) {
		return fmt.Errorf("text is invalid: %s", text)
	}
	return nil
}

func (s *WorkService) saveWorkTopics(ctx *gin.Context, work *model.L36ActionWork, topicNames []string) (topicIds []int64, err error) {
	if len(topicNames) == 0 {
		return topicIds, nil
	}
	topics, err := repo.NewTopicRepo().FindOrInitByNames(ctx, topicNames)
	if err != nil {
		return topicIds, err
	}
	topicIds = lo.Map(topics, func(topic model.L36ActionTopic, i int) int64 {
		return topic.ID
	})
	workTopics := lo.Map(topics, func(topic model.L36ActionTopic, i int) model.L36ActionWorkTopic {
		return model.L36ActionWorkTopic{
			WorkID:  work.ID,
			TopicID: topic.ID,
		}
	})
	if err := repo.NewWorkTopicRepo().CreateBatch(ctx, workTopics); err != nil {
		return topicIds, fmt.Errorf("batch save work topic error: %v", err)
	}
	if err := s.updateTopicHot(ctx, topicIds, 1); err != nil {
		return topicIds, fmt.Errorf("update topic hot error: %v", err)
	}
	return topicIds, nil
}

// WorkCreate 创建作品
// 参数:
//   - ctx: gin上下文
//   - params: 创建作品请求参数
//
// 返回:
//   - 创建作品响应
//   - 错误信息
func (s *WorkService) WorkCreate(ctx *gin.Context, params *schema.WorkCreateReq) (*schema.WorkCreateRes, *errors.Err) {
	if err := s.checkMedia(params.Video, params.Images, params.ResampleUrl); err != nil {
		return nil, err
	}

	if err := s.checkCreateWorkParams(params); err != nil {
		return nil, err
	}

	if err := s.checkTexts(ctx, params.Name, params.Summary); err != nil {
		return nil, err
	}

	user, userErr := repo.NewUserRepo().FindById(ctx, params.UserId)

	if userErr != nil {
		log.LogWithContext(ctx).WithError(userErr).Error("find user error")
		return nil, errors.New(errors.ServerError)
	}

	if user == nil {
		return nil, errors.New(errors.UserNotExist)
	}

	if user.WorkCount >= int32(config.C.Work.CreateLimit) {
		return nil, errors.New(errors.WorkCreateLimit)
	}

	if isBanned, err := repo.NewGmCreatorBanRepo().IsBanned(ctx, user.ID, constant.BanTypeWork); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("check creator banned error")
		return nil, errors.New(errors.ServerError)
	} else if isBanned {
		return nil, errors.New(errors.CreatorBannedWork)
	}

	auditStatus, auditInfo, missAuditMedias, err := s.getAuditStatus(ctx, params.Video, params.Images, params.Cover)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get audit status error")
		return nil, errors.New(errors.ServerError)
	}

	property, err := s.DeepCloneProperty(ctx, params.RoleId, params.Property)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("deep clone property error")
		return nil, errors.New(errors.CloneFileError)
	}

	record := &model.L36ActionWork{
		UserID:      user.ID,
		RoleID:      params.RoleId,
		Type:        int32(params.Type),
		Name:        util.ReplaceLineBreak(params.Name),
		Summary:     params.Summary,
		Cover:       params.Cover,
		Video:       params.Video,
		Scene:       params.Scene,
		Gender:      int8(params.Gender),
		Images:      util.JsonStringify(params.Images),
		Property:    property,
		Contents:    int32(s.EncodeContents(params.Contents)),
		Visibility:  int8(params.Visibility),
		AuditStatus: int8(auditStatus),
		AuditInfo:   util.JsonStringify(auditInfo),
		ResampleURL: params.ResampleUrl,
		RawProperty: params.Property,
	}

	if err := repo.NewWorkRepo().Create(ctx, record); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create work error")
		return nil, errors.New(errors.ServerError)
	}

	if len(missAuditMedias) > 0 {
		missAuditMedias = lo.Map(missAuditMedias, func(reviewMedia model.L36ActionReviewMedium, i int) model.L36ActionReviewMedium {
			reviewMedia.WorkID = record.ID
			return reviewMedia
		})
		if err := repo.NewReviewMediaRepo().CreateBatch(ctx, missAuditMedias); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("batch save review media error")
			return nil, errors.New(errors.ServerError)
		}
	}

	// 增加用户经验值
	isReachLimit, err := repo.NewExpLimitSubRepo().IsReachUserDesignerExpLimit(ctx, user.ID, user.ID, "work_count")
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("incr user work count error")
		return nil, errors.New(errors.ServerError)
	}

	exp := 0
	if !isReachLimit {
		exp = config.C.Work.CreateExp
	}

	if err := repo.NewUserRepo().UpdateById(ctx, user.ID, map[string]interface{}{
		"work_count": gorm.Expr("work_count + ?", 1),
		"exp":        gorm.Expr("exp + ?", exp),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user work count error")
		return nil, errors.New(errors.ServerError)
	}

	// 保存作品话题
	topicNames := NewTopicService().GetTopicNames(params.Summary)
	topicIds, err := s.saveWorkTopics(ctx, record, topicNames)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get topic ids error")
		return nil, errors.New(errors.ServerError)
	}
	// 同步到 Es
	if err := es.NewWorkEsRepo().IndexWork(ctx, record, topicIds); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("index work error")
	}

	// 运营日志
	util.LogYunying("l36_action_work_create", map[string]interface{}{
		"work_id":    record.ID,
		"user_id":    user.ID,
		"role_id":    record.RoleID,
		"type":       record.Type,
		"contents":   s.DecodeContents(int(record.Contents)),
		"visibility": record.Visibility,
		"cover":      record.Cover,
		"video":      record.Video,
		"images":     record.Images,
		"property":   record.Property,
		"topics":     topicNames,
		"name":       record.Name,
		"summary":    record.Summary,
	})

	return &schema.WorkCreateRes{
		WorkId: record.ID,
	}, nil
}

// downloadProperty 下载并解析 property 内容
func (s *WorkService) downloadProperty(ctx context.Context, property string) (map[string]interface{}, error) {
	var propertyMap map[string]interface{}
	_, err := kit.HttpGet2(ctx, &propertyMap, property, conn.GetClient())
	if err != nil {
		return nil, fmt.Errorf("download property error: %w", err)
	}
	return propertyMap, nil
}

// findNeedProcessUrls 查找需要处理的 fuxi fp 地址和 fp-action 地址
func (s *WorkService) findNeedProcessUrls(propertyMap map[string]interface{}) (map[string]string, map[string]string) {
	fpService := NewFPService()
	needProcessFuxiMap := map[string]string{}
	needProcessFpActionMap := map[string]string{}
	for k, v := range propertyMap {
		if k == "appearStr" {
			continue
		}
		vStr, ok := v.(string)
		if !ok {
			continue
		}
		if fpService.IsFuxiFpUrl(vStr) {
			needProcessFuxiMap[k] = vStr
		} else if fpService.IsActionFpUrl(vStr) {
			needProcessFpActionMap[k] = vStr
		}
	}
	return needProcessFuxiMap, needProcessFpActionMap
}

// cloneFuxiFiles 并发克隆 fuxi 文件
func (s *WorkService) cloneFuxiFiles(ctx context.Context, needProcessMap map[string]string, uid string) (map[string]string, error) {
	var mutex sync.Mutex
	processedMap := map[string]string{}
	group := sync.WaitGroup{}
	var uploadErr error

	// 使用信号量控制最大并发数
	sem := make(chan struct{}, 5)

	for k, v := range needProcessMap {
		group.Add(1)
		go func(k string, v string) {
			defer group.Done()
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量

			clonedUrl, err := external.CloneFile(ctx, &external.FilePickerCloneReq{
				URL: v,
				UID: uid,
			})
			if err != nil {
				log.LogWithContext(ctx).WithError(err).Error("clone file error")
				mutex.Lock()
				uploadErr = fmt.Errorf("clone file %s error: %w", v, err)
				mutex.Unlock()
				return
			}
			mutex.Lock()
			processedMap[k] = clonedUrl.CloneURL
			mutex.Unlock()
		}(k, v)
	}
	group.Wait()

	if uploadErr != nil {
		return nil, uploadErr
	}
	return processedMap, nil
}

func (s *WorkService) cloneActionFiles(ctx context.Context, needProcessMap map[string]string, uid string) (map[string]string, error) {
	var mutex sync.Mutex
	processedMap := map[string]string{}
	group := sync.WaitGroup{}
	var uploadErr error

	// 使用信号量控制最大并发数
	sem := make(chan struct{}, 5)

	fpService := NewFPService()
	for k, v := range needProcessMap {
		group.Add(1)
		go func(k string, v string) {
			defer group.Done()
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量

			cloneUrl, err := fpService.CloneFile(ctx, v, &filepicker_tools.ExtraPolicy{
				UID: &uid,
			})
			if err != nil {
				log.LogWithContext(ctx).WithError(err).Error("clone file error")
				mutex.Lock()
				uploadErr = fmt.Errorf("clone file %s error: %w", v, err)
				mutex.Unlock()
				return
			}
			mutex.Lock()
			processedMap[k] = cloneUrl
			mutex.Unlock()
		}(k, v)
	}
	group.Wait()

	if uploadErr != nil {
		return nil, uploadErr
	}
	return processedMap, nil
}

// deleteClonedFuxiFiles 并发删除已克隆的 fuxi 文件
func (s *WorkService) deleteClonedFuxiFiles(ctx context.Context, processedMap map[string]string) {
	group := sync.WaitGroup{}

	// 使用信号量控制最大并发数
	sem := make(chan struct{}, 5)

	for _, v := range processedMap {
		group.Add(1)
		go func(v string) {
			defer group.Done()
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量

			if err := external.DeleteFile(ctx, &external.FilePickerDeleteReq{
				URL: v,
			}); err != nil {
				log.LogWithContext(ctx).WithError(err).Error("delete cloned file error")
			}
		}(v)
	}
	group.Wait()
}

// deleteClonedFpActionFiles 并发删除已克隆的 fp-action 文件
func (s *WorkService) deleteClonedActionFiles(ctx context.Context, processedMap map[string]string) {
	group := sync.WaitGroup{}

	// 使用信号量控制最大并发数
	sem := make(chan struct{}, 5)

	fpService := NewFPService()
	for _, v := range processedMap {
		group.Add(1)
		go func(v string) {
			defer group.Done()
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量

			if err := fpService.DelFile(ctx, v); err != nil {
				log.LogWithContext(ctx).WithError(err).Error("delete cloned file error")
			}
		}(v)
	}
	group.Wait()
}

// uploadProcessedProperty 上传处理后的 property
func (s *WorkService) uploadProcessedProperty(ctx context.Context, propertyMap map[string]interface{}, fuxiProcessedMap map[string]string, actionProcessedMap map[string]string, uid string) (string, error) {
	fpService := NewFPService()
	for k, v := range fuxiProcessedMap {
		propertyMap[k] = v
	}
	for k, v := range actionProcessedMap {
		propertyMap[k] = v
	}
	uploadUrl, err := fpService.UploadPassContent(ctx, util.JsonStringify(propertyMap), uid)
	if err != nil {
		return "", fmt.Errorf("upload property error: %w", err)
	}
	return uploadUrl, nil
}

func (s *WorkService) DeepCloneProperty(ctx context.Context, roleId int64, property string) (string, error) {
	if !config.C.Work.ProcessRawProperty || property == "" {
		return property, nil
	}

	// 下载并解析 property
	propertyMap, err := s.downloadProperty(ctx, property)
	if err != nil {
		return "", err
	}

	// 查找需要处理的 fuxi fp 地址
	needProcessFuxiMap, needProcessActionMap := s.findNeedProcessUrls(propertyMap)
	if len(needProcessFuxiMap) == 0 && len(needProcessActionMap) == 0 {
		return property, nil
	}

	// 并发克隆文件
	uid := strconv.FormatInt(roleId, 10)
	processedFuxiMap, err := s.cloneFuxiFiles(ctx, needProcessFuxiMap, uid)
	if err != nil {
		// 克隆失败时删除已克隆的文件
		s.deleteClonedFuxiFiles(ctx, processedFuxiMap)
		return "", fmt.Errorf("clone fuxi file error: %w", err)
	}
	processedActionMap, err := s.cloneActionFiles(ctx, needProcessActionMap, uid)
	if err != nil {
		// 克隆失败时删除已克隆的文件
		s.deleteClonedActionFiles(ctx, needProcessActionMap)
		return "", fmt.Errorf("clone action file error: %w", err)
	}

	// 上传处理后的 property
	uploadUrl, err := s.uploadProcessedProperty(ctx, propertyMap, processedFuxiMap, processedActionMap, uid)
	if err != nil {
		// 上传失败时删除已克隆的文件
		s.deleteClonedFuxiFiles(ctx, processedFuxiMap)
		s.deleteClonedActionFiles(ctx, processedActionMap)
		return "", err
	}

	return uploadUrl, nil
}

func (s *WorkService) isWorkMediaUpdated(video string, images []string, cover string, work *model.L36ActionWork) bool {
	if work.Video != video {
		return true
	}
	if work.Cover != cover {
		return true
	}
	if len(work.Images) != len(images) {
		return true
	}
	diff, _ := lo.Difference(util.JsonParseStrArray(work.Images), images)
	return len(diff) > 0
}

func (s *WorkService) updateWorkTopics(ctx *gin.Context, work *model.L36ActionWork, summary string) (topicIds []int64, err error) {
	oldTopicIds, err := repo.NewWorkTopicRepo().FindByWorkId(ctx, work.ID)
	if err != nil {
		return nil, fmt.Errorf("find work %d topic failed: %w", work.ID, err)
	}
	if summary == work.Summary {
		return oldTopicIds, nil
	}
	newTopicNames := NewTopicService().GetTopicNames(summary)
	newTopics, err := repo.NewTopicRepo().FindOrInitByNames(ctx, newTopicNames)
	if err != nil {
		return nil, fmt.Errorf("find or init topic error: %w", err)
	}
	newTopicIds := lo.Map(newTopics, func(topic model.L36ActionTopic, i int) int64 {
		return topic.ID
	})
	removedTopicIds, addedTopicIds := lo.Difference(oldTopicIds, newTopicIds)
	if len(removedTopicIds) == 0 && len(addedTopicIds) == 0 {
		return newTopicIds, nil
	}
	if err := repo.NewWorkTopicRepo().UpdateWorkTopics(ctx, work.ID, newTopicIds); err != nil {
		return newTopicIds, fmt.Errorf("update work %d topic failed: %w", work.ID, err)
	}
	if err := s.updateTopicHot(ctx, addedTopicIds, 1); err != nil {
		return newTopicIds, fmt.Errorf("update added topic hot error: %w", err)
	}
	if err := s.updateTopicHot(ctx, removedTopicIds, -1); err != nil {
		return newTopicIds, fmt.Errorf("update removed topic hot error: %w", err)
	}
	return newTopicIds, nil
}

func (s *WorkService) WorkUpdate(ctx *gin.Context, params *schema.WorkUpdateReq) (*schema.WorkUpdateRes, *errors.Err) {
	if err := s.checkMedia(params.Video, params.Images, ""); err != nil {
		return nil, err
	}

	if err := s.checkUpdateWorkParams(params); err != nil {
		return nil, err
	}

	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return nil, errors.New(errors.WorkNotExist)
	}

	// 复制一份 work 用于更新
	newWork := *work
	if params.Name != work.Name || params.Summary != work.Summary {
		if err := s.checkTexts(ctx, params.Name, params.Summary); err != nil {
			return nil, err
		}
	}

	// 检查图片和视频是否有变化，如果有变化则重新审核
	if work.UserID != params.UserId {
		return nil, errors.New(errors.WorkNoAuth)
	}
	updateMap := map[string]interface{}{}
	if params.Type != int(work.Type) {
		newWork.Type = int32(params.Type)
		updateMap["type"] = params.Type
	}
	if params.Scene != work.Scene {
		newWork.Scene = params.Scene
		updateMap["scene"] = params.Scene
	}
	if params.Name != work.Name {
		newWork.Name = params.Name
		updateMap["name"] = params.Name
	}
	if params.Cover != work.Cover {
		newWork.Cover = params.Cover
		updateMap["cover"] = params.Cover
	}
	if params.Summary != work.Summary {
		newWork.Summary = params.Summary
		updateMap["summary"] = params.Summary
	}
	if params.Visibility != int(work.Visibility) {
		newWork.Visibility = int8(params.Visibility)
		updateMap["visibility"] = params.Visibility
	}

	newTopicIds, err := s.updateWorkTopics(ctx, work, params.Summary)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work topic error")
		return nil, errors.New(errors.ServerError)
	}
	if s.isWorkMediaUpdated(params.Video, params.Images, params.Cover, work) {
		auditStatus, auditInfo, missAuditMedias, err := s.getAuditStatus(ctx, params.Video, params.Images, params.Cover)
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Error("get audit status error")
			return nil, errors.New(errors.ServerError)
		}
		if params.Video != work.Video {
			updateMap["video"] = params.Video
			newWork.Video = params.Video
		}
		oldImages := util.JsonParseStrArray(work.Images)
		diff1, _ := lo.Difference(oldImages, params.Images)
		diff2, _ := lo.Difference(params.Images, oldImages)
		if len(diff1) > 0 || len(diff2) > 0 {
			updateMap["images"] = util.JsonStringify(params.Images)
			newWork.Images = util.JsonStringify(params.Images)
		}
		updateMap["audit_status"] = auditStatus
		updateMap["audit_info"] = util.JsonStringify(auditInfo)
		newWork.AuditStatus = int8(auditStatus)
		newWork.AuditInfo = util.JsonStringify(auditInfo)
		if len(missAuditMedias) > 0 {
			missAuditMedias = lo.Map(missAuditMedias, func(reviewMedia model.L36ActionReviewMedium, i int) model.L36ActionReviewMedium {
				reviewMedia.WorkID = work.ID
				return reviewMedia
			})
			if err := repo.NewReviewMediaRepo().CreateBatch(ctx, missAuditMedias); err != nil {
				log.LogWithContext(ctx).WithError(err).Error("batch save review media error")
			}
		}
	}

	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, updateMap); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work error")
		return nil, errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Update(ctx, work.ID, updateMap)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work cache error")
	}

	// 处理 ES 相关逻辑
	if err := es.NewWorkEsRepo().IndexWork(ctx, &newWork, newTopicIds); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("index work error")
	}

	util.LogYunying("l36_action_work_update", map[string]interface{}{
		"work_id":    work.ID,
		"user_id":    work.UserID,
		"role_id":    work.RoleID,
		"update_map": updateMap,
	})

	return &schema.WorkUpdateRes{
		WorkId: work.ID,
	}, nil
}

func (s *WorkService) WorkDel(ctx *gin.Context, params *schema.WorkDelReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}
	if work.UserID != params.UserId {
		return errors.New(errors.WorkNoAuth)
	}
	if err := repo.NewWorkRepo().UpdateById(ctx, work.ID, map[string]interface{}{
		"is_delete": 1,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("delete work error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
		"is_delete": 1,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work is_delete failed")
	}

	if err = repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"work_count": gorm.Expr("if(work_count > 1, work_count - 1, 0)"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user work count error")
		return errors.New(errors.ServerError)
	}
	// 删除 ES 中的作品
	if err := es.NewWorkEsRepo().Delete(ctx, int(work.ID)); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("delete work from es error")
	}
	// 写入到作品删除表，用于异步处理作品删除以后，收藏数据
	if err := repo.NewWorkDeleteRepo().Save(ctx, &model.L36ActionWorkDelete{
		WorkID: work.ID,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create work delete error")
	}

	// 删除作品通知
	informService := NewInformService()
	if err := informService.DelWorkInform(ctx, work.RoleID, work.ID); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("del work inform failed")
	}

	// 清理作品资源
	if err := s.deleteWorkResource(ctx, work); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("delete work resource error")
	}

	util.LogYunying("l36_action_work_del", map[string]interface{}{
		"work_id": work.ID,
		"user_id": work.UserID,
		"role_id": work.RoleID,
	})
	return nil
}

func (s *WorkService) deleteWorkResource(ctx context.Context, work *model.L36ActionWork) error {
	if !config.C.Work.DeleteFile {
		return nil
	}

	fpService := NewFPService()

	toDeleteFiles := []string{}

	if work.Video != "" {
		toDeleteFiles = append(toDeleteFiles, work.Video)
	}
	if work.Images != "" {
		images := util.JsonParseStrArray(work.Images)
		for _, image := range images {
			toDeleteFiles = append(toDeleteFiles, image)
		}
	}
	if work.Cover != "" {
		toDeleteFiles = append(toDeleteFiles, work.Cover)
	}
	if work.Property != "" {
		propertyMap, err := s.downloadProperty(ctx, work.Property)
		if err != nil {
			return fmt.Errorf("download property error: %w", err)
		}
		fpService := NewFPService()
		for k, v := range propertyMap {
			if k == "appearStr" {
				continue
			}
			if vStr, ok := v.(string); ok {
				if fpService.IsFuxiFpUrl(vStr) || fpService.IsActionFpUrl(vStr) {
					toDeleteFiles = append(toDeleteFiles, vStr)
				}
			}
		}
		toDeleteFiles = append(toDeleteFiles, work.Property)
	}

	// 使用信号量控制最大并发数
	sem := make(chan struct{}, 5)
	group := sync.WaitGroup{}
	for _, file := range toDeleteFiles {
		group.Add(1)
		go func(file string) {
			defer group.Done()

			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量
			if fpService.IsFuxiFpUrl(file) {
				err := external.DeleteFile(ctx, &external.FilePickerDeleteReq{
					URL: file,
				})
				if err != nil {
					log.LogWithContext(ctx).WithError(err).Error("delete fuxi file error")
				}
			} else if fpService.IsActionFpUrl(file) {
				err := fpService.DelFile(ctx, file)
				if err != nil {
					log.LogWithContext(ctx).WithError(err).Error("delete action file error")
				}
			}
		}(file)
	}
	group.Wait()
	return nil
}

func (s *WorkService) getBackupList(ctx *gin.Context, workType int, cnt int, excludeIds []int64) ([]int64, error) {
	workIds, err := cache.NewBackupRecommendCache().Get(ctx, workType)
	if err != nil {
		return []int64{}, err
	}

	// 过滤掉已经存在的workIds
	filteredWorkIds := make([]int64, 0)
	excludeMap := make(map[int64]bool)
	for _, id := range excludeIds {
		excludeMap[id] = true
	}

	for _, id := range workIds {
		if !excludeMap[id] {
			filteredWorkIds = append(filteredWorkIds, id)
		}
	}

	selected := util.GetRandomElements(filteredWorkIds, cnt)
	elog.WithContext(ctx).WithFields(elog.Fields{
		"selected":   selected,
		"excludeIds": excludeIds,
	}).Debug("get backup list")
	return selected, nil
}

func (s *WorkService) getRecommendList(ctx *gin.Context, params external.FuxiRecommendParams) ([]model.L36ActionWork, string, error) {
	fuxiThisSecond, err := cache.NewRecommendLimitCache().Incr(ctx, 1)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("incr recommend limit error")
		return nil, "", err
	}

	// 如果 Disable 为 true，或者 Qps 限制，直接走保底逻辑
	if config.C.Fuxi.Disable || fuxiThisSecond > int64(config.C.Fuxi.Qps) {
		if fuxiThisSecond > int64(config.C.Fuxi.Qps) {
			log.LogWithContext(ctx).WithField("fuxi_this_second", fuxiThisSecond).Warn("fuxi qps limit")
		}
		backupList, err := s.getBackupList(ctx, params.Type, params.TopN, []int64{})
		if err != nil {
			return nil, "", err
		}
		works, err := cache.FindPublicByIds(ctx, backupList)
		if err != nil {
			return nil, "", err
		}
		return works, "", nil
	}

	recommendWorks := make([]model.L36ActionWork, 0)
	failed := false
	resp, err := external.GetFuxiRecommendWorks(ctx, &params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("get recommend works error")
		failed = true
	}
	workIds := resp.Data.Results
	works, err := cache.FindPublicByIds(ctx, workIds)
	if err != nil {
		return recommendWorks, "", err
	}
	works = s.filterRecommendWorks(works, params.Type)
	// 如果接口没有失败，且返回的条数小于 pageSize，无需补全
	if !failed && len(workIds) > 0 && len(workIds) < params.TopN {
		workDict := lo.KeyBy(works, func(work model.L36ActionWork) int64 {
			return work.ID
		})
		for _, workId := range workIds {
			if work, ok := workDict[workId]; ok {
				recommendWorks = append(recommendWorks, work)
			}
		}
		return recommendWorks, resp.Data.Scm, nil
	}
	// 补全
	leftCount := params.TopN - len(works)
	if leftCount > 0 {
		backupList, err := s.getBackupList(ctx, params.Type, leftCount, workIds)
		if err != nil {
			return recommendWorks, "", err
		}
		backupWorks, err := cache.FindPublicByIds(ctx, backupList)
		if err != nil {
			return recommendWorks, "", err
		}
		backupWorkDict := lo.KeyBy(backupWorks, func(work model.L36ActionWork) int64 {
			return work.ID
		})
		for _, workId := range backupList {
			if work, ok := backupWorkDict[workId]; ok {
				works = append(works, work)
			}
		}
	}
	//  处理不在推荐列表中的保底作品
	workDict := lo.KeyBy(works, func(work model.L36ActionWork) int64 {
		return work.ID
	})
	for _, workId := range workIds {
		if work, ok := workDict[workId]; ok {
			recommendWorks = append(recommendWorks, work)
		}
	}
	recommendDict := lo.KeyBy(recommendWorks, func(work model.L36ActionWork) int64 {
		return work.ID
	})
	for _, work := range works {
		if _, ok := recommendDict[work.ID]; !ok {
			recommendWorks = append(recommendWorks, work)
		}
	}
	return recommendWorks, resp.Data.Scm, nil
}

func (s *WorkService) filterRecommendWorks(works []model.L36ActionWork, workType int) []model.L36ActionWork {
	// TYPE = 1, 2 推荐 TYPE = 1,2
	if workType != constant.WorkTypePureVideo {
		return lo.Filter(works, func(work model.L36ActionWork, i int) bool {
			return work.Type == constant.WorkTypeAction || work.Type == constant.WorkTypeVideo
		})
	}
	// TYPE = 3 推荐 TYPE = 2 or TYPE = 3
	return lo.Filter(works, func(work model.L36ActionWork, i int) bool {
		return work.Type == constant.WorkTypePureVideo || work.Type == constant.WorkTypeVideo
	})
}

// fillWorkInfo 填充作品信息
// 参数:
//   - ctx: gin上下文
//   - works: 作品列表
//   - userId: 用户ID
//   - scm: 推荐来源
//
// 返回:
//   - 作品信息列表
//   - 错误信息
func (s WorkService) fillWorkInfo(ctx *gin.Context, works []model.L36ActionWork, userId int64, scm, traceId string) ([]schema.WorkItem, error) {
	if len(works) == 0 {
		return []schema.WorkItem{}, nil
	}

	workIds := lo.Map(works, func(work model.L36ActionWork, i int) int64 {
		return work.ID
	})

	userIds := lo.Map(works, func(work model.L36ActionWork, i int) int64 {
		return work.UserID
	})

	var likeMap map[int64]bool
	var collectMap map[int64]bool
	var designerInfos []schema.DesignerInfo
	var followMap map[int64]bool

	// 使用wait.Group并发获取数据
	// wg := &wait.Group{}
	var likeMapErr, collectMapErr, designerInfosErr, followMapErr error

	// wg.Start(func() {
	likeMap, likeMapErr = repo.NewWorkLikeRepo().GetLikeMap(ctx, userId, workIds)
	// })

	// wg.Start(func() {
	collectMap, collectMapErr = repo.NewWorkCollectRepo().GetCollectMap(ctx, userId, workIds)
	// })

	// wg.Start(func() {
	// users, usersErr = repo.NewUserRepo().FindByIds(ctx, userIds)
	designerInfos, designerInfosErr = cache.NewDesignerRoleCache().Gets(ctx, userIds)
	// })

	// wg.Start(func() {
	followMap, followMapErr = repo.NewDesignerFollowRepo().GetFollowMap(ctx, userId, userIds)
	// })

	// 等待所有goroutine完成
	// wg.Wait()

	// 检查错误
	if likeMapErr != nil {
		log.LogWithContext(ctx).WithError(likeMapErr).Error("get like map error")
		return nil, likeMapErr
	}
	if collectMapErr != nil {
		log.LogWithContext(ctx).WithError(collectMapErr).Error("get collect map error")
		return nil, collectMapErr
	}
	if designerInfosErr != nil {
		log.LogWithContext(ctx).WithError(designerInfosErr).Error("get designer infos error")
		return nil, designerInfosErr
	}
	if followMapErr != nil {
		log.LogWithContext(ctx).WithError(followMapErr).Error("get follow map error")
		return nil, followMapErr
	}

	designerInfoMap := lo.KeyBy(designerInfos, func(designerInfo schema.DesignerInfo) int64 {
		return designerInfo.ID
	})

	workInfos := lo.Map(works, func(work model.L36ActionWork, i int) schema.WorkItem {
		designerInfo := designerInfoMap[work.UserID]
		designerInfo.IsFollow = followMap[work.UserID]

		return schema.WorkItem{
			WorkId:       work.ID,
			Type:         int(work.Type),
			Name:         work.Name,
			Summary:      work.Summary,
			Video:        work.Video,
			Cover:        work.Cover,
			Images:       util.JsonParseStrArray(work.Images),
			Scene:        work.Scene,
			Property:     work.Property,
			IsLiked:      likeMap[work.ID],
			IsCollected:  collectMap[work.ID],
			LikeCount:    work.GetLikeCount(),
			CollectCount: work.GetCollectCount(),
			CommentCount: work.GetCommentCount(),
			Contents:     s.DecodeContents(int(work.Contents)),
			Hot:          work.GetHot(),
			Scm:          scm,
			Designer:     designerInfo,
			Visibility:   int(work.Visibility),
			ResampleUrl:  work.ResampleURL,
			RoleId:       work.RoleID,
			AuditStatus:  int(work.AuditStatus),
			TraceId:      traceId,
		}
	})
	return workInfos, nil
}

func (s *WorkService) WorkListRecommend(ctx *gin.Context, params *schema.WorkListRecommendReq) (*schema.WorkListRes, *errors.Err) {
	works, scm, err := s.getRecommendList(ctx, external.FuxiRecommendParams{
		TopN:      params.PageSize,
		RoleId:    params.RoleId,
		Type:      params.Type,
		SessionId: params.SessionId,
		TraceId:   params.TraceId,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get recommend list error")
		return nil, errors.New(errors.ServerError)
	}
	workInfos, err := s.fillWorkInfo(ctx, works, params.UserId, scm, params.TraceId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill work info error")
		return nil, errors.New(errors.ServerError)
	}

	if scm != "" {
		now := time.Now().Unix()
		for _, work := range workInfos {
			if work.Scm == scm {
				util.LogYunying("l36_action_bhv", map[string]interface{}{
					"sessionid": params.SessionId,
					"traceid":   params.TraceId,
					"role_id":   params.RoleId,
					"id":        work.WorkId,
					"scm":       scm,
					"bhv_time":  now,
					"bhv_type":  "exposure",
				})
			}
		}
	}

	return &schema.WorkListRes{
		List: workInfos,
	}, nil
}

func (s *WorkService) getDefaultHotAndExp(actionType string) (hot, exp int) {
	switch actionType {
	// 点赞
	case repo.FieldLikeCount:
		return config.C.Work.LikeHot, config.C.Work.LikeExp
	// 收藏
	case repo.FieldCollectCount:
		return config.C.Work.CollectHot, config.C.Work.CollectExp
	// 评论
	case repo.FieldCommentCount:
		return config.C.Work.CommentHot, config.C.Work.CommentExp
	// 转发(有效转发)
	case repo.FieldShareCount:
		return config.C.Work.ShareHot, config.C.Work.ShareExp
	// 被翻拍
	case repo.FieldUseCount:
		return config.C.Work.UseHot, config.C.Work.UseExp
	// 有效浏览
	case repo.FieldCompleteCount:
		return config.C.Work.CompletePlayHot, config.C.Work.CompletePlayExp
	}
	panic(fmt.Sprintf("unknown action type: %s", actionType))
}

// calculateHotAndExp 计算点赞/收藏等操作的热度和经验值
func (s *WorkService) calculateHotAndExp(ctx context.Context, userId int64, authorId int64, work *model.L36ActionWork, actionType string, notAddHot bool) (hot, exp int, err error) {
	// 如果不满足	，则不增加热度和经验值
	if notAddHot || work.IsPrivateWork() {
		return 0, 0, nil
	}

	hot, exp = s.getDefaultHotAndExp(actionType)
	// 检查是否达到用户对设计师的每日经验/热度上限
	isReachLimit, err := repo.NewExpLimitSubRepo().IsReachUserDesignerExpLimit(ctx, userId, authorId, actionType)
	if err != nil {
		return 0, 0, fmt.Errorf("check user designer exp limit error for action %s failed: %w", actionType, err)
	}
	if isReachLimit {
		hot = 0
		exp = 0
	}
	return hot, exp, nil
}

func (s *WorkService) WorkLike(ctx *gin.Context, params *schema.WorkLikeReq) *errors.Err {
	workRepo, workLikeRepo := repo.NewWorkRepo(), repo.NewWorkLikeRepo()
	work, err := workRepo.FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return errors.New(errors.WorkNotExist)
	}
	like, err := workLikeRepo.FindByUserWork(ctx, params.UserId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work like error")
		return errors.New(errors.ServerError)
	}
	if like != nil && like.IsDelete == 0 {
		return errors.New(errors.WorkLikeExist)
	}
	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find user error")
		return errors.New(errors.ServerError)
	}
	if user == nil {
		log.LogWithContext(ctx).Error("user not found")
		return errors.New(errors.UserNotExist)
	}
	likeID, err := workLikeRepo.Like(ctx, params.UserId, params.RoleId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("like work error")
		return errors.New(errors.ServerError)
	}
	if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"liked_count": gorm.Expr("liked_count + ?", 1),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user liked count error")
		return errors.New(errors.ServerError)
	}

	hot, exp, err := s.calculateHotAndExp(ctx, params.UserId, work.UserID, work, repo.FieldLikeCount, like != nil)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("calculate hot and exp error")
		return errors.New(errors.ServerError)
	}

	if err := workRepo.UpdateById(ctx, params.WorkId, map[string]interface{}{
		"like_count": gorm.Expr("like_count + ?", 1),
		"hot":        gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work like count error")
		return errors.New(errors.ServerError)
	}

	// 添加点赞通知
	informService := NewInformService()
	if err := informService.AddWorkLikeInform(ctx, user.NameRoleID, work.RoleID, work.ID, work.Name, likeID); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("add work like inform failed")
	}

	_, err = cache.GetWorkCache().Incr(ctx, work.ID, map[string]interface{}{
		"like_count": 1,
		"hot":        hot,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work like count error")
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(params.WorkId), map[string]interface{}{
		"like_count": work.LikeCount + work.FakeLikeCount + 1,
		"hot":        work.Hot + int32(hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work like count error")
	}

	if exp > 0 {
		if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
			"exp": gorm.Expr("exp + ?", exp),
		}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update user exp error")
			return errors.New(errors.ServerError)
		}
	}

	util.LogYunying("l36_action_work_like", map[string]interface{}{
		"work_id": params.WorkId,
		"user_id": params.UserId,
		"role_id": params.RoleId,
	})
	if params.Scm != "" {
		util.LogYunying("l36_action_bhv", map[string]interface{}{
			"sessionid": params.SessionId,
			"traceid":   params.TraceId,
			"role_id":   params.RoleId,
			"id":        params.WorkId,
			"scm":       params.Scm,
			"bhv_time":  time.Now().Unix(),
			"bhv_type":  "like",
		})
	}

	// 新增实习活动埋点日志
	util.LogYunying("l36_action_work_like_change", map[string]interface{}{
		"work_id":          params.WorkId,
		"designer_id":      work.UserID,
		"designer_role_id": work.RoleID,
		"view_like_count":  work.LikeCount + work.FakeLikeCount + 1,
	})

	return nil
}

func (s *WorkService) WorkCancelLike(ctx *gin.Context, params *schema.WorkCancelLikeReq) *errors.Err {
	workRepo, workLikeRepo := repo.NewWorkRepo(), repo.NewWorkLikeRepo()
	work, err := workRepo.FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	// 如果作品不存在或已删除,或者作品对其他用户不展示,返回不存在
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return errors.New(errors.WorkNotExist)
	}
	like, err := workLikeRepo.FindByUserWork(ctx, params.UserId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work like error")
		return errors.New(errors.ServerError)
	}
	// 如果点赞记录不存在或已删除，直接返回成功
	if like == nil || like.IsDelete == 1 {
		return errors.New(errors.LikeNotExist)
	}
	if err := workLikeRepo.UpdateById(ctx, like.ID, map[string]interface{}{
		"is_delete": 1,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("cancel like work error")
		return errors.New(errors.ServerError)
	}
	if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"liked_count": gorm.Expr("if(liked_count > 1, liked_count - 1, 0)"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user liked count error")
		return errors.New(errors.ServerError)
	}
	if err := workRepo.UpdateById(ctx, params.WorkId, map[string]interface{}{
		"like_count": gorm.Expr("if(like_count > 1, like_count - 1, 0)"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work like count error")
		return errors.New(errors.ServerError)
	}

	// 删除点赞通知
	informService := NewInformService()
	if err := informService.DelWorkLikeInform(ctx, work.RoleID, work.ID, like.ID); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("del work like inform failed")
	}

	_, err = cache.GetWorkCache().Incr(ctx, work.ID, map[string]interface{}{
		"like_count": -1,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work like count error")
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(params.WorkId), map[string]interface{}{
		"like_count": work.LikeCount + work.FakeLikeCount - 1,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work like count error")
	}

	util.LogYunying("l36_action_work_cancel_like", map[string]interface{}{
		"work_id": params.WorkId,
		"user_id": params.UserId,
		"role_id": params.RoleId,
	})
	return nil
}

func (s *WorkService) WorkCollect(ctx *gin.Context, params *schema.WorkCollectReq) *errors.Err {
	workRepo, workCollectRepo := repo.NewWorkRepo(), repo.NewWorkCollectRepo()
	work, err := workRepo.FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return errors.New(errors.WorkNotExist)
	}
	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find user error")
		return errors.New(errors.ServerError)
	}
	if user == nil {
		return errors.New(errors.UserNotExist)
	}
	if user.CollectCount >= int32(config.C.Work.CollectLimit) {
		return errors.New(errors.UserCollectLimit)
	}
	collect, err := workCollectRepo.FindByUserWork(ctx, params.UserId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work collect error")
		return errors.New(errors.ServerError)
	}
	if collect != nil && collect.IsDelete == 0 {
		return errors.New(errors.WorkCollectExist)
	}
	collectID, err := workCollectRepo.Collect(ctx, params.UserId, params.RoleId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("collect work error")
		return errors.New(errors.ServerError)
	}
	if err := repo.NewUserRepo().UpdateById(ctx, params.UserId, map[string]interface{}{
		"collect_count": gorm.Expr("collect_count + ?", 1),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user collect count error")
		return errors.New(errors.ServerError)
	}

	hot, exp, err := s.calculateHotAndExp(ctx, params.UserId, work.UserID, work, repo.FieldCollectCount, collect != nil)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("calculate hot and exp error")
		return errors.New(errors.ServerError)
	}

	if err := workRepo.UpdateById(ctx, params.WorkId, map[string]interface{}{
		"collect_count": gorm.Expr("collect_count + ?", 1),
		"hot":           gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work collect count error")
		return errors.New(errors.ServerError)
	}

	// 添加收藏通知
	informService := NewInformService()
	if err := informService.AddWorkCollectInform(ctx, user.NameRoleID, work.RoleID, work.ID, work.Name, collectID); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("add work collect inform failed")
	}

	_, err = cache.GetWorkCache().Incr(ctx, params.WorkId, map[string]interface{}{
		"collect_count": 1,
		"hot":           hot,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work collect count error")
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(params.WorkId), map[string]interface{}{
		"collect_count": work.CollectCount + work.FakeCollectCount + 1,
		"hot":           work.Hot + int32(hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work collect count error")
	}

	if exp > 0 {
		if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
			"exp": gorm.Expr("exp + ?", exp),
		}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update user exp error")
			return errors.New(errors.ServerError)
		}
	}

	util.LogYunying("l36_action_work_collect", map[string]interface{}{
		"work_id": params.WorkId,
		"user_id": params.UserId,
		"role_id": params.RoleId,
	})
	if params.Scm != "" {
		util.LogYunying("l36_action_bhv", map[string]interface{}{
			"sessionid": params.SessionId,
			"traceid":   params.TraceId,
			"role_id":   params.RoleId,
			"id":        params.WorkId,
			"scm":       params.Scm,
			"bhv_time":  time.Now().Unix(),
			"bhv_type":  "collect",
		})
	}
	return nil
}

func (s *WorkService) WorkCancelCollect(ctx *gin.Context, params *schema.WorkCancelCollectReq) *errors.Err {
	workRepo, workCollectRepo := repo.NewWorkRepo(), repo.NewWorkCollectRepo()
	work, err := workRepo.FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}
	collect, err := workCollectRepo.FindByUserWork(ctx, params.UserId, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work collect error")
		return errors.New(errors.ServerError)
	}
	if collect == nil || collect.IsDelete == 1 {
		return errors.New(errors.WorkCollectNotExist)
	}
	if err := workCollectRepo.UpdateById(ctx, collect.ID, map[string]interface{}{
		"is_delete": 1,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("cancel collect work error")
		return errors.New(errors.ServerError)
	}
	if err := workRepo.UpdateById(ctx, params.WorkId, map[string]interface{}{
		"collect_count": gorm.Expr("if(collect_count > 1, collect_count - 1, 0)"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work collect count error")
		return errors.New(errors.ServerError)
	}

	// 删除收藏通知
	informService := NewInformService()
	if err := informService.DelWorkCollectInform(ctx, work.RoleID, work.ID, collect.ID); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("del work collect inform failed")
	}

	_, err = cache.GetWorkCache().Incr(ctx, params.WorkId, map[string]interface{}{
		"collect_count": -1,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work collect count error")
	}

	// 更新玩家收藏数
	if err := repo.NewUserRepo().UpdateById(ctx, params.UserId, map[string]interface{}{
		"collect_count": gorm.Expr("if(collect_count > 1, collect_count - 1, 0)"),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user collect count error")
		return errors.New(errors.ServerError)
	}

	// 同步到ES
	if err := es.NewWorkEsRepo().Update(ctx, int(params.WorkId), map[string]interface{}{
		"collect_count": work.CollectCount + work.FakeCollectCount - 1,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("update work collect count error")
	}

	util.LogYunying("l36_action_work_cancel_collect", map[string]interface{}{
		"work_id": params.WorkId,
		"user_id": params.UserId,
		"role_id": params.RoleId,
	})
	return nil
}

func (s *WorkService) WorkListSearch(ctx *gin.Context, params *schema.WorkListSearchReq) (*schema.WorkListRes, *errors.Err) {
	workIds, err := es.NewWorkEsRepo().SearchListWork(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("search work error")
		return nil, errors.New(errors.ServerError)
	}
	works, err := repo.NewWorkRepo().FindPublicByIds(ctx, workIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find works error")
		return nil, errors.New(errors.ServerError)
	}
	workInfos, err := s.fillWorkInfo(ctx, works, params.UserId, "", "")
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill work info error")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.WorkListRes{
		List: workInfos,
	}, nil
}

func (s *WorkService) WorkDetail(ctx *gin.Context, params *schema.WorkDetailReq) (*schema.WorkDetailRes, *errors.Err) {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return nil, errors.New(errors.WorkNotExist)
	}
	workInfo, err := s.fillWorkInfo(ctx, []model.L36ActionWork{*work}, params.UserId, "", "")
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill work info error")
	}
	if params.Scm != "" {
		util.LogYunying("l36_action_bhv", map[string]interface{}{
			"sessionid": params.SessionId,
			"traceid":   params.TraceId,
			"role_id":   params.RoleId,
			"id":        params.WorkId,
			"scm":       params.Scm,
			"bhv_time":  time.Now().Unix(),
			"bhv_type":  "exposure",
		})
	}
	return &schema.WorkDetailRes{
		WorkItem: workInfo[0],
	}, nil
}

func (s *WorkService) WorkListMine(ctx *gin.Context, params *schema.WorkListMineReq) (*schema.WorkListRes, *errors.Err) {
	works, err := repo.NewWorkRepo().ListWorkMine(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list work mine error")
		return nil, errors.New(errors.ServerError)
	}
	workInfos, err := s.fillWorkInfo(ctx, works, params.UserId, "", "")
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill work info error")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.WorkListRes{
		List: workInfos,
	}, nil
}

func (s *WorkService) WorkListCollect(ctx *gin.Context, params *schema.WorkListCollectReq) (*schema.WorkListRes, *errors.Err) {
	works, err := repo.NewWorkRepo().ListWorkCollect(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list work collect error")
		return nil, errors.New(errors.ServerError)
	}
	workInfos, err := s.fillWorkInfo(ctx, works, params.UserId, "", "")
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill work info error")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.WorkListRes{
		List: workInfos,
	}, nil
}

func (s *WorkService) WorkListDesigner(ctx *gin.Context, params *schema.WorkListDesignerReq) (*schema.WorkListRes, *errors.Err) {
	works, err := repo.NewWorkRepo().ListWorkDesigner(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list work designer error")
		return nil, errors.New(errors.ServerError)
	}
	workInfos, err := s.fillWorkInfo(ctx, works, params.UserId, "", "")
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("fill work info error")
		return nil, errors.New(errors.ServerError)
	}
	return &schema.WorkListRes{
		List: workInfos,
	}, nil
}

func (s *WorkService) WorkTrace(ctx *gin.Context, params *schema.WorkTraceReq) *errors.Err {
	util.LogYunying("l36_action_bhv", map[string]interface{}{
		"sessionid": params.SessionId,
		"traceid":   params.TraceId,
		"role_id":   params.RoleId,
		"id":        params.WorkId,
		"scm":       params.Scm,
		"bhv_time":  time.Now().Unix(),
		"bhv_type":  params.BhvType,
	})
	return nil
}

func (s *WorkService) WorkUse(ctx *gin.Context, params *schema.WorkUseReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 || !work.IsShowForOther(params.UserId) {
		return errors.New(errors.WorkNotExist)
	}
	// 每天有使用上限, 超出不加热度
	count, err := repo.NewWorkUseRepo().CountByWorkUserDate(ctx, params.WorkId, params.UserId, time.Now().UnixMilli())
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("count work use error")
		return errors.New(errors.ServerError)
	}
	if count >= int64(config.C.Work.DailyEffectUseLimit) {
		return nil
	}

	hot, exp, err := s.calculateHotAndExp(ctx, params.UserId, work.UserID, work, repo.FieldUseCount, false)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("calculate hot and exp error")
		return errors.New(errors.ServerError)
	}

	if exp <= 0 {
		return nil
	}

	if err := repo.NewWorkUseRepo().Create(ctx, &model.L36ActionWorkUse{
		WorkID: params.WorkId,
		UserID: params.UserId,
		RoleID: params.RoleId,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create work use error")
		return errors.New(errors.ServerError)
	}

	if err := repo.NewWorkRepo().UpdateById(ctx, params.WorkId, map[string]interface{}{
		"use_count": gorm.Expr("use_count + ?", 1),
		"hot":       gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work use count error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Incr(ctx, params.WorkId, map[string]interface{}{
		"use_count": 1,
		"hot":       hot,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work use count and hot failed")
	}

	if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"exp": gorm.Expr("exp + ?", config.C.Work.UseExp),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user exp error")
		return errors.New(errors.ServerError)
	}

	if params.Scm != "" {
		util.LogYunying("l36_action_bhv", map[string]interface{}{
			"sessionid": params.SessionId,
			"traceid":   params.TraceId,
			"role_id":   params.RoleId,
			"id":        params.WorkId,
			"scm":       params.Scm,
			"bhv_time":  time.Now().Unix(),
			"bhv_type":  "use",
		})
	}
	return nil
}

func (s *WorkService) WorkCompletePlay(ctx *gin.Context, params *schema.WorkCompletePlayReq) *errors.Err {
	return nil
}

func (s *WorkService) OnWorkSuccessView(ctx context.Context, params *schema.WorkCompletePlayReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}
	// 每天只有第一次有效浏览计算热度
	now := time.Now()
	date := int64(now.Year()*10000 + int(now.Month())*100 + now.Day())
	playRepo := repo.NewWorkPlaySubRepo()
	record, err := playRepo.FindByWorkUserDate(ctx, params.WorkId, params.UserId, date)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work play error")
		return errors.New(errors.ServerError)
	}

	hot, exp, err := s.calculateHotAndExp(ctx, params.UserId, work.UserID, work, repo.FieldCompleteCount, record != nil)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("calculate hot and exp error")
		return errors.New(errors.ServerError)
	}

	if exp <= 0 {
		return nil
	}

	if err := playRepo.Create(ctx, &model.L36ActionWorkPlay{
		WorkID: params.WorkId,
		UserID: params.UserId,
		RoleID: params.RoleId,
		Date:   date,
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create work play error")
		return errors.New(errors.ServerError)
	}

	if err := repo.NewWorkRepo().UpdateById(ctx, params.WorkId, map[string]interface{}{
		"complete_count": gorm.Expr("complete_count + ?", 1),
		"hot":            gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work complete count error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Incr(ctx, params.WorkId, map[string]interface{}{
		"complete_count": 1,
		"hot":            hot,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work complete count and hot failed")
	}

	if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
		"exp": gorm.Expr("exp + ?", exp),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update user exp error")
		return errors.New(errors.ServerError)
	}

	if params.Scm != "" {
		util.LogYunying("l36_action_bhv", map[string]interface{}{
			"sessionid": params.SessionId,
			"traceid":   params.TraceId,
			"role_id":   params.RoleId,
			"id":        params.WorkId,
			"scm":       params.Scm,
			"bhv_time":  time.Now().Unix(),
			"bhv_type":  "complete_play",
		})
	}

	return nil
}

func (s *WorkService) WorkEffectShare(ctx *gin.Context, params *schema.WorkEffectShareReq) *errors.Err {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return errors.New(errors.WorkNotExist)
	}
	share, err := repo.NewWorkShareRepo().FindById(ctx, params.ShareId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work share error")
		return errors.New(errors.ServerError)
	}
	if share == nil {
		return errors.New(errors.WorkShareNotExist)
	}
	if share.IsEffect == 1 {
		return nil
	}

	if err := repo.NewWorkShareRepo().UpdateById(ctx, params.ShareId, map[string]interface{}{
		"is_effect":   1,
		"effect_time": time.Now().UnixMilli(),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work share error")
		return errors.New(errors.ServerError)
	}

	// 转发，每人每天只算一次热度
	isAddHot := s.getEffectShareLock(ctx, share, work)
	hot, exp, err := s.calculateHotAndExp(ctx, share.UserID, work.UserID, work, repo.FieldShareCount, isAddHot)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("calculate hot and exp error")
		return errors.New(errors.ServerError)
	}

	if err := repo.NewWorkRepo().UpdateById(ctx, params.WorkId, map[string]interface{}{
		"share_count": gorm.Expr("share_count + ?", 1),
		"hot":         gorm.Expr("hot + ?", hot),
	}); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work share count error")
		return errors.New(errors.ServerError)
	}

	_, err = cache.GetWorkCache().Incr(ctx, params.WorkId, map[string]interface{}{
		"share_count": 1,
		"hot":         hot,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("update work share count and hot failed")
	}

	if exp > 0 {
		if err := repo.NewUserRepo().UpdateById(ctx, work.UserID, map[string]interface{}{
			"exp": gorm.Expr("exp + ?", exp),
		}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update user exp error")
			return errors.New(errors.ServerError)
		}
	}

	util.LogYunying("l36_action_work_effect_share", map[string]interface{}{
		"work_id": params.WorkId,
		"user_id": params.UserId,
		"role_id": params.RoleId,
	})
	return nil
}

func (*WorkService) getEffectShareLock(ctx context.Context, share *model.L36ActionWorkShare, work *model.L36ActionWork) bool {
	date := time.Now().Format(time.DateOnly)
	limitKey := fmt.Sprintf("work_share_limit:%d:%d:%s", share.UserID, work.ID, date)
	lock := lock.NewOperationLock(conn.GetRedisConn(), limitKey)
	tomorrow := time.Now().AddDate(0, 0, 1).Truncate(time.Hour * 24)
	expire := tomorrow.Sub(time.Now()) + time.Duration(rand.Intn(1000))*time.Second
	ok, err := lock.Lock(ctx, expire)
	if err != nil {
		return false
	}
	return ok
}

func (s *WorkService) WorkShare(ctx *gin.Context, params *schema.WorkShareReq) (*schema.WorkShareRes, *errors.Err) {
	work, err := repo.NewWorkRepo().FindById(ctx, params.WorkId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work error")
		return nil, errors.New(errors.ServerError)
	}
	if work == nil || work.IsDelete == 1 {
		return nil, errors.New(errors.WorkNotExist)
	}
	if work.Visibility == int8(constant.VisibilityPrivate) {
		return nil, errors.New(errors.WorkIsPrivate)
	}
	if !work.IsAuditPass() {
		return nil, errors.New(errors.WorkAuditNotPass)
	}

	user, err := repo.NewUserRepo().FindById(ctx, params.UserId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find user error")
		return nil, errors.New(errors.ServerError)
	}

	if user == nil {
		log.LogWithContext(ctx).Error("user not found")
		return nil, errors.New(errors.UserNotExist)
	}

	share := model.L36ActionWorkShare{
		WorkID:   params.WorkId,
		UserID:   params.UserId,
		RoleID:   params.RoleId,
		IsEffect: 0,
	}
	if err := repo.NewWorkShareRepo().Create(ctx, &share); err != nil {
		log.LogWithContext(ctx).WithError(err).Error("create work share error")
		return nil, errors.New(errors.ServerError)
	}

	// 添加分享通知
	informService := NewInformService()
	if err := informService.AddWorkShareInform(ctx, user.NameRoleID, work.RoleID, work.ID, work.Name); err != nil {
		log.LogWithContext(ctx).WithError(err).Warn("add work share inform failed")
	}

	util.LogYunying("l36_action_work_share", map[string]interface{}{
		"work_id": params.WorkId,
		"user_id": params.UserId,
		"role_id": params.RoleId,
	})
	return &schema.WorkShareRes{
		ShareId: share.ID,
	}, nil
}

func (s *WorkService) HandleWorkDelete(ctx context.Context, work *model.L36ActionWork) error {
	batch := 1000
	workCollectRepo := repo.NewWorkCollectRepo()
	for {
		collects, err := workCollectRepo.FindCollectsByWorkId(ctx, work.ID, batch)
		if err != nil {
			elog.WithContext(ctx).WithError(err).Error("find work collect error")
			return err
		}
		// 批量更新收藏记录状态
		collectIds := lo.Map(collects, func(collect model.L36ActionWorkCollect, _ int) int64 {
			return collect.ID
		})
		if err := workCollectRepo.UpdateByIds(ctx, collectIds, map[string]interface{}{
			"is_delete": 1,
		}); err != nil {
			elog.WithContext(ctx).WithError(err).Error("update work collect error")
			return err
		}
		// 更新玩家收藏数
		userIds := lo.Map(collects, func(collect model.L36ActionWorkCollect, _ int) int64 {
			return collect.UserID
		})
		if err := repo.NewUserRepo().UpdateByIds(ctx, userIds, map[string]interface{}{
			"collect_count": gorm.Expr("if(collect_count > 1, collect_count - 1, 0)"),
		}); err != nil {
			elog.WithContext(ctx).WithError(err).Error("update user collect count error")
			return err
		}
		if len(collects) < batch {
			break
		}
	}
	return nil
}
