package service

import (
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util/log"

	"github.com/gin-gonic/gin"
)

type OpenService struct{}

func NewOpenService() *OpenService {
	return &OpenService{}
}

func (s *OpenService) OpenWorkDetail(ctx *gin.Context, params *schema.OpenWorkDetailReq) (*schema.OpenWorkDetailRes, *errors.Err) {
	user, err := repo.NewUserRepo().FindOneByRoleId(ctx, params.RoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find user error")
		return nil, errors.New(errors.ServerError)
	}
	userId := int64(0)
	if user != nil {
		userId = user.ID
	}
	workService := NewWorkService()
	workInfo, detailErr := workService.WorkDetail(ctx, &schema.WorkDetailReq{
		UserBase: schema.UserBase{
			UserId: userId,
		},
		WorkBase: schema.WorkBase{
			WorkId: params.WorkId,
		},
	})
	if detailErr != nil {
		return nil, detailErr
	}
	return &schema.OpenWorkDetailRes{
		WorkItem: workInfo.WorkItem,
	}, nil
}
