package service

import (
	"regexp"
	"strconv"
	"strings"

	"app/dao/cache"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util/log"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// 预编译正则表达式
var (
	regWithColor = regexp.MustCompile(`#(c|C)[0-9a-fA-F]{6}#[^#\s]{1,16}##`)
	regSimple    = regexp.MustCompile(`#([^#]{1,16})#`)
)

type TopicService struct{}

func NewTopicService() *TopicService {
	return &TopicService{}
}

// TopicSearch 搜索话题
// 参数:
//   - ctx: gin上下文
//   - params: 搜索参数
//
// 返回:
//   - 话题搜索结果
//   - 错误信息
func (s *TopicService) TopicSearch(ctx *gin.Context, params *schema.TopicSearchReq) (*schema.TopicSearchRes, *errors.Err) {
	topics, err := repo.NewTopicRepo().SearchTopics(ctx, params)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("search topics error")
		return nil, errors.New(errors.ServerError)
	}

	topicInfos := lo.Map(topics, func(topic model.L36ActionTopic, i int) schema.TopicItem {
		return schema.TopicItem{
			ID:   topic.ID,
			Name: topic.Name,
		}
	})

	return &schema.TopicSearchRes{
		List: topicInfos,
	}, nil
}

// TopicList 获取话题列表
// 参数:
//   - ctx: gin上下文
//   - params: 列表参数
//
// 返回:
//   - 话题列表结果
//   - 错误信息
func (s *TopicService) TopicList(ctx *gin.Context, params *schema.TopicListReq) (*schema.TopicListRes, *errors.Err) {
	topics, err := cache.NewHotTopicCache().Get(ctx, 0)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list topics error")
		return nil, errors.New(errors.ServerError)
	}

	// 分页处理
	pageTopics := lo.Slice(topics, params.PageSize*(params.Page-1), params.PageSize*params.Page)
	topicInfos := lo.Map(pageTopics, func(topic model.L36ActionTopic, i int) schema.TopicItem {
		return schema.TopicItem{
			ID:   topic.ID,
			Name: topic.Name,
		}
	})

	return &schema.TopicListRes{
		List: topicInfos,
	}, nil
}

// filterEmoji 过滤掉表情符号
// 参数:
//   - topicArr: 话题数组
//
// 返回:
//   - 过滤后的话题数组
func filterEmoji(topicArr []string) []string {
	topics := []string{}
	for _, r := range topicArr {
		n, err := strconv.Atoi(r)
		if err != nil || n == 64 || n < 1 || n > 73 {
			topics = append(topics, r)
		}
	}
	return topics
}

// getTopicFromText 从文本中提取话题
// 参数:
//   - text: 文本内容
//
// 返回:
//   - 提取的话题数组
func getTopicFromText(text string) []string {
	var topicNames []string

	matchTopicWithColor := regWithColor.FindAllString(text, -1)
	if matchTopicWithColor != nil {
		for _, r := range matchTopicWithColor {
			topicNames = append(topicNames, r[9:len(r)-2])
		}
		text = regWithColor.ReplaceAllString(text, "")
	}

	matchTopicSimple := regSimple.FindAllString(text, -1)
	for _, r := range matchTopicSimple {
		topicNames = append(topicNames, strings.Trim(r, "#"))
	}

	return filterEmoji(lo.Uniq(topicNames))
}

// GetTopicNames 从文本中获取话题名称
// 参数:
//   - text: 文本内容
//
// 返回:
//   - 话题名称数组
func (s *TopicService) GetTopicNames(text string) []string {
	return getTopicFromText(text)
}

func (s *TopicService) TopicListRecommend(ctx *gin.Context, params *schema.TopicListRecommendReq) (*schema.TopicListRecommendRes, *errors.Err) {
	topics, err := cache.NewRecommendTopicCache().Get(ctx, 0)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("list topics error")
		return nil, errors.New(errors.ServerError)
	}
	topicInfos := lo.Map(topics, func(topic model.L36ActionTopic, i int) schema.TopicItem {
		return schema.TopicItem{
			ID:   topic.ID,
			Name: topic.Name,
		}
	})

	return &schema.TopicListRecommendRes{
		List: topicInfos,
	}, nil
}
