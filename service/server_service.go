package service

import (
	"math"
	"time"

	"app/constant"
	"app/dao/cache"
	"app/dao/es"
	"app/dao/model"
	"app/dao/repo"
	"app/errors"
	"app/schema"
	"app/util"
	"app/util/log"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type ServerService struct{}

func NewServerService() *ServerService {
	return &ServerService{}
}

// ServerFpReviewCallback 处理文件上传服务(FP)的审核回调
// 参数:
//   - ctx: gin上下文
//   - params: 回调请求参数
//
// 返回:
//   - 错误信息
func (s *ServerService) ServerFpReviewCallback(ctx *gin.Context, params *schema.ServerFpReviewCallbackReq) *errors.Err {
	fpService, workEsRepo, workService := NewFPService(), es.NewWorkEsRepo(), NewWorkService()
	fileIds := lo.Map(params.Files, func(file schema.FpReviewCallbackFile, i int) string {
		return file.FileId
	})
	workRepo, fileRepo := repo.NewWorkRepo(), repo.NewReviewMediaRepo()
	files, err := fileRepo.FindByFileIds(ctx, fileIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find files by file ids failed")
		return errors.New(errors.ServerError)
	}

	// 文件ID到文件信息的映射
	fileMap := lo.KeyBy(files, func(file model.L36ActionReviewMedium) string {
		return file.FileID
	})

	// 文件ID到审核状态的映射
	fileAuditStatusMap := make(map[string]constant.FpReviewStatus)

	// 作品ID到文件ID列表的映射
	workFileIdsMap := make(map[int64][]string)

	// 审核状态到作品ID列表的映射
	auditStatusMap := make(map[constant.FpReviewStatus][]int64)

	// 预审核通过的图片列表
	preReviewedImages := make([]model.L36ActionReviewMedium, 0)

	for _, file := range params.Files {
		fileAuditStatusMap[file.FileId] = constant.FpReviewStatus(file.ToStatus)
		// 更新原始files切片中的AuditStatus
		for i := range files {
			if files[i].FileID == file.FileId {
				files[i].AuditStatus = int8(file.ToStatus)
			}
		}
		if localImage, ok := fileMap[file.FileId]; ok {
			if auditStatusMap[file.ToStatus] == nil {
				auditStatusMap[file.ToStatus] = make([]int64, 0)
			}
			auditStatusMap[file.ToStatus] = append(auditStatusMap[file.ToStatus], localImage.ID)
			if localImage.WorkID != 0 {
				if workFileIdsMap[localImage.WorkID] == nil {
					workFileIdsMap[localImage.WorkID] = make([]string, 0)
				}
				workFileIdsMap[localImage.WorkID] = append(workFileIdsMap[localImage.WorkID], localImage.FileID)
			}
		} else {
			preReviewedImages = append(preReviewedImages, model.L36ActionReviewMedium{
				FileID:      file.FileId,
				AuditStatus: int8(file.ToStatus),
			})
		}
	}

	// 批量创建预审核通过的图片
	if len(preReviewedImages) > 0 {
		if err := fileRepo.CreateBatch(ctx, preReviewedImages); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("create batch files failed")
			// 这里只记录日志，不返回错误，允许部分操作失败
		}
	}

	// 更新文件的审核状态
	if len(files) > 0 {
		if err := fileRepo.SaveBatchAuditStatus(ctx, files); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update batch audit status failed")
		}
	}

	// 获取需要更新的作品列表
	workIds := lo.Keys(workFileIdsMap)
	works, err := workRepo.FindByIds(ctx, workIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find works by ids failed")
		return errors.New(errors.ServerError)
	}

	// 获取作品对应的话题
	workTopicMap, err := repo.NewWorkTopicRepo().FindWorkTopics(ctx, workIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find work topics failed")
		return errors.New(errors.ServerError)
	}

	// 更新每个作品的审核状态和同步到ES
	for _, work := range works {
		// 更新work的审核状态
		images := util.JsonToStringArray(work.Images)
		var auditInfo AuditInfo
		if err := sonic.Unmarshal([]byte(work.AuditInfo), &auditInfo); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("unmarshal audit info failed")
			continue
		}

		// 更新每张图片的审核状态
		for i, image := range images {
			fileId := fpService.GetFpFileId(image)
			fileAuditStatus, ok := fileAuditStatusMap[fileId]
			if ok {
				auditInfo.Images[i] = int(fileAuditStatus)
			}
		}

		// 更新视频审核状态
		if work.Video != "" {
			fileId := fpService.GetFpFileId(work.Video)
			if fileAuditStatus, ok := fileAuditStatusMap[fileId]; ok {
				auditInfo.Video = int(fileAuditStatus)
			}
		}

		// 更新封面审核状态
		if work.Cover != "" {
			fileId := fpService.GetFpFileId(work.Cover)
			if fileAuditStatus, ok := fileAuditStatusMap[fileId]; ok {
				auditInfo.Cover = int(fileAuditStatus)
			}
		}

		// 根据图片审核状态计算作品整体审核状态
		auditStatus := workService.GetAuditStatusByAuditInfo(&auditInfo)
		if err := workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
			"audit_status": auditStatus,
			"audit_info":   util.StructToJson(auditInfo),
		}); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update work audit status failed")
			continue
		}

		_, err = cache.GetWorkCache().Update(ctx, work.ID, map[string]interface{}{
			"audit_status": int(auditStatus),
			"audit_info":   util.StructToJson(auditInfo),
		})
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Error("update work audit status failed")
		}

		topicIds := workTopicMap[work.ID]
		work.AuditStatus = int8(auditStatus)
		if err := workEsRepo.IndexWork(ctx, &work, topicIds); err != nil {
			log.LogWithContext(ctx).WithError(err).Error("index work error")
		}
	}
	return nil
}

func (s *ServerService) ServerDesignerDetail(ctx *gin.Context, params *schema.ServerDesignerDetailReq) (*schema.ServerDesignerDetailRes, *errors.Err) {
	designer, err := repo.NewUserRepo().FindOneByRoleId(ctx, params.RoleId)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find designer by role id failed")
		return nil, errors.New(errors.ServerError)
	}
	if designer == nil {
		return nil, errors.New(errors.DesignerNotExist)
	}
	roleIds := []int64{designer.AvatarRoleID, designer.NameRoleID}
	roles, err := repo.NewRoleRepo().FindByRoleIds(ctx, roleIds)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("find role fail")
		return nil, errors.New(errors.ServerError)
	}
	roleDict := lo.KeyBy(roles, func(role model.L36RoleInfo) int64 {
		return role.RoleID
	})
	designerService := NewDesignerService()
	level, threshold := designerService.getDesignerLevelInfo(int64(designer.Exp))
	avatarRole := roleDict[designer.AvatarRoleID]
	nameRole := roleDict[designer.NameRoleID]
	return &schema.ServerDesignerDetailRes{
		DesignerInfo:  *designerService.fillDesignerInfo(designer, &avatarRole, &nameRole, false),
		WorkCount:     int(designer.WorkCount),
		FansCount:     int(designer.FansCount),
		LikedCount:    int(designer.LikedCount),
		FollowCount:   int(designer.FollowCount),
		DesignerLevel: level,
		Exp:           int(designer.Exp),
		ExpThreshold:  int(threshold),
	}, nil
}

func (s *ServerService) ServerPaddingTrigger(ctx *gin.Context, params *schema.ServerPaddingTriggerReq) (*schema.ServerPaddingTriggerRes, *errors.Err) {
	defer util.TimeCost("ServerPaddingTrigger")()
	currentHour := time.Now().Hour()
	hour := lo.Max([]int32{params.LikeCfg.ReleaseHour, params.CommentCfg.ReleaseHour, params.CollectCfg.ReleaseHour, int32(currentHour) + 1})
	workRepo := repo.NewWorkRepo()
	userRepo := repo.NewUserRepo()
	esRepo := es.NewWorkEsRepo()
	workCache := cache.GetWorkCache()
	workId, err := workRepo.GetIdByGteCTime(ctx, time.Now().UnixMilli()-int64(hour)*3600*1000)
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get work id by gte ctime failed")
		return nil, errors.New(errors.ServerError)
	}
	workId = workId - 1
	limit := 10000

	list := []model.L36ActionWork{}
	var fakeLikeCount, fakeCommentCount, fakeCollectCount int32
	now := time.Now().UnixMilli()

	maxAddCache := cache.NewPaddingDailyMaxCount()
	maxLikeGteThreshold, err := maxAddCache.Get(ctx, cache.PaddingDailyMaxCountParams{
		Type:            "like",
		MinGteThreshold: params.LikeCfg.GteThresholdsMin,
		MaxGteThreshold: params.LikeCfg.GteThresholdsMax,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get max like gte threshold failed")
		return nil, errors.New(errors.ServerError)
	}
	maxCommentGteThreshold, err := maxAddCache.Get(ctx, cache.PaddingDailyMaxCountParams{
		Type:            "comment",
		MinGteThreshold: params.CommentCfg.GteThresholdsMin,
		MaxGteThreshold: params.CommentCfg.GteThresholdsMax,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get max comment gte threshold failed")
		return nil, errors.New(errors.ServerError)
	}
	maxCollectGteThreshold, err := maxAddCache.Get(ctx, cache.PaddingDailyMaxCountParams{
		Type:            "collect",
		MinGteThreshold: params.CollectCfg.GteThresholdsMin,
		MaxGteThreshold: params.CollectCfg.GteThresholdsMax,
	})
	if err != nil {
		log.LogWithContext(ctx).WithError(err).Error("get max collect gte threshold failed")
		return nil, errors.New(errors.ServerError)
	}

	ret := &schema.ServerPaddingTriggerRes{
		LikeData:    schema.PaddingResItem{},
		CommentData: schema.PaddingResItem{},
		CollectData: schema.PaddingResItem{},
	}

	for {
		query := workRepo.NormalScope(ctx).Where("id > ? and visibility = ?", workId, constant.VisibilityPublic).Order("id asc").Limit(limit)
		err := query.Find(&list).Error
		if err != nil {
			log.LogWithContext(ctx).WithError(err).Error("find works by id failed")
			return nil, errors.New(errors.ServerError)
		}
		log.LogWithContext(ctx).WithField("workId", workId).Info("ServerPaddingTrigger process")

		for _, work := range list {

			var add int32
			if params.LikeCfg.OpenStatus == 1 &&
				now-work.Ctime < int64(params.LikeCfg.ReleaseHour)*3600*1000 &&
				work.LikeCount >= params.LikeCfg.MinRealThreshold &&
				work.FakeLikeCount < work.LikeCount*params.LikeCfg.GtePercent/100 &&
				(work.FakeLikeCount+work.LikeCount < maxLikeGteThreshold || maxLikeGteThreshold == 0) {
				canAddMaxCount := math.Ceil(math.Min(float64(work.LikeCount)*float64(params.LikeCfg.GtePercent)/100, float64(maxLikeGteThreshold)) - float64(work.FakeLikeCount))
				add = int32(math.Min(
					math.Ceil(util.GetRandomFloat64(float64(params.LikeCfg.GteMinRatio), float64(params.LikeCfg.GteMaxRatio))/100*float64(work.LikeCount)),
					canAddMaxCount))
				if add > 0 {
					fakeLikeCount += add
					work.FakeLikeCount += add
					signalHot, _ := NewWorkService().getDefaultHotAndExp(repo.FieldLikeCount)
					hot := signalHot * int(add)
					err := workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
						"fake_like_count": gorm.Expr("fake_like_count + ?", add),
						"hot":             gorm.Expr("hot + ?", hot),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work fake like count failed")
						continue
					}
					// 新增实习活动埋点日志
					util.LogYunying("l36_action_work_like_change", map[string]interface{}{
						"work_id":          work.ID,
						"designer_id":      work.UserID,
						"designer_role_id": work.RoleID,
						"view_like_count":  work.LikeCount + work.FakeLikeCount,
					})

					err = userRepo.UpdateById(ctx, work.UserID, map[string]interface{}{
						"liked_count": gorm.Expr("liked_count + ?", add),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update user liked count failed")
						continue
					}

					err = esRepo.Update(ctx, int(work.ID), map[string]interface{}{
						"like_count": work.LikeCount + add,
						"hot":        work.Hot + int32(hot),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work es failed")
					}

					_, err = workCache.Incr(ctx, work.ID, map[string]interface{}{
						"fake_like_count": add,
						"hot":             hot,
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work cache failed")
					}
				}
			}

			allLikeCount := work.LikeCount + work.FakeLikeCount
			if allLikeCount > ret.LikeData.AllCount {
				ret.LikeData.AllCount = allLikeCount
				ret.LikeData.RealCount = work.LikeCount
				ret.LikeData.PaddingCount = work.FakeLikeCount
				ret.LikeData.WorkName = work.Name
				ret.LikeData.WorkId = work.ID
				ret.LikeData.WorkCreateTime = work.Ctime
				ret.LikeData.GetThresholdsMin = params.LikeCfg.GteThresholdsMin
				ret.LikeData.GetThresholdsMax = params.LikeCfg.GteThresholdsMax
			}

			add = 0
			if params.CommentCfg.OpenStatus == 1 &&
				now-work.Ctime < int64(params.CommentCfg.ReleaseHour)*3600*1000 &&
				work.CommentCount >= params.CommentCfg.MinRealThreshold &&
				work.FakeCommentCount < work.CommentCount*params.CommentCfg.GtePercent/100 &&
				(work.FakeCommentCount+work.CommentCount < maxCommentGteThreshold || maxCommentGteThreshold == 0) {
				canAddMaxCount := math.Ceil(math.Min(float64(work.CommentCount)*float64(params.CommentCfg.GtePercent)/100, float64(maxCommentGteThreshold)) - float64(work.FakeCommentCount))
				add = int32(math.Min(
					math.Ceil(util.GetRandomFloat64(float64(params.CommentCfg.GteMinRatio), float64(params.CommentCfg.GteMaxRatio))/100*float64(work.CommentCount)),
					canAddMaxCount))
				if add > 0 {
					fakeCommentCount += add
					work.FakeCommentCount += add
					signalHot, _ := NewWorkService().getDefaultHotAndExp(repo.FieldCommentCount)
					hot := signalHot * int(add)
					err := workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
						"fake_comment_count": gorm.Expr("fake_comment_count + ?", add),
						"hot":                gorm.Expr("hot + ?", hot),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work fake comment count failed")
						continue
					}

					err = esRepo.Update(ctx, int(work.ID), map[string]interface{}{
						"comment_count": work.CommentCount + add,
						"hot":           work.Hot + int32(hot),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work es failed")
					}

					_, err = workCache.Incr(ctx, work.ID, map[string]interface{}{
						"fake_comment_count": add,
						"hot":                hot,
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work cache failed")
					}
				}
			}

			allCommentCount := work.CommentCount + work.FakeCommentCount
			if allCommentCount > ret.CommentData.AllCount {
				ret.CommentData.AllCount = allCommentCount
				ret.CommentData.RealCount = work.CommentCount
				ret.CommentData.PaddingCount = work.FakeCommentCount
				ret.CommentData.WorkName = work.Name
				ret.CommentData.WorkId = work.ID
				ret.CommentData.WorkCreateTime = work.Ctime
				ret.CommentData.GetThresholdsMin = params.CommentCfg.GteThresholdsMin
				ret.CommentData.GetThresholdsMax = params.CommentCfg.GteThresholdsMax
			}

			add = 0

			if params.CollectCfg.OpenStatus == 1 &&
				now-work.Ctime < int64(params.CollectCfg.ReleaseHour)*3600*1000 &&
				work.CollectCount >= params.CollectCfg.MinRealThreshold &&
				work.FakeCollectCount < work.CollectCount*params.CollectCfg.GtePercent/100 &&
				(work.FakeCollectCount+work.CollectCount < maxCollectGteThreshold || maxCollectGteThreshold == 0) {
				canAddMaxCount := math.Ceil(math.Min(float64(work.CollectCount)*float64(params.CollectCfg.GtePercent)/100, float64(maxCollectGteThreshold)) - float64(work.FakeCollectCount))
				add = int32(math.Min(
					math.Ceil(util.GetRandomFloat64(float64(params.CollectCfg.GteMinRatio), float64(params.CollectCfg.GteMaxRatio))/100*float64(work.CollectCount)),
					canAddMaxCount))
				if add > 0 {
					fakeCollectCount += add
					work.FakeCollectCount += add
					signalHot, _ := NewWorkService().getDefaultHotAndExp(repo.FieldCollectCount)
					hot := signalHot * int(add)

					err := workRepo.UpdateById(ctx, work.ID, map[string]interface{}{
						"fake_collect_count": gorm.Expr("fake_collect_count + ?", add),
						"hot":                gorm.Expr("hot + ?", hot),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work fake collect count failed")
						continue
					}

					err = esRepo.Update(ctx, int(work.ID), map[string]interface{}{
						"collect_count": work.CollectCount + add,
						"hot":           work.Hot + int32(hot),
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work es failed")
					}

					_, err = workCache.Incr(ctx, work.ID, map[string]interface{}{
						"fake_collect_count": add,
						"hot":                hot,
					})
					if err != nil {
						log.LogWithContext(ctx).WithError(err).Error("update work cache failed")
					}
				}
			}

			allCollectCount := work.CollectCount + work.FakeCollectCount
			if allCollectCount > ret.CollectData.AllCount {
				ret.CollectData.AllCount = allCollectCount
				ret.CollectData.RealCount = work.CollectCount
				ret.CollectData.PaddingCount = work.FakeCollectCount
				ret.CollectData.WorkName = work.Name
				ret.CollectData.WorkId = work.ID
				ret.CollectData.WorkCreateTime = work.Ctime
				ret.CollectData.GetThresholdsMin = params.CollectCfg.GteThresholdsMin
				ret.CollectData.GetThresholdsMax = params.CollectCfg.GteThresholdsMax
			}

		}
		if len(list) < limit {
			break
		}
		workId = list[len(list)-1].ID
	}
	log.LogWithContext(ctx).WithFields(elog.Fields{
		"like_data":          ret.LikeData,
		"comment_data":       ret.CommentData,
		"collect_data":       ret.CollectData,
		"like_cfg":           params.LikeCfg,
		"comment_cfg":        params.CommentCfg,
		"collect_cfg":        params.CollectCfg,
		"fake_like_count":    fakeLikeCount,
		"fake_comment_count": fakeCommentCount,
		"fake_collect_count": fakeCollectCount,
	}).Info("ServerPaddingTrigger end")
	return ret, nil
}
