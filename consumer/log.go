package consumer

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/araddon/dateparse"
)

type LogRecord struct {
	LogName string
	LogTime time.Time
	Payload string
}

const logReg = `\[(.*?)\]\[(.*?)\],(\{.*\})$`

// [2024-04-10 17:41:40][1][871297] /gamelog/bi/301/l22_gas301_logic-32_2024041017_192.168.21.62.bi.log [2024-04-10 17:41:40 +0800][RoleInfo],{"server":301,"role_name":"护花剑","role_id":"3ph700000*************","urs":"***********","account_id":"<EMAIL>","old_accountid":"","aid":"982813","login_time":**********,"role_level":20,"second_platform":"Android","platform":"Android","app_channel":"netease","login_channel":"netease","uid":"*************","role_icon":3430147,"role_frame":********,"role_occup":"","role_school":"","flag_roledelete":0,"flag_speechmute":0,"survive_single_grade":5020011,"survive_single_rank_score":1541,"survive_double_grade":5020001,"survive_double_rank_score":1026,"survive_triplex_grade":5020001,"survive_triplex_rank_score":1009,"survive_kill_single_grade":5021001,"survive_kill_single_rank_score":3000,"survive_kill_double_grade":5021001,"survive_kill_double_rank_score":3000,"survive_kill_triplex_grade":5021001,"survive_kill_triplex_rank_score":3000,"season_id":9620001}
func ParseLog(str string) (*LogRecord, error) {
	str = strings.TrimSpace(str)
	if str == "" {
		return nil, fmt.Errorf("empty log string")
	}
	reg := regexp.MustCompile(logReg)
	arr := reg.FindStringSubmatch(str)
	if arr == nil || len(arr) != 4 {
		return nil, fmt.Errorf("invalid log format")
	}

	// 验证JSON格式
	if !json.Valid([]byte(arr[3])) {
		return nil, fmt.Errorf("invalid JSON payload")
	}

	logTime, err := dateparse.ParseLocal(arr[1])
	if err != nil {
		return nil, fmt.Errorf("parse time error: %v", err)
	}

	logName := arr[2]
	payload := arr[3]

	return &LogRecord{
		LogName: logName,
		LogTime: logTime,
		Payload: payload,
	}, nil
}
