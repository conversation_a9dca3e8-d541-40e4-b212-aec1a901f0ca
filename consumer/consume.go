package consumer

import (
	"context"
	"fmt"
	"time"

	"app/config"
	"app/schema"
	"app/service"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/bytedance/sonic"
	"github.com/panjf2000/ants/v2"
	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/plain"
)

func Consume(ctx context.Context) {
	conf := config.C

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: []string{conf.Kafka.Address},
		Topic:   conf.Kafka.Topic,
		GroupID: conf.Kafka.Group,
		Dialer: &kafka.Dialer{
			Timeout:   10 * time.Second,
			DualStack: true,
			SASLMechanism: plain.Mechanism{
				Username: conf.Kafka.User,
				Password: conf.Kafka.Password,
			},
		},
		ErrorLogger:    kafka.LoggerFunc(elog.Error),
		CommitInterval: time.Second,
	})

	fmt.Println("Kafka连接已创建, Brokers:", conf.Kafka.Address, "Topic:", conf.Kafka.Topic, "GroupID:", conf.Kafka.Group)

	defer r.Close()

	// 创建 ants 协程池
	pool, err := ants.NewPool(100,
		ants.WithPreAlloc(true),
	)
	if err != nil {
		elog.WithError(err).Error("Failed to create ants pool")
		return
	}
	defer pool.Release()

	for {
		select {
		case <-ctx.Done():
			elog.Info("Context cancelled, stopping consumer...")
			return
		default:
			m, err := r.FetchMessage(ctx)
			if err != nil {
				elog.WithError(err).Error("Consumer.FetchMessage")
				return
			}
			message := m // 创建一个副本

			err = pool.Submit(func() {
				processMessage(ctx, &message)
				if err := r.CommitMessages(ctx, message); err != nil {
					elog.WithError(err).Error("Failed to commit message")
				}
			})
			if err != nil {
				elog.WithError(err).Error("Failed to submit task to pool")
			}
		}
	}
}

// processMessage 处理消息
func processMessage(ctx context.Context, message *kafka.Message) {
	text := string(message.Value)
	logRecord, err := ParseLog(text)
	if err != nil {
		elog.WithError(err).WithField("message", text).Info("Consumer.ParseLog")
		return
	}
	if logRecord == nil {
		elog.WithField("message", text).Error("Consumer.ParseLog")
		return
	}
	if logRecord.LogName == "GacScreenPlaySite_SuccessViewWork_Action" {
		OnGacScreenPlaySite_SuccessViewWork_Action(ctx, logRecord)
	}
}

func OnGacScreenPlaySite_SuccessViewWork_Action(ctx context.Context, logRecord *LogRecord) {
	var event schema.PlaySiteSuccessViewWorkActionEvent
	if err := sonic.Unmarshal([]byte(logRecord.Payload), &event); err != nil {
		elog.WithError(err).WithField("message", logRecord.Payload).Error("Consumer.OnGacScreenPlaySite_SuccessViewWork_Action.Unmarshal")
		return
	}

	workService := service.NewWorkService()
	workService.OnWorkSuccessView(ctx, &schema.WorkCompletePlayReq{
		UserBase: schema.UserBase{
			UserId: event.UserID,
			RoleId: event.RoleID,
		},
		WorkBase: schema.WorkBase{
			WorkId: event.WorkID,
		},
		TraceOptBase: schema.TraceOptBase{
			TraceBase: schema.TraceBase{
				TraceId: event.TraceId,
			},
			Scm: event.Scm,
		},
	})
}
