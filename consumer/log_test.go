package consumer

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestParseLog(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		wantErr  bool
		expected *LogRecord
	}{
		{
			name:    "正常日志-RankDiffFightProp",
			input:   `[2025-04-22 09:02:24][RankDiffFightProp],{"server":33,"value":517,"value_no_buff":381,"role_id":6497100033,"prop_str":"pAttMax","__event_id":400582}`,
			wantErr: false,
			expected: &LogRecord{
				LogName: "RankDiffFightProp",
				LogTime: time.Date(2025, 4, 22, 9, 2, 24, 0, time.Local),
				Payload: `{"server":33,"value":517,"value_no_buff":381,"role_id":6497100033,"prop_str":"pAttMax","__event_id":400582}`,
			},
		},
		{
			name:    "正常日志-minor_chat",
			input:   `[2025-05-24 14:35:43][minor_chat],{"server":11,"total_charge":518170,"role_id":115700011}`,
			wantErr: false,
			expected: &LogRecord{
				LogName: "minor_chat",
				LogTime: time.Date(2025, 5, 24, 14, 35, 43, 0, time.Local),
				Payload: `{"server":11,"total_charge":518170,"role_id":115700011}`,
			},
		},
		{
			name:    "正常日志-带时区",
			input:   `[2024-04-10 17:41:40 +0800][RoleInfo],{"server":301,"role_name":"test"}`,
			wantErr: false,
			expected: &LogRecord{
				LogName: "RoleInfo",
				LogTime: time.Date(2024, 4, 10, 17, 41, 40, 0, time.Local),
				Payload: `{"server":301,"role_name":"test"}`,
			},
		},
		{
			name:    "格式错误-缺少中括号",
			input:   `2025-04-22 09:02:24][RankDiffFightProp],{"server":33}`,
			wantErr: true,
		},
		{
			name:    "格式错误-缺少JSON",
			input:   `[2025-04-22 09:02:24][RankDiffFightProp]`,
			wantErr: true,
		},
		{
			name:    "格式错误-JSON格式不完整",
			input:   `[2025-04-22 09:02:24][RankDiffFightProp],{"server":33`,
			wantErr: true,
		},
		{
			name:    "格式错误-时间格式错误",
			input:   `[2025-13-22 09:02:24][RankDiffFightProp],{"server":33}`,
			wantErr: true,
		},
		{
			name:    "空字符串",
			input:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseLog(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, got)
			assert.Equal(t, tt.expected.LogName, got.LogName)
			assert.Equal(t, tt.expected.Payload, got.Payload)
			// 由于时区问题,这里只比较Unix时间戳
			assert.Equal(t, tt.expected.LogTime.Unix(), got.LogTime.Unix())
		})
	}
}
