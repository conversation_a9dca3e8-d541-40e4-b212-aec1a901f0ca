package cache

import (
	"context"
	"errors"
	"time"

	"app/config"

	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"golang.org/x/exp/constraints"
	"golang.org/x/sync/singleflight"
)

var sf singleflight.Group = singleflight.Group{}

var ErrCacheGetInvalidType = errors.New("cache get invalid type")

// 声明
type Cacheable[T comparable, F any] interface {
	GetKey(params T) string
	GetWithoutRefresh(ctx context.Context, params T) (F, error)
	GetExpire(params T, result F) time.Duration
}

type RedisCache[T comparable, F any] struct {
	rds redis.UniversalClient
	Cacheable[T, F]
	CustomGetWithoutRefresh func(ctx context.Context, params []T) (map[T]F, error)
}

func NewRedisCache[T comparable, F any](rds redis.UniversalClient, imp Cacheable[T, F]) *RedisCache[T, F] {
	return &RedisCache[T, F]{rds: rds, Cacheable: imp}
}

// Get 从缓存中获取数据，如果没有则从数据库中获取
// params: 从缓存中获取数据的参数
// value: 从缓存中获取的数据
func (r *RedisCache[T, F]) Get(ctx context.Context, params T) (value F, err error) {
	key := r.getFullKey(params)
	result, err := r.rds.Get(ctx, key).Result()
	if err == redis.Nil {
		value, err = r.Refresh(ctx, params)
		if err != nil {
			return value, err
		}
		return value, nil
	}
	if err != nil {
		return value, err
	}
	err = sonic.Unmarshal([]byte(result), &value)
	if err != nil {
		return value, err
	}
	return value, err
}

func (r *RedisCache[T, F]) GetInt(ctx context.Context, params T) (int, error) {
	key := r.getFullKey(params)
	return r.rds.Get(ctx, key).Int()
}

func (r *RedisCache[T, F]) Set(ctx context.Context, params T, value F) (err error) {
	key := r.getFullKey(params)
	expire := r.GetExpire(params, value)
	bytes, err := sonic.Marshal(value)
	if err != nil {
		return err
	}
	err = r.rds.Set(ctx, key, bytes, expire).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r *RedisCache[T, F]) Refresh(ctx context.Context, params T) (F, error) {
	key := r.GetKey(params)
	value, err, _ := sf.Do(key, func() (interface{}, error) {
		return r.GetWithoutRefresh(ctx, params)
	})
	sf.Forget(key)
	result, ok := value.(F)
	if !ok {
		return *new(F), ErrCacheGetInvalidType
	}
	if err != nil {
		return result, err
	}
	err = r.Set(ctx, params, result)
	return result, err
}

func (r *RedisCache[T, F]) Del(ctx context.Context, params T) error {
	key := r.getFullKey(params)
	err := r.rds.Del(ctx, key).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r *RedisCache[T, F]) getFullKey(params T) string {
	key := r.GetKey(params)
	return config.C.Redis.Prefix + key
}

// 以下为批量string操作
func (r *RedisCache[T, F]) Gets(ctx context.Context, params []T) (res []F, err error) {
	keys := make([]string, len(params))
	if len(params) == 0 {
		return
	}
	for i, param := range params {
		keys[i] = r.getFullKey(param)
	}
	results, err := r.rds.MGet(context.Background(), keys...).Result()
	if err != nil {
		return nil, err
	}
	tmpRes := make(map[T]F)
	nullParams := make([]T, 0)
	for i, result := range results {
		value := new(F)
		if result == nil || result == "null" {
			nullParams = append(nullParams, params[i])
		} else {
			err = sonic.Unmarshal([]byte(result.(string)), &value)
		}
		if err != nil {
			return nil, err
		}
		tmpRes[params[i]] = *value
	}

	if len(nullParams) > 0 {
		refreshesRet, errRefreshes := r.Refreshes(ctx, nullParams)
		if errRefreshes != nil {
			return nil, errRefreshes
		}
		for k, v := range refreshesRet {
			tmpRes[k] = v
		}
	}

	return lo.Map(params, func(param T, i int) F {
		return tmpRes[param]
	}), nil
}

func (r *RedisCache[T, F]) Refreshes(ctx context.Context, params []T) (res map[T]F, err error) {
	ret, err := r.FetchDataSources(ctx, params)
	if err != nil {
		return nil, err
	}
	err = r.MSet(ctx, ret)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (r *RedisCache[T, F]) FetchDataSources(ctx context.Context, params []T) (map[T]F, error) {
	if r.CustomGetWithoutRefresh != nil {
		return r.CustomGetWithoutRefresh(ctx, params)
	}
	ret := make(map[T]F)
	for _, param := range params {
		value, err := r.GetWithoutRefresh(ctx, param)
		if err != nil {
			return nil, err
		}
		ret[param] = value
	}
	return ret, nil
}

func (r *RedisCache[T, F]) MSet(ctx context.Context, params map[T]F) error {
	pipe := r.rds.Pipeline()
	for key, value := range params {
		keyStr := r.getFullKey(key)
		bytes, err := sonic.Marshal(value)
		if err != nil {
			return err
		}
		pipe.Set(ctx, keyStr, bytes, r.GetExpire(key, value))
	}
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

// 临时计数器缓存, 不会去数据库中获取
type TempCounterCache[T comparable, F constraints.Integer] struct {
	rds redis.UniversalClient
	Cacheable[T, F]
}

func NewTempCounterCache[T comparable, F constraints.Integer](rds redis.UniversalClient, imp Cacheable[T, F]) *TempCounterCache[T, F] {
	return &TempCounterCache[T, F]{rds: rds, Cacheable: imp}
}

func (r *TempCounterCache[T, F]) getKey(params T) string {
	key := r.GetKey(params)
	return config.C.Redis.Prefix + key
}

func (r *TempCounterCache[T, F]) Incr(ctx context.Context, params T) (F, error) {
	key := r.getKey(params)
	result, err := r.rds.Incr(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	return F(result), nil
}

func (r *TempCounterCache[T, F]) Get(ctx context.Context, params T) (F, error) {
	key := r.getKey(params)
	result, err := r.rds.Get(ctx, key).Int64()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	return F(result), nil
}

func (r *TempCounterCache[T, F]) Del(ctx context.Context, params T) error {
	key := r.getKey(params)
	err := r.rds.Del(ctx, key).Err()
	if err != nil {
		return err
	}
	return nil
}

// 计数器缓存，会去数据库中获取
type CounterCache[T comparable, F constraints.Integer] struct {
	rds redis.UniversalClient
	Cacheable[T, F]
}

func NewCounterCache[T comparable, F constraints.Integer](rds redis.UniversalClient, imp Cacheable[T, F]) *CounterCache[T, F] {
	return &CounterCache[T, F]{rds: rds, Cacheable: imp}
}

func (r *CounterCache[T, F]) getFullKey(params T) string {
	key := r.GetKey(params)
	return config.C.Redis.Prefix + key
}

func (r *CounterCache[T, F]) Set(ctx context.Context, params T, value F) error {
	key := r.getFullKey(params)
	expire := r.GetExpire(params, value)
	err := r.rds.Set(ctx, key, value, expire).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r *CounterCache[T, F]) Refresh(ctx context.Context, params T) (F, error) {
	key := r.getFullKey(params)
	value, err, _ := sf.Do(key, func() (interface{}, error) {
		return r.GetWithoutRefresh(ctx, params)
	})
	sf.Forget(key)
	result, ok := value.(F)
	if !ok {
		return *new(F), ErrCacheGetInvalidType
	}
	if err != nil {
		return result, err
	}
	err = r.Set(ctx, params, result)
	return result, err
}

func (r *CounterCache[T, F]) Get(ctx context.Context, params T) (F, error) {
	key := r.getFullKey(params)
	result, err := r.rds.Get(ctx, key).Int64()
	if err == redis.Nil {
		value, err := r.Refresh(ctx, params)
		if err != nil {
			return 0, err
		}
		return value, nil
	}
	if err != nil {
		return 0, err
	}
	return F(result), nil
}

func (r *CounterCache[T, F]) Incr(ctx context.Context, params T) (F, error) {
	// 先从缓存中获取，如果没有则从数据库中获取
	key := r.getFullKey(params)
	_, err := r.rds.Get(ctx, key).Int64()
	if err == redis.Nil {
		return r.Refresh(ctx, params)
	}
	if err != nil {
		return 0, err
	}
	result, err := r.rds.Incr(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	return F(result), nil
}
