package cache

import (
	"context"
	"time"

	"app/conn"
	"app/dao/model"
	"app/dao/repo"
	"app/schema"

	"github.com/samber/lo"
)

type RecommendTopicCache struct{}

func (c *RecommendTopicCache) GetKey(params int) string {
	return "RecommendTopic"
}

func (c *RecommendTopicCache) GetExpire(params int, result []model.L36ActionTopic) time.Duration {
	return time.Minute * 5
}

func (c *RecommendTopicCache) GetWithoutRefresh(ctx context.Context, params int) (result []model.L36ActionTopic, err error) {
	recommendTopicRepo := repo.NewRecommendTopicRepo()
	recommendTopics, err := recommendTopicRepo.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	topicIds := lo.Map(recommendTopics, func(recommendTopic *model.L36ActionRecommendTopic, _ int) int64 {
		return recommendTopic.TopicID
	})
	topicRepo := repo.NewTopicRepo()
	return topicRepo.FindByIds(ctx, topicIds)
}

func NewRecommendTopicCache() *RedisCache[int, []model.L36ActionTopic] {
	return NewRedisCache(conn.GetRedisConn(), &RecommendTopicCache{})
}

type HotTopicCache struct{}

func (c *HotTopicCache) GetKey(params int) string {
	return "HotTopics"
}

func (c *HotTopicCache) GetExpire(params int, result []model.L36ActionTopic) time.Duration {
	return time.Minute * 5
}

func (c *HotTopicCache) GetWithoutRefresh(ctx context.Context, params int) (result []model.L36ActionTopic, err error) {
	// 获取有效的置顶话题
	topTopicRepo := repo.NewTopTopicRepo()
	topTopics, err := topTopicRepo.GetValidTopTopics(ctx)
	if err != nil {
		return nil, err
	}

	// 获取置顶话题的 ID
	topTopicIDs := lo.Map(topTopics, func(topic model.L36ActionTopTopic, _ int) int64 {
		return topic.TopicID
	})

	// 获取置顶话题的详细信息
	topicRepo := repo.NewTopicRepo()
	topTopicDetails, err := topicRepo.FindByIds(ctx, topTopicIDs)
	if err != nil {
		return nil, err
	}

	topTopicMap := lo.KeyBy(topTopicDetails, func(topic model.L36ActionTopic) int64 {
		return topic.ID
	})

	allTopics := []model.L36ActionTopic{}
	for _, topTopic := range topTopics {
		topic, ok := topTopicMap[topTopic.TopicID]
		if !ok {
			continue
		}
		allTopics = append(allTopics, topic)
	}

	// 获取其他热门话题
	otherTopics, err := topicRepo.ListTopics(ctx, &schema.TopicListReq{
		Pagination: schema.Pagination{
			Page:     1,
			PageSize: 100,
		},
	})
	if err != nil {
		return nil, err
	}

	for _, topic := range otherTopics {
		if !lo.Contains(topTopicIDs, topic.ID) {
			allTopics = append(allTopics, topic)
		}
	}

	return allTopics, nil
}

func NewHotTopicCache() *RedisCache[int, []model.L36ActionTopic] {
	return NewRedisCache(conn.GetRedisConn(), &HotTopicCache{})
}
