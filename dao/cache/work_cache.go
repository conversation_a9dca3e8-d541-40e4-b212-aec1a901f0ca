package cache

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"app/config"
	"app/conn"
	"app/constant"
	"app/dao/model"
	"app/dao/repo"
	"app/schema"
	"app/util"

	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

type BackupRecommendCache struct{}

func (c *BackupRecommendCache) GetKey(workType int) string {
	return fmt.Sprintf("backup_recommend:%d", workType)
}

func (c *BackupRecommendCache) GetExpire(workType int, result []int64) time.Duration {
	return time.Hour * 24
}

func (c *BackupRecommendCache) GetWithoutRefresh(ctx context.Context, workType int) (result []int64, err error) {
	works, err := repo.NewWorkRepo().GetTopHotWorks(ctx, workType, config.C.Fuxi.BackupCount)
	if err != nil {
		return nil, err
	}
	return lo.Map(works, func(work model.L36ActionWork, i int) int64 {
		return work.ID
	}), nil
}

func NewBackupRecommendCache() *RedisCache[int, []int64] {
	return NewRedisCache(conn.GetRedisConn(), &BackupRecommendCache{})
}

type WorkHotRankCache struct{}

func (c *WorkHotRankCache) GetKey(workType int) string {
	return "WorkHotRank"
}

func (c *WorkHotRankCache) GetExpire(workType int, result []schema.RankWorkItem) time.Duration {
	return time.Hour
}

func (c *WorkHotRankCache) GetWithoutRefresh(ctx context.Context, workType int) (result []schema.RankWorkItem, err error) {
	works, err := repo.NewWorkRepo().GetAllTopHotWorks(ctx, 100)
	if err != nil {
		return result, err
	}

	userIds := lo.Map(works, func(work model.L36ActionWork, i int) int64 {
		return work.UserID
	})
	users, err := repo.NewUserRepo().FindByIds(ctx, userIds)
	if err != nil {
		return result, err
	}
	userMap := lo.KeyBy(users, func(user model.L36ActionUser) int64 {
		return user.ID
	})
	roleMap, err := repo.NewRoleRepo().FindRelatedRoleMap(ctx, users)
	if err != nil {
		return result, err
	}

	result = lo.Map(works, func(work model.L36ActionWork, i int) schema.RankWorkItem {
		user := userMap[work.UserID]
		avatarRole := roleMap[user.AvatarRoleID]
		nameRole := roleMap[user.NameRoleID]
		return schema.RankWorkItem{
			WorkId:  work.ID,
			Name:    work.Name,
			Summary: work.Summary,
			Video:   work.Video,
			Images:  util.JsonParseStrArray(work.Images),
			Hot:     int(work.Hot),
			Rank:    i + 1,
			Designer: schema.DesignerInfo{
				ID:     user.ID,
				Name:   nameRole.GetRoleName(&user),
				RoleId: user.RoleID,
				Avatar: avatarRole.GetAvatarInfo(),
			},
		}
	})
	return result, nil
}

func NewWorkHotRankCache() *RedisCache[int, []schema.RankWorkItem] {
	return NewRedisCache(conn.GetRedisConn(), &WorkHotRankCache{})
}

// 单个work cache
type WorkCacheClass struct {
	workRepo *repo.WorkRepo
	fieldMap map[string]fieldInfo // 预计算的字段映射
}

// fieldInfo 存储结构体字段信息，用于快速查找和设置
type fieldInfo struct {
	Index int          // 结构体字段索引
	Kind  reflect.Kind // 字段类型
	Name  string       // 结构体字段名 (CamelCase)
}

func (c *WorkCacheClass) GetKey(workId int64) string {
	return fmt.Sprintf("workDetail:%d", workId)
}

func (c *WorkCacheClass) GetExpire(workId int64, result model.L36ActionWork) time.Duration {
	return time.Minute * 30
}

func (c *WorkCacheClass) FetchDataSource(ctx context.Context, workId int64) (result model.L36ActionWork, err error) {
	work, err := c.workRepo.FindById(ctx, workId)
	if err != nil {
		return result, err
	}
	// 如果找不到work，返回一个空的work，避免缓存穿透攻击（写入空hash）
	if work == nil {
		// 返回一个零值 L36ActionWork，让调用方写入 hash_empty_flag
		return model.L36ActionWork{}, fmt.Errorf("work not found for id %d, preventing cache penetration", workId)
	}
	return *work, nil
}

// 优化后的 Format 方法
func (c *WorkCacheClass) Format(value map[string]string) (model.L36ActionWork, error) {
	work := model.L36ActionWork{}
	tmpObj := map[string]interface{}{}
	for k, v := range value {
		// 跳过空值或标记
		if v == "" || k == "hash_empty_flag" {
			continue
		}

		info, found := c.fieldMap[k]

		if !found {
			// 可以选择记录未找到的字段或忽略
			// log.Printf("Warning: Field mapping not found for key: %s", k)
			continue
		}
		switch info.Kind {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			intVal, _ := strconv.ParseInt(v, 10, 64)
			tmpObj[k] = intVal
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			uintVal, _ := strconv.ParseUint(v, 10, 64)
			tmpObj[k] = uintVal
		case reflect.Float32, reflect.Float64:
			floatVal, _ := strconv.ParseFloat(v, 64)
			tmpObj[k] = floatVal
		case reflect.Bool:
			boolVal, _ := strconv.ParseBool(v)
			tmpObj[k] = boolVal
		case reflect.String:
			tmpObj[k] = v
		default:
		}
	}
	tmpStr := util.JsonStringify(tmpObj)
	util.JsonParseWithDefaultValue(tmpStr, &work)

	return work, nil
}

// toSnakeCase 将驼峰命名转换为下划线命名
func toSnakeCase(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// NewWorkCacheClass 初始化 WorkCacheClass 并预计算字段映射
func NewWorkCacheClass() *GenericHashCache[int64, model.L36ActionWork] {
	workRepo := repo.NewWorkRepo()
	fieldMap := make(map[string]fieldInfo)
	workType := reflect.TypeOf(model.L36ActionWork{})

	// English Title casing
	caser := cases.Title(language.English)

	for i := 0; i < workType.NumField(); i++ {
		field := workType.Field(i)
		fieldName := field.Name // CamelCase Name

		// 优先使用 json tag
		jsonTag := field.Tag.Get("json")
		key := ""
		if jsonTag != "" && jsonTag != "-" {
			parts := strings.Split(jsonTag, ",")
			key = parts[0]
		}

		// 如果没有 json tag，尝试 gorm tag
		if key == "" {
			gormTag := field.Tag.Get("gorm")
			if gormTag != "" && gormTag != "-" {
				// 简单解析 column:name
				const columnPrefix = "column:"
				if strings.Contains(gormTag, columnPrefix) {
					tags := strings.Split(gormTag, ";")
					for _, tag := range tags {
						if strings.HasPrefix(tag, columnPrefix) {
							key = strings.TrimPrefix(tag, columnPrefix)
							break
						}
					}
				}
			}
		}

		// 如果都没有 tag，使用字段名的 snake_case 形式
		if key == "" {
			key = toSnakeCase(fieldName)
		}

		// 存储映射信息
		if key != "" {
			// 检查 key 是否已存在，避免覆盖（例如不同字段指定了相同的 tag）
			if _, exists := fieldMap[key]; !exists {
				fieldMap[key] = fieldInfo{
					Index: i,
					Kind:  field.Type.Kind(),
					Name:  fieldName,
				}
			} else {
				// 可以记录日志，提示存在重复的 key 映射
				// log.Printf("Warning: Duplicate key mapping detected for key '%s'. Field '%s' might be shadowed.", key, fieldName)
			}
		}

		// 同时添加字段本身的 CamelCase 名称作为可能的 key
		// （以防 Redis 中直接存储了驼峰命名的 key）
		// 这里需要注意，如果 snake_case 和 CamelCase 相同（例如 "ID"），则无需重复添加
		camelKey := fieldName
		if _, exists := fieldMap[camelKey]; !exists && key != camelKey {
			fieldMap[camelKey] = fieldInfo{
				Index: i,
				Kind:  field.Type.Kind(),
				Name:  fieldName,
			}
		}

		// 为了兼容之前的 toCamelCase 逻辑可能产生的 key（首字母大写的驼峰）
		titleCamelKey := caser.String(fieldName)
		if _, exists := fieldMap[titleCamelKey]; !exists && key != titleCamelKey && camelKey != titleCamelKey {
			fieldMap[titleCamelKey] = fieldInfo{
				Index: i,
				Kind:  field.Type.Kind(),
				Name:  fieldName,
			}
		}
	}
	fieldMap["id"] = fieldInfo{
		Index: 0,
		Kind:  reflect.Int64,
		Name:  "ID",
	}
	fieldMap["ctime"] = fieldInfo{
		Index: 0,
		Kind:  reflect.Int64,
		Name:  "Ctime",
	}
	fieldMap["utime"] = fieldInfo{
		Index: 0,
		Kind:  reflect.Int64,
		Name:  "Utime",
	}

	cacheClass := &WorkCacheClass{
		workRepo: workRepo,
		fieldMap: fieldMap,
	}
	return NewGenericHashCache(conn.GetRedisConn(), cacheClass)
}

var workCache *GenericHashCache[int64, model.L36ActionWork]

var workCacheOnce = sync.Once{}

func GetWorkCache() *GenericHashCache[int64, model.L36ActionWork] {
	workCacheOnce.Do(func() {
		workCache = NewWorkCacheClass()
	})
	return workCache
}

// WorkCacheGets 函数保持不变，但现在会调用优化后的 Format
func WorkCacheGets(ctx context.Context, workIds []int64) (result []model.L36ActionWork, err error) {
	workRepo := repo.NewWorkRepo()
	workMap := make(map[int64]model.L36ActionWork)

	if len(workIds) == 0 {
		return []model.L36ActionWork{}, nil
	}

	// 使用Redis管道批量获取
	pipe := conn.GetRedisConn().Pipeline()
	for _, workId := range workIds {
		lurKey := fmt.Sprintf("workDetail_lru:%d", workId)
		if val, ok := GlobalLRUCache.Get(lurKey); ok {
			workMap[workId] = val.(model.L36ActionWork)
			continue
		}

		key := GetWorkCache().getFullKey(workId)
		pipe.HGetAll(ctx, key)
	}

	cmders, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 处理结果并记录未命中的ID
	notMatchWorkIds := make([]int64, 0)

	for i, cmd := range cmders {
		if i >= len(workIds) {
			continue
		}

		workId := workIds[i]

		// 跳过有错误的命令
		if cmd.Err() != nil {
			notMatchWorkIds = append(notMatchWorkIds, workId)
			continue
		}

		// 获取HGetAll的结果
		var hashResult map[string]string
		var ok bool = false

		switch typedCmd := cmd.(type) {
		case *redis.MapStringStringCmd:
			hashResult, err = typedCmd.Result()
			if err != nil {
				notMatchWorkIds = append(notMatchWorkIds, workId)
				continue
			}
			ok = true
		default:
			notMatchWorkIds = append(notMatchWorkIds, workId)
			continue
		}

		if !ok || len(hashResult) == 0 || (len(hashResult) == 1 && hashResult["hash_empty_flag"] == "1") {
			notMatchWorkIds = append(notMatchWorkIds, workId)
			continue
		}
		formattedWork, err := GetWorkCache().Format(hashResult)
		if err != nil {
			notMatchWorkIds = append(notMatchWorkIds, workId)
			continue
		}

		workMap[workId] = formattedWork

		if formattedWork.ID != 0 {
			lruKey := fmt.Sprintf("workDetail_lru:%d", formattedWork.ID)
			GlobalLRUCache.Add(lruKey, formattedWork, time.Second*10)
		}
	}

	// 获取未命中的数据并缓存
	if len(notMatchWorkIds) > 0 {
		notMatchWorks, err := workRepo.FindByIds(ctx, notMatchWorkIds)
		if err != nil {
			return nil, err
		}

		for _, work := range notMatchWorks {
			// 缓存到Redis
			pipe := conn.GetRedisConn().Pipeline()
			workId := work.ID
			key := GetWorkCache().getFullKey(workId)

			// 将work转换为map进行存储
			bytes, _ := sonic.Marshal(work)
			workData := make(map[string]interface{})
			_ = sonic.Unmarshal(bytes, &workData)

			if len(workData) > 0 {
				pipe.HSet(ctx, key, workData)
				pipe.Expire(ctx, key, GetWorkCache().GetExpire(workId, work))
			}

			_, err := pipe.Exec(ctx)
			if err != nil {
				return nil, err
			}

			workMap[workId] = work
		}
	}

	// 按照传入的workIds顺序返回结果
	for _, workId := range workIds {
		if work, ok := workMap[workId]; ok {
			result = append(result, work)
		} else {
			// 如果没有找到，返回空的model
			result = append(result, model.L36ActionWork{})
		}
	}

	return result, nil
}

func FindPublicByIds(ctx context.Context, workIds []int64) (result []model.L36ActionWork, err error) {
	works, err := WorkCacheGets(ctx, workIds)
	if err != nil {
		return nil, err
	}
	works = lo.Filter(works, func(work model.L36ActionWork, i int) bool {
		return work.Visibility == constant.VisibilityPublic && work.IsDelete == 0 && work.AuditStatus == int8(constant.FpReviewStatusPass)
	})

	return works, nil
}
