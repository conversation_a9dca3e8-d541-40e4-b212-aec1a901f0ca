package cache

import (
	"context"
	"fmt"
	"testing"

	"app/config"
	"app/conn"
	"app/util"
)

func init() {
	conf, _ := config.LoadConfig()
	conn.ConnectDB1(&conf.Mysql)
	conn.ConnectRedis1(&conf.Redis)
}

func TestWorkCacheClass_Get(t *testing.T) {
	work, err := GetWorkCache().Get(context.Background(), 2247)
	if err != nil {
		t.<PERSON>("Get work error: %v", err)
	} else {
		fmt.Printf("work: %v", util.JsonStringify(work))
	}
}

func TestWorkCacheClass_Gets(t *testing.T) {
	works, err := WorkCacheGets(context.Background(), []int64{2247, 2248, 2246, 2245})
	if err != nil {
		t.<PERSON>("Get works error: %v", err)
	} else {
		fmt.Printf("works: %v", util.JsonStringify(works))
	}
}
