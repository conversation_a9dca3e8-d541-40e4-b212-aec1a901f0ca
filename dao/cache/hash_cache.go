package cache

import (
	"context"
	"errors"
	"reflect"
	"time"

	"app/config"

	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
)

// Lua 脚本用于更新哈希中的字段值
const updateLuaScript = `
local function update(key, props)
	local result = redis.call('exists', key)
	if result ~= 1 then
		return 0
	end
	local is_empty = redis.call('hget', key, 'hash_empty_flag')
	if is_empty == '1' then
		redis.call('del', key)
		return 0
	end
	redis.call('hmset', key, unpack(props))
	return 1
end
return update(KEYS[1], ARGV)
`

// Lua 脚本用于增加哈希中字段的值
const incrLuaScriptStr = `
local function incr(key, props)
	local result = redis.call('exists', key)
	if result ~= 1 then
		return 0
	end
	local is_empty = redis.call('hget', key, 'hash_empty_flag')
	if is_empty == '1' then
		redis.call('del', key)
		return 0
	end
	for i = 1, #props, 2 do
		local field = props[i]
		local value = props[i + 1]
		local valueNum = tonumber(value)
		if valueNum > 0 then
			redis.call('hincrby', key, field, value)
		else
			local remote = redis.call('hget', key, field)
			if remote == nil then
				return 0
			end
			local num = tonumber(remote)
			if num < -valueNum then
				redis.call('hset', key, field, 0)
			else
				redis.call('hincrby', key, field, valueNum)
			end
		end
	end
	return 1
end
return incr(KEYS[1], ARGV)
`

var incrLuaScript = redis.NewScript(incrLuaScriptStr)

// 添加新的错误定义
var ErrRedisClientNotInitialized = errors.New("redis client is not initialized")

// HashCacheable 声明哈希缓存接口
type HashCacheable[T any, F any] interface {
	GetKey(params T) string
	FetchDataSource(ctx context.Context, params T) (F, error)
	GetExpire(params T, result F) time.Duration
	Format(value map[string]string) (F, error)
}

// GenericHashCache 实现哈希缓存的抽象类
type GenericHashCache[T any, F any] struct {
	rds redis.UniversalClient
	HashCacheable[T, F]
}

// NewGenericHashCache 创建哈希缓存的实例
func NewGenericHashCache[T any, F any](rds redis.UniversalClient, imp HashCacheable[T, F]) *GenericHashCache[T, F] {
	return &GenericHashCache[T, F]{rds: rds, HashCacheable: imp}
}

// Get 从缓存中获取数据，如果没有则从数据源中获取
func (g *GenericHashCache[T, F]) Get(ctx context.Context, params T) (value F, err error) {
	key := g.getFullKey(params)
	result, err := g.rds.HGetAll(ctx, key).Result()
	if err != nil && err != redis.Nil {
		return value, err
	}

	if len(result) > 0 {
		if result["hash_empty_flag"] == "1" {
			return value, nil
		}
		return g.Format(result)
	}

	value, err = g.Refresh(ctx, params)
	return value, err
}

// Refresh 刷新缓存
func (g *GenericHashCache[T, F]) Refresh(ctx context.Context, params T) (F, error) {
	key := g.GetKey(params)
	value, err, _ := sf.Do(key, func() (interface{}, error) {
		return g.FetchDataSource(ctx, params)
	})
	sf.Forget(key)
	result, ok := value.(F)
	if !ok {
		return *new(F), ErrCacheGetInvalidType
	}
	if err != nil {
		return result, err
	}
	err = g.Set(ctx, params, result)
	return result, err
}

// Del 删除缓存
func (g *GenericHashCache[T, F]) Del(ctx context.Context, params T) error {
	key := g.getFullKey(params)
	err := g.rds.Del(ctx, key).Err()
	if err != nil {
		return err
	}
	return nil
}

// Set 设置缓存
func (g *GenericHashCache[T, F]) Set(ctx context.Context, params T, content F) error {
	return g.writeToCache(ctx, params, content)
}

// writeToCache 将数据写入缓存
func (g *GenericHashCache[T, F]) writeToCache(ctx context.Context, params T, content F) error {
	key := g.getFullKey(params)

	// 检查content是否为空
	isNilOrEmpty := false
	if reflect.ValueOf(content).IsZero() {
		isNilOrEmpty = true
	}

	var hashMap map[string]interface{}
	if isNilOrEmpty {
		// 用 hash_empty_flag 标记是否为空，避免缓存穿透
		hashMap = map[string]interface{}{
			"hash_empty_flag": "1",
		}
	} else {
		// 将content转换为map
		bytes, err := sonic.Marshal(content)
		if err != nil {
			return err
		}

		tempMap := make(map[string]interface{})
		err = sonic.Unmarshal(bytes, &tempMap)
		if err != nil {
			return err
		}

		// 添加非空标记
		tempMap["hash_empty_flag"] = "0"
		hashMap = tempMap
	}

	// 设置哈希值
	err := g.rds.HSet(ctx, key, hashMap).Err()
	if err != nil {
		return err
	}

	// 设置过期时间
	expire := g.GetExpire(params, content)
	if expire > 0 {
		err = g.rds.Expire(ctx, key, expire).Err()
		if err != nil {
			return err
		}
	}

	return nil
}

// Update 更新哈希中的字段
func (g *GenericHashCache[T, F]) Update(ctx context.Context, params T, props map[string]interface{}) (bool, error) {
	key := g.getFullKey(params)

	// 将map转换为字符串数组
	args := make([]interface{}, 0, len(props)*2)
	for k, v := range props {
		args = append(args, k, v)
	}

	// 执行Lua脚本
	result, err := g.rds.Eval(ctx, updateLuaScript, []string{key}, args...).Int64()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// Incr 增加哈希中字段的值
func (g *GenericHashCache[T, F]) Incr(ctx context.Context, params T, props map[string]interface{}) (bool, error) {
	// 添加检查 g.rds 是否为 nil
	if g.rds == nil {
		return false, ErrRedisClientNotInitialized // 返回自定义错误
	}

	key := g.getFullKey(params)

	// 将map转换为字符串数组
	args := make([]interface{}, 0, len(props)*2)
	for k, v := range props {
		args = append(args, k, v)
	}
	// 执行Lua脚本
	result, err := incrLuaScript.Run(ctx, g.rds, []string{key}, args...).Int64()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// getFullKey 获取完整的键名
func (g *GenericHashCache[T, F]) getFullKey(params T) string {
	key := g.GetKey(params)
	return config.C.Redis.Prefix + key
}
