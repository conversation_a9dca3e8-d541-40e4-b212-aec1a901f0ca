package cache

import (
	"context"
	"fmt"
	"time"

	"app/conn"
	"app/dao/model"
	"app/dao/repo"
)

type CommentHotCommentCache struct{}

func (c *CommentHotCommentCache) GetKey(originId int64) string {
	return fmt.Sprintf("CommentHotComment:%d", originId)
}

func (c *CommentHotCommentCache) GetExpire(params int64, result []model.L36ActionWorkComment) time.Duration {
	return time.Minute * 10
}

func (c *CommentHotCommentCache) GetWithoutRefresh(ctx context.Context, originId int64) (result []model.L36ActionWorkComment, err error) {
	records, err := repo.NewCommentRepo().GetHotCommentListByOriginId(ctx, originId)
	return records, err
}

func NewCommentHotCommentCache() *RedisCache[int64, []model.L36ActionWorkComment] {
	return NewRedisCache(conn.GetRedisConn(), &CommentHotCommentCache{})
}
