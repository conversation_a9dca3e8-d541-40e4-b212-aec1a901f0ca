package cache

import (
	"context"
	"fmt"
	"time"

	"app/conn"
)

type RecommendLimitCache struct{}

func (c *RecommendLimitCache) GetKey(params any) string {
	return fmt.Sprintf("recommend_limit:%d", time.Now().Unix())
}

func (c *RecommendLimitCache) GetExpire(params any, result int64) time.Duration {
	return time.Second * 10
}

func (c *RecommendLimitCache) GetWithoutRefresh(ctx context.Context, params any) (result int64, err error) {
	return 0, nil
}

func NewRecommendLimitCache() *TempCounterCache[any, int64] {
	return NewTempCounterCache(conn.GetRedisConn(), &RecommendLimitCache{})
}
