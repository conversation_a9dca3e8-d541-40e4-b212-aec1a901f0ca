package cache

import (
	"context"
	"fmt"
	"time"

	"app/conn"
	"app/dao/model"
	"app/dao/repo"
	"app/schema"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/samber/lo"
)

type UserHotWorkCache struct{}

func (c *UserHotWorkCache) GetKey(userId int64) string {
	return fmt.Sprintf("UserHotWork:%d", userId)
}

func (c *UserHotWorkCache) GetExpire(userId int64, result []model.L36ActionWork) time.Duration {
	return time.Minute * 10
}

func (c *UserHotWorkCache) GetWithoutRefresh(ctx context.Context, userId int64) (result []model.L36ActionWork, err error) {
	records, err := repo.NewWorkRepo().FindUserTopWorks(ctx, userId, 3)
	return records, err
}

func NewUserHotWorkCache() *RedisCache[int64, []model.L36ActionWork] {
	return NewRedisCache(conn.GetRedisConn(), &UserHotWorkCache{})
}

type DesignerExpRankCache struct{}

func (c *DesignerExpRankCache) GetKey(params int) string {
	return "DesignerExpRank"
}

func (c *DesignerExpRankCache) GetExpire(params int, result []schema.RankDesignerItem) time.Duration {
	return time.Hour
}

func (c *DesignerExpRankCache) GetWithoutRefresh(ctx context.Context, params int) (result []schema.RankDesignerItem, err error) {
	users, err := repo.NewUserRepo().FindTopExpUsers(ctx, 100)
	if err != nil {
		return result, err
	}
	roleIds := lo.Map(users, func(user model.L36ActionUser, i int) int64 {
		return user.RoleID
	})
	roles, err := repo.NewRoleRepo().FindByRoleIds(ctx, roleIds)
	if err != nil {
		return result, err
	}
	roleMap := lo.KeyBy(roles, func(role model.L36RoleInfo) int64 {
		return role.RoleID
	})
	result = lo.Map(users, func(user model.L36ActionUser, i int) schema.RankDesignerItem {
		role, _ := roleMap[user.RoleID]
		return schema.RankDesignerItem{
			RoleId:   user.RoleID,
			RoleName: role.RoleName,
			Job:      int(role.Job),
			Gender:   int(role.Gender),
			Level:    int(role.Level),
			Rank:     i + 1,
			Exp:      int64(user.Exp),
		}
	})
	return result, nil
}

func NewDesignerExpRankCache() *RedisCache[int, []schema.RankDesignerItem] {
	return NewRedisCache(conn.GetRedisConn(), &DesignerExpRankCache{})
}

type DesignerRoleCache struct{}

func (c *DesignerRoleCache) GetKey(userId int64) string {
	return fmt.Sprintf("DesignerRole:%d", userId)
}

func (c *DesignerRoleCache) GetExpire(userId int64, result schema.DesignerInfo) time.Duration {
	return time.Minute * 10
}

func (c *DesignerRoleCache) GetWithoutRefresh(ctx context.Context, userId int64) (result schema.DesignerInfo, err error) {
	user, err := repo.NewUserRepo().FindById(ctx, userId)
	if err != nil {
		return result, err
	}
	roleDict, err := repo.NewRoleRepo().FindRelatedRoleMap(ctx, []model.L36ActionUser{*user})
	if err != nil {
		return result, err
	}
	avatarRole := roleDict[user.AvatarRoleID]
	nameRole := roleDict[user.NameRoleID]
	return schema.DesignerInfo{
		ID:     user.ID,
		RoleId: user.RoleID,
		Avatar: avatarRole.GetAvatarInfo(),
		Name:   nameRole.GetRoleName(user),
	}, nil
}

func NewDesignerRoleCache() *RedisCache[int64, schema.DesignerInfo] {
	instance := NewRedisCache(conn.GetRedisConn(), &DesignerRoleCache{})
	instance.CustomGetWithoutRefresh = func(ctx context.Context, params []int64) (map[int64]schema.DesignerInfo, error) {
		users, usersErr := repo.NewUserRepo().FindByIds(ctx, params)
		if usersErr != nil {
			return nil, usersErr
		}
		roleDict, err := repo.NewRoleRepo().FindRelatedRoleMap(ctx, users)
		if err != nil {
			elog.WithContext(ctx).WithField("err", err.Error()).Error("find roles error")
			return nil, err
		}

		result := make(map[int64]schema.DesignerInfo)
		for _, user := range users {
			avatarRole := roleDict[user.AvatarRoleID]
			nameRole := roleDict[user.NameRoleID]
			result[user.ID] = schema.DesignerInfo{
				ID:     user.ID,
				RoleId: user.RoleID,
				Avatar: avatarRole.GetAvatarInfo(),
				Name:   nameRole.GetRoleName(&user),
			}
		}
		return result, nil
	}
	return instance
}
