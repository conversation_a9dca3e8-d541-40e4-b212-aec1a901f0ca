package cache

import (
	"context"
	"testing"

	"app/config"
	"app/conn"
	"app/util"
)

func init() {
	conf, _ := config.LoadConfig()
	conn.ConnectDB1(&conf.Mysql)
	conn.ConnectRedis1(&conf.Redis)
}

func TestDesignerRoleCache_Get(t *testing.T) {
	cache := NewDesignerRoleCache()
	results, err := cache.Get(context.Background(), 1)
	if err != nil {
		t.<PERSON>("Get designer role error: %v", err)
	} else {
		t.Logf("Get designer role results: %v", util.JsonStringify(results))
	}
}

func TestDesignerRoleCache_Gets(t *testing.T) {
	cache := NewDesignerRoleCache()
	results, err := cache.Gets(context.Background(), []int64{1, 2, 4, 5, 6})
	if err != nil {
		t.<PERSON><PERSON>("Get designer role error: %v", err)
	} else {
		t.Logf("Get designer role results: %v", util.JsonStringify(results))
	}
}
