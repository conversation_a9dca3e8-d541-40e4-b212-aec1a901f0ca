package cache

import (
	"context"
	"fmt"
	"time"

	"app/conn"
	"app/util"
)

type PaddingDailyMaxCount struct{}

type PaddingDailyMaxCountParams struct {
	Type            string
	MinGteThreshold int32
	MaxGteThreshold int32
}

func (p *PaddingDailyMaxCount) GetKey(params PaddingDailyMaxCountParams) string {
	return fmt.Sprintf("padding_daily_max_count:%s:%s:%d:%d", time.Now().Format("20060102"), params.Type, params.MinGteThreshold, params.MaxGteThreshold)
}

func (p *PaddingDailyMaxCount) GetExpire(params PaddingDailyMaxCountParams, result int32) time.Duration {
	return time.Hour * 25
}

func (p *PaddingDailyMaxCount) GetWithoutRefresh(ctx context.Context, params PaddingDailyMaxCountParams) (int32, error) {
	if params.MaxGteThreshold == 0 || params.MinGteThreshold > params.MaxGteThreshold {
		return 0, nil
	}
	if params.MinGteThreshold == params.MaxGteThreshold {
		return params.MinGteThreshold * 10000, nil
	}
	return (int32(util.GetRandomIntN(int(params.MaxGteThreshold-params.MinGteThreshold))) + params.MinGteThreshold) * 10000, nil
}

func NewPaddingDailyMaxCount() *RedisCache[PaddingDailyMaxCountParams, int32] {
	return NewRedisCache(conn.GetRedisConn(), &PaddingDailyMaxCount{})
}
