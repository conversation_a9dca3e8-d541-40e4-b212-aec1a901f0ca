package es

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"app/config"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	es "github.com/elastic/go-elasticsearch/v8"
)

var (
	client   *es.TypedClient
	once     = sync.Once{}
	esLogger = &CustomEsLogger{}
)

type CustomEsLogger struct{}

// LogRoundTrip should not modify the request or response, except for consuming and closing the body.
// Implementations have to check for nil values in request and response.
func (c *CustomEsLogger) LogRoundTrip(req *http.Request, res *http.Response, err error, t time.Time, d time.Duration) error {
	if err != nil {
		return err
	}
	if !config.C.Test.TestEnv {
		return nil
	}
	if req == nil || req.Body == nil {
		return nil
	}
	bytes, err := io.ReadAll(req.Body)
	if err != nil {
		fmt.Println(err)
		return err
	}
	reqBody := string(bytes)
	elog.WithFields(elog.Fields{
		"request":  reqBody,
		"duration": d.Milliseconds(),
	}).Info("es request")
	return nil
}

// RequestBodyEnabled makes the client pass a copy of request body to the logger.
func (c *CustomEsLogger) RequestBodyEnabled() bool {
	return config.C.Test.TestEnv
}

// ResponseBodyEnabled makes the client pass a copy of response body to the logger.
func (c *CustomEsLogger) ResponseBodyEnabled() bool {
	return config.C.Test.TestEnv
}

// GetClient 获取ES客户端
func GetEsClient(conf *config.EsCfg) (*es.TypedClient, error) {
	address := strings.Split(conf.Address, ",")
	var err error
	once.Do(func() {
		cfg := es.Config{
			Addresses: address,
			Username:  conf.User,
			Password:  conf.Password,
			Logger:    esLogger,
		}
		client, err = es.NewTypedClient(cfg)
	})
	return client, err
}
