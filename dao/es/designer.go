package es

import (
	"context"

	"app/config"
	"app/schema"

	"github.com/elastic/go-elasticsearch/v8/typedapi/types"
	"github.com/elastic/go-elasticsearch/v8/typedapi/types/enums/operator"
)

type Designer struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	Exp  int32  `json:"exp"`
}

type DesignerEsRepo struct {
	BaseEsRepo[Designer]
}

const designerIndexMapping = `{
	"settings": {
	  "analysis": {
		"analyzer": {
		  "ik_analyzer": {
			"type": "custom",
			"tokenizer": "ik_max_word"
		  }
		}
	  }
	},
	"mappings": {
	  "properties": {
		"id": {
		  "type": "integer"
		},
		"name": {
		  "type": "text",
		  "analyzer": "ik_analyzer"
		},
		"exp": {
			"type": "integer"
		}
	  }
	}
  }`

func NewDesignerEsRepo() *DesignerEsRepo {
	conf := config.C.Es
	index := "action_designer"
	if conf.Suffix != "" {
		index = index + "_" + conf.Suffix
	}
	return &DesignerEsRepo{
		BaseEsRepo: *NewBaseEsRepo[Designer](index, designerIndexMapping, &conf),
	}
}

func (r *DesignerEsRepo) SearchListDesigner(ctx context.Context, params *schema.DesignerListSearchReq) ([]int64, error) {
	query := types.NewBoolQuery()
	query.Must = append(query.Must, types.Query{
		Match: map[string]types.MatchQuery{
			"name": {Query: params.Keyword, Operator: &operator.And},
		},
	})
	docs, _, err := r.Search(ctx, &types.Query{
		Bool: query,
	}, params.Pagination, "exp:desc")
	if err != nil {
		return nil, err
	}
	var ids []int64
	for _, doc := range docs {
		ids = append(ids, doc.ID)
	}
	return ids, nil
}
