package es

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"app/config"
	"app/schema"
	"app/util"

	"github.com/bytedance/sonic"
	es "github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/typedapi/core/search"
	"github.com/elastic/go-elasticsearch/v8/typedapi/types"
	"github.com/elastic/go-elasticsearch/v8/typedapi/types/enums/sortorder"
)

type BaseEsRepo[T any] struct {
	index   string
	mapping string
	client  *es.TypedClient
}

func NewBaseEsRepo[T any](index string, mapping string, conf *config.EsCfg) *BaseEsRepo[T] {
	client, err := GetEsClient(conf)
	if err != nil {
		panic(err)
	}
	return &BaseEsRepo[T]{
		index:   index,
		mapping: mapping,
		client:  client,
	}
}

func (r *BaseEsRepo[T]) CreateIndex(ctx context.Context) error {
	exists, err := r.client.Indices.Exists(r.index).Do(ctx)
	if err != nil {
		return err
	}
	if exists {
		fmt.Println("index: " + r.index + " already exists")
		return nil
	}
	_, err = r.client.Indices.Create(r.index).Raw(strings.NewReader(r.mapping)).Do(ctx)
	if err != nil {
		fmt.Println("create index: " + r.index + " failed")
		return err
	}
	fmt.Println("create index: " + r.index + " success")
	return nil
}

func (r *BaseEsRepo[T]) DeleteIndex(ctx context.Context) error {
	_, err := r.client.Indices.Delete(r.index).Do(ctx)
	if err != nil {
		fmt.Println("delete index: " + r.index + " failed")
		return err
	}
	fmt.Println("delete index: " + r.index + " success")
	return nil
}

func (r *BaseEsRepo[T]) Index(ctx context.Context, id int, doc T) error {
	_, err := r.client.Index(r.index).Document(doc).Id(strconv.Itoa(id)).Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (r *BaseEsRepo[T]) Delete(ctx context.Context, id int) error {
	_, err := r.client.Delete(r.index, strconv.Itoa(id)).Do(ctx)
	// 忽略404错误
	if err != nil && errors.As(err, &types.ElasticsearchError{}) {
		esErr := err.(*types.ElasticsearchError)
		if esErr.Status == 404 {
			return nil
		}
	}
	if err != nil {
		return fmt.Errorf("delete work from es failed: %w, id: %d", err, id)
	}
	return nil
}

func (r *BaseEsRepo[T]) Update(ctx context.Context, id int, update map[string]any) error {
	_, err := r.client.Update(r.index, strconv.Itoa(id)).Doc(update).Do(ctx)
	// 忽略404错误
	if err != nil && errors.As(err, &types.ElasticsearchError{}) {
		esErr := err.(*types.ElasticsearchError)
		if esErr.Status == 404 {
			return nil
		}
	}
	if err != nil {
		return fmt.Errorf("update work to es failed: %w, id: %d, update: %v", err, id, update)
	}
	return nil
}

func (r *BaseEsRepo[T]) Get(ctx context.Context, id int) (*T, error) {
	res, err := r.client.Get(r.index, strconv.Itoa(id)).Do(ctx)
	if err != nil {
		return nil, err
	}
	if !res.Found {
		return nil, nil
	}
	var doc T
	if err := sonic.Unmarshal(res.Source_, &doc); err != nil {
		return nil, err
	}
	return &doc, nil
}

func (r *BaseEsRepo[T]) Search(ctx context.Context, query *types.Query, page schema.Pagination, sort ...string) ([]T, int64, error) {
	from := (page.Page - 1) * page.PageSize
	sorts := []types.SortCombinations{}
	for _, s := range sort {
		sortOptions := map[string]types.FieldSort{}
		parts := strings.Split(s, ":")
		key := parts[0]
		order := sortorder.Asc
		if len(parts) > 1 && parts[1] == "desc" {
			order = sortorder.Desc
		}
		sortOptions[key] = types.FieldSort{
			Order: &order,
		}

		sorts = append(sorts, sortOptions)
	}
	json, err := sonic.Marshal(query)
	if err != nil {
		return nil, 0, err
	}
	res, err := r.client.Search().Index(r.index).Request(&search.Request{
		Query: query,
	}).From(from).Sort(sorts...).Size(page.PageSize).Do(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("search error: %s, query: %s", err, string(json))
	}
	var docs []T
	for _, hit := range res.Hits.Hits {
		var doc T
		if err := sonic.Unmarshal(hit.Source_, &doc); err != nil {
			return nil, 0, err
		}
		docs = append(docs, doc)
	}
	return docs, res.Hits.Total.Value, nil
}

func (c *BaseEsRepo[T]) BuildTermOrTerms(key string, value any) types.Query {
	ids := util.AssertToArray(value)
	if len(ids) == 0 {
		return types.Query{}
	}
	var query types.Query
	if len(ids) == 1 {
		query = types.Query{Term: map[string]types.TermQuery{
			key: {
				Value: ids[0],
			},
		}}
	} else {
		query = types.Query{Terms: &types.TermsQuery{
			TermsQuery: map[string]types.TermsQueryField{
				key: ids,
			},
		}}
	}
	return query
}
