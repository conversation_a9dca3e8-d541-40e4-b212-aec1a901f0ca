package es

import (
	"context"

	"app/config"
	"app/constant"
	"app/dao/model"
	"app/dao/repo"
	"app/schema"
	"app/util"

	"github.com/elastic/go-elasticsearch/v8/typedapi/types"
	"github.com/elastic/go-elasticsearch/v8/typedapi/types/enums/operator"
)

type Work struct {
	ID           int64   `json:"id"`
	Type         int32   `json:"type"`
	UserID       int64   `json:"user_id"`
	TopicIds     []int64 `json:"topic_ids"`
	Name         string  `json:"name"`
	Summary      string  `json:"summary"`
	Hot          int32   `json:"hot"`
	Contents     []int   `json:"contents"`
	Gender       int     `json:"gender"`
	Visibility   int8    `json:"visibility"`
	LikeCount    int32   `json:"like_count"`
	CollectCount int32   `json:"collect_count"`
	CommentCount int32   `json:"comment_count"`
	AuditStatus  int8    `json:"audit_status"`
}

type WorkEsRepo struct {
	BaseEsRepo[Work]
}

const workIndexMapping = `{
	"settings": {
	  "analysis": {
		"analyzer": {
		  "ik_analyzer": {
			"type": "custom",
			"tokenizer": "ik_max_word"
		  }
		}
	  }
	},
	"mappings": {
	  "properties": {
		"id": {
		  "type": "integer"
		},
		"type": {
		  "type": "keyword"
		},
		"user_id": {
			"type": "integer"
		},
		"gender": {
			"type": "integer"
		},
		"topic_ids": {
		  "type": "keyword"
		},
		"name": {
			"type": "text",
			"analyzer": "ik_analyzer"
		},
		"summary": {
		  "type": "text",
		  "analyzer": "ik_analyzer"
		},
		"contents": {
		  "type": "keyword"
		},
		"hot": {
		  "type": "integer"
		},
		"visibility": {
		  "type": "integer"
		},
		"like_count": {
		  "type": "integer"
		},
		"collect_count": {
		  "type": "integer"
		},
		"comment_count": {
		  "type": "integer"
		},
		"audit_status": {
			"type": "integer"
		}
	  }
	}
  }`

func NewWorkEsRepo() *WorkEsRepo {
	conf := config.C.Es
	index := "action_work"
	if conf.Suffix != "" {
		index = index + "_" + conf.Suffix
	}
	return &WorkEsRepo{
		BaseEsRepo: *NewBaseEsRepo[Work](index, workIndexMapping, &conf),
	}
}

func (r *WorkEsRepo) SearchListWork(ctx context.Context, params *schema.WorkListSearchReq) ([]int64, error) {
	query := types.NewBoolQuery()

	// Add visibility check first
	query.Must = append(query.Must, r.BuildTermOrTerms("visibility", constant.VisibilityPublic))

	// Add audit status check to ensure only normal status works are returned
	query.Must = append(query.Must, r.BuildTermOrTerms("audit_status", constant.FpReviewStatusPass))

	if params.Gender != 0 {
		query.Must = append(query.Must, r.BuildTermOrTerms("gender", params.Gender-1))
	}

	if params.Keyword != "" {
		subQuery := []types.Query{
			{Match: map[string]types.MatchQuery{"name": {Query: params.Keyword, Operator: &operator.And}}},
			{Match: map[string]types.MatchQuery{"summary": {Query: params.Keyword, Operator: &operator.And}}},
		}
		query.Must = append(query.Must, types.Query{Bool: &types.BoolQuery{Should: subQuery}})
	}

	if params.Topic != "" {
		topic, err := repo.NewTopicRepo().FindByName(ctx, params.Topic)
		if err != nil {
			return nil, err
		}
		if topic == nil {
			return []int64{}, nil
		}
		query.Must = append(query.Must, r.BuildTermOrTerms("topic_ids", topic.ID))
	}

	if params.Contents != "" {
		content := util.CsvStringToSlice(params.Contents)
		query.Must = append(query.Must, r.BuildTermOrTerms("contents", content))
	}

	sort := r.GetWorkSort(params.Sort, params.Order)
	docs, _, err := r.Search(ctx, &types.Query{
		Bool: query,
	}, params.Pagination, sort)
	if err != nil {
		return nil, err
	}

	ids := make([]int64, len(docs))
	for i, doc := range docs {
		ids[i] = doc.ID
	}

	return ids, nil
}

func (r *WorkEsRepo) GetWorkSort(sort string, order string) string {
	if sort == "" {
		sort = "new"
	}
	if order == "" {
		order = "desc"
	}

	sortKey := sort
	switch sort {
	case "new":
		sortKey = "id"
	case "like":
		sortKey = "like_count"
	case "collect":
		sortKey = "collect_count"
	case "comment":
		sortKey = "comment_count"
	}

	return sortKey + ":" + order
}

func (r *WorkEsRepo) FormatEsWork(work *model.L36ActionWork, topicIds []int64) Work {
	return Work{
		ID:           work.ID,
		Type:         work.Type,
		UserID:       work.UserID,
		TopicIds:     topicIds,
		Name:         work.Name,
		Summary:      work.Summary,
		Hot:          work.Hot,
		Contents:     work.DecodeContents(),
		Gender:       int(work.Gender),
		Visibility:   int8(work.Visibility),
		LikeCount:    work.LikeCount + work.FakeLikeCount,
		CollectCount: work.CollectCount + work.FakeCollectCount,
		CommentCount: work.CommentCount + work.FakeCommentCount,
		AuditStatus:  int8(work.AuditStatus),
	}
}

func (r *WorkEsRepo) IndexWork(ctx context.Context, work *model.L36ActionWork, topicIds []int64) error {
	return r.Index(ctx, int(work.ID), r.FormatEsWork(work, topicIds))
}
