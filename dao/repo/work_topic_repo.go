package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

// WorkTopicRepo 作品话题关联仓库
type WorkTopicRepo struct {
	BaseRepo[model.L36ActionWorkTopic]
}

// NewWorkTopicRepo 创建作品话题关联仓库实例
func NewWorkTopicRepo() *WorkTopicRepo {
	return &WorkTopicRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkTopic]{
			DB: conn.GetDB(),
		},
	}
}

// UpdateWorkTopics updates the list of topics associated with a work.
// It removes all existing topic associations and creates new ones based on the provided topic IDs.
// If workId is invalid (<=0) or newTopicIds is empty, it will perform the appropriate operations.
// ctx 上下文
// workId 作品ID
// newTopicIds 新的话题ID列表
// 返回 error 操作错误
func (r *WorkTopicRepo) UpdateWorkTopics(ctx context.Context, workId int64, newTopicIds []int64) error {
	if workId <= 0 {
		return nil
	}

	// 删除原有的作品话题
	if err := r.Scope(ctx).Where("work_id = ?", workId).Delete(model.L36ActionWorkTopic{}).Error; err != nil {
		return err
	}

	if len(newTopicIds) == 0 {
		return nil
	}

	// 新增作品话题
	return r.CreateBatch(ctx, lo.Map(newTopicIds, func(topicId int64, i int) model.L36ActionWorkTopic {
		return model.L36ActionWorkTopic{
			WorkID:  workId,
			TopicID: topicId,
		}
	}))
}

// FindWorkTopics retrieves the topic IDs associated with the given work IDs.
// It returns a map where keys are work IDs and values are slices of associated topic IDs.
// If workIds is empty, it returns an empty map.
//
// ctx 上下文
// workIds 作品ID列表
// 返回 map[int64][]int64 作品话题映射，key为作品ID，value为话题ID列表
// 返回 error 查询错误
func (r *WorkTopicRepo) FindWorkTopics(ctx context.Context, workIds []int64) (map[int64][]int64, error) {
	workTopicMap := make(map[int64][]int64)
	if len(workIds) == 0 {
		return workTopicMap, nil
	}

	var workTopics []model.L36ActionWorkTopic
	err := r.Scope(ctx).Where("work_id in (?)", workIds).Find(&workTopics).Error
	if err == gorm.ErrRecordNotFound {
		return workTopicMap, nil
	}
	if err != nil {
		return workTopicMap, err
	}

	for _, workTopic := range workTopics {
		if workTopicMap[workTopic.WorkID] == nil {
			workTopicMap[workTopic.WorkID] = make([]int64, 0)
		}
		workTopicMap[workTopic.WorkID] = append(workTopicMap[workTopic.WorkID], workTopic.TopicID)
	}
	return workTopicMap, nil
}

// FindByWorkId 根据作品ID查询作品话题
// ctx 上下文
// workId 作品ID
// 返回 作品话题列表
// 返回 error 查询错误
func (r *WorkTopicRepo) FindByWorkId(ctx context.Context, workId int64) ([]int64, error) {
	if workId <= 0 {
		return nil, nil
	}
	var workTopics []model.L36ActionWorkTopic
	err := r.Scope(ctx).Where("work_id = ?", workId).Find(&workTopics).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find work %d topic failed: %w", workId, err)
	}
	return lo.Map(workTopics, func(workTopic model.L36ActionWorkTopic, i int) int64 {
		return workTopic.TopicID
	}), nil
}
