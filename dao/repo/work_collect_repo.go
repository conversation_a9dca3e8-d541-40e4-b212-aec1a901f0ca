package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type WorkCollectRepo struct {
	BaseRepo[model.L36ActionWorkCollect]
}

func NewWorkCollectRepo() *WorkCollectRepo {
	return &WorkCollectRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkCollect]{
			DB: conn.GetDB(),
		},
	}
}

// GetCollectMap 获取用户对多个作品的收藏状态
//
// ctx 上下文
// userId 用户ID
// workIds 作品ID列表
// 返回map[int64]bool 作品收藏状态映射
// 返回error 查询错误信息
func (r *WorkCollectRepo) GetCollectMap(ctx context.Context, userId int64, workIds []int64) (map[int64]bool, error) {
	if userId <= 0 || len(workIds) == 0 {
		return make(map[int64]bool), nil
	}

	collectMap := make(map[int64]bool)
	var collects []model.L36ActionWorkCollect
	err := r.NormalScope(ctx).Where("user_id = ? and work_id in (?)", userId, workIds).Find(&collects).Error
	if err == gorm.ErrRecordNotFound {
		return collectMap, nil
	}
	if err != nil {
		return nil, err
	}

	for _, collect := range collects {
		collectMap[collect.WorkID] = true
	}
	return collectMap, nil
}

// FindByUserWork 查询用户对单个作品的收藏记录
//
// ctx 上下文
// userId 用户ID
// workId 作品ID
// 返回*model.L36ActionWorkCollect 收藏记录，未找到返回nil
// 返回error 查询错误信息
func (r *WorkCollectRepo) FindByUserWork(ctx context.Context, userId int64, workId int64) (*model.L36ActionWorkCollect, error) {
	if userId <= 0 || workId <= 0 {
		return nil, nil
	}

	var collect model.L36ActionWorkCollect
	err := r.Scope(ctx).Where("user_id = ? and work_id = ?", userId, workId).Take(&collect).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &collect, err
}

// Collect 收藏作品
//
// ctx 上下文
// userId 用户ID
// roleId 角色ID
// workId 作品ID
// 返回error 操作错误信息
func (r *WorkCollectRepo) Collect(ctx context.Context, userId int64, roleId int64, workId int64) (int64, error) {
	if userId <= 0 || roleId <= 0 || workId <= 0 {
		return 0, nil
	}

	collect := model.L36ActionWorkCollect{
		UserID:   userId,
		WorkID:   workId,
		RoleID:   roleId,
		IsDelete: 0,
	}
	err := r.Scope(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{
			"is_delete": 0,
		}),
	}).Create(&collect).Error
	if err != nil {
		return 0, fmt.Errorf("collect work fail: %w, userId: %d, roleId: %d, workId: %d", err, userId, roleId, workId)
	}
	return collect.ID, nil
}

// CountByTimeRange 统计时间范围内各作品的收藏数
//
// ctx 上下文
// startTime 开始时间戳
// endTime 结束时间戳
// 返回[]schema.WorkCounter 作品收藏计数列表
// 返回error 查询错误信息
func (r *WorkCollectRepo) CountByTimeRange(ctx context.Context, startTime int64, endTime int64) ([]schema.WorkCounter, error) {
	if startTime >= endTime {
		return []schema.WorkCounter{}, nil
	}

	var works []schema.WorkCounter
	err := r.Scope(ctx).
		Where("ctime >= ? and ctime < ?", startTime, endTime).
		Group("work_id").
		Select("work_id, count(id) as count").
		Find(&works).Error

	if err == gorm.ErrRecordNotFound {
		return works, nil
	}
	return works, err
}

// DeleteByWorkId 根据作品ID批量删除收藏记录
//
// ctx 上下文
// workId 作品ID
// limit 每次删除的数量限制
// 返回 int 实际删除的数量
// 返回 error 操作错误信息
func (r *WorkCollectRepo) DeleteByWorkId(ctx context.Context, workId int64, limit int) (int, error) {
	if workId <= 0 || limit <= 0 {
		return 0, nil
	}

	result := r.Scope(ctx).Where("work_id = ?", workId).Limit(limit).Delete(&model.L36ActionWorkCollect{})
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

// CancelCollectByWorkId 根据作品ID批量更新收藏记录状态
//
// ctx 上下文
// workId 作品ID
// limit 每次更新的数量限制
// 返回 int 实际更新的数量
// 返回 error 操作错误信息
func (r *WorkCollectRepo) FindCollectsByWorkId(ctx context.Context, workId int64, limit int) ([]model.L36ActionWorkCollect, error) {
	if workId <= 0 || limit <= 0 {
		return []model.L36ActionWorkCollect{}, nil
	}
	var collects []model.L36ActionWorkCollect
	err := r.NormalScope(ctx).Where("work_id = ?", workId).Limit(limit).Find(&collects).Error
	if err == gorm.ErrRecordNotFound {
		return collects, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find collects by work id error: %w, work id: %d, limit: %d", err, workId, limit)
	}
	return collects, nil
}
