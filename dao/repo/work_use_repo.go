package repo

import (
	"context"
	"time"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
)

type WorkUseRepo struct {
	BaseRepo[model.L36ActionWorkUse]
}

func NewWorkUseRepo() *WorkUseRepo {
	return &WorkUseRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkUse]{
			DB: conn.GetDB(),
		},
	}
}

// FindOneByWorkUser 查询用户对作品的使用记录。
//
// ctx 上下文
// workId 作品ID
// userId 用户ID
// 返回 *model.L36ActionWorkUse 使用记录，未找到返回nil
// 返回 error 查询错误
func (r *WorkUseRepo) FindOneByWorkUser(ctx context.Context, workId int64, userId int64) (*model.L36ActionWorkUse, error) {
	if workId <= 0 || userId <= 0 {
		return nil, nil
	}

	var workUse model.L36ActionWorkUse
	err := r.Scope(ctx).Where("work_id = ? and user_id = ?", workId, userId).Take(&workUse).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &workUse, err
}

// CountByWorkUserDate 统计用户当天对作品的使用次数。
//
// ctx 上下文
// workId 作品ID
// userId 用户ID
// now 当前时间戳（毫秒）
// 返回 int64 使用次数
// 返回 error 查询错误
func (r *WorkUseRepo) CountByWorkUserDate(ctx context.Context, workId int64, userId int64, now int64) (int64, error) {
	if workId <= 0 || userId <= 0 || now <= 0 {
		return 0, nil
	}

	var count int64
	t := time.UnixMilli(now)
	start := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).UnixMilli()
	err := r.Scope(ctx).
		Where("work_id = ? and user_id = ? and ctime >= ? and ctime < ?", workId, userId, start, now).
		Count(&count).Error
	return count, err
}

// CountByTimeRange 统计指定时间范围内各作品的使用数量。
//
// ctx 上下文
// startTime 开始时间戳（毫秒）
// endTime 结束时间戳（毫秒）
// 返回 []schema.WorkCounter 作品使用统计列表
// 返回 error 查询错误
func (r *WorkUseRepo) CountByTimeRange(ctx context.Context, startTime int64, endTime int64) ([]schema.WorkCounter, error) {
	if startTime >= endTime {
		return []schema.WorkCounter{}, nil
	}

	var works []schema.WorkCounter
	err := r.Scope(ctx).
		Where("ctime >= ? and ctime < ?", startTime, endTime).
		Group("work_id").
		Select("work_id, count(id) as count").
		Find(&works).Error

	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}
