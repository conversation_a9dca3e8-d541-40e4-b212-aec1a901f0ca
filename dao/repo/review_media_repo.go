package repo

import (
	"context"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ReviewMediaRepo struct {
	BaseRepo[model.L36ActionReviewMedium]
}

func NewReviewMediaRepo() *ReviewMediaRepo {
	return &ReviewMediaRepo{
		BaseRepo: BaseRepo[model.L36ActionReviewMedium]{
			DB: conn.GetDB(),
		},
	}
}

// FindByFileIds 根据文件ID列表批量查询媒体信息
//
// ctx 上下文
// fileIds 文件ID列表
// 返回媒体信息列表
// 返回查询错误信息
func (r *ReviewMediaRepo) FindByFileIds(ctx context.Context, fileIds []string) ([]model.L36ActionReviewMedium, error) {
	if len(fileIds) == 0 {
		return []model.L36ActionReviewMedium{}, nil
	}

	var res []model.L36ActionReviewMedium
	err := r.<PERSON><PERSON>(ctx).Where("file_id in (?)", fileIds).Find(&res).Error
	if err == gorm.ErrRecordNotFound {
		return res, nil
	}
	return res, err
}

func (r *ReviewMediaRepo) SaveBatchAuditStatus(ctx context.Context, records []model.L36ActionReviewMedium) error {
	if len(records) == 0 {
		return nil
	}
	return r.DB.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "file_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"audit_status"}),
		}).CreateInBatches(records, 100).Error
}

func (r *ReviewMediaRepo) DeleteByFileIds(ctx context.Context, fileIds []string) error {
	if len(fileIds) == 0 {
		return nil
	}
	return r.DB.WithContext(ctx).Where("file_id IN ?", fileIds).Delete(&model.L36ActionReviewMedium{}).Error
}
