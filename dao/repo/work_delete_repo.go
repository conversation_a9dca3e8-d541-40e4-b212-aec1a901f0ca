package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
)

type WorkDeleteRepo struct {
	BaseRepo[model.L36ActionWorkDelete]
}

func NewWorkDeleteRepo() *WorkDeleteRepo {
	return &WorkDeleteRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkDelete]{
			DB: conn.GetDB(),
		},
	}
}

func (r WorkDeleteRepo) FindBatch(ctx context.Context, limit int) ([]model.L36ActionWorkDelete, error) {
	var list []model.L36ActionWorkDelete
	err := r.DB.WithContext(ctx).Limit(limit).Find(&list).Error
	if err == gorm.ErrRecordNotFound {
		return list, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find work delete batch error: %w, limit: %d", err, limit)
	}
	return list, nil
}

// DeleteByWorkIds 根据作品ID列表批量删除记录
//
// ctx 上下文
// workIds 作品ID列表，为空时直接返回nil
// 返回 error 操作错误信息，包括数据库操作失败等
func (r *WorkDeleteRepo) DeleteByWorkIds(ctx context.Context, workIds []int64) error {
	if len(workIds) == 0 {
		return nil
	}

	err := r.DB.WithContext(ctx).Where("work_id in (?)", workIds).Delete(&model.L36ActionWorkDelete{}).Error
	if err != nil {
		return fmt.Errorf("delete work deletes error: %w", err)
	}
	return nil
}
