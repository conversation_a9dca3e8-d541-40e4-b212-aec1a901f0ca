package repo

import (
	"app/conn"
	"app/dao/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type FashionUserRepo struct {
	BaseRepo[model.L36FashionUser]
}

func NewFashionUserRepo() *FashionUserRepo {
	return &FashionUserRepo{
		BaseRepo: BaseRepo[model.L36FashionUser]{
			DB: conn.GetDB(),
		},
	}
}

// FindOneByAid 根据aid查询时尚用户信息
//
// ctx 上下文
// aid 用户aid
// 返回用户信息，未找到返回nil
// 返回查询错误信息
func (r *FashionUserRepo) FindOneByAid(ctx *gin.Context, aid string) (*model.L36FashionUser, error) {
	if aid == "" {
		return nil, nil
	}

	var fashionUser model.L36FashionUser
	err := r.Scope(ctx).Where("aid = ?", aid).First(&fashionUser).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &fashionUser, err
}
