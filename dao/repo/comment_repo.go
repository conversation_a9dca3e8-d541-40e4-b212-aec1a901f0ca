package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
)

const (
	NoOriginComment     = -1 // 原始评论ID（非回复评论）
	MaxHotReplyComments = 2  // 热门回复评论最大数量
	IsAddHot            = 1  // 是否加入热门
)

type CommentRepo struct {
	BaseRepo[model.L36ActionWorkComment]
}

func NewCommentRepo() *CommentRepo {
	return &CommentRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkComment]{
			DB: conn.GetDB(),
		},
	}
}

// FindOneByUserWork 查找用户对指定作品的最新评论
// params:
//   - ctx: 上下文
//   - userId: 用户ID
//   - workId: 作品ID
//
// returns: 评论信息，如果不存在返回nil
func (r *CommentRepo) FindOneByUserWork(ctx context.Context, userId int64, workId int64) (*model.L36ActionWorkComment, error) {
	var comment model.L36ActionWorkComment
	err := r.DB.WithContext(ctx).Where("user_id = ? AND work_id = ?", userId, workId).Last(&comment).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find one by user work fail: %w, userId: %d, workId: %d", err, userId, workId)
	}
	return &comment, nil
}

// ListWorkComments 获取作品评论列表
// params:
//   - ctx: 上下文
//   - params: 查询参数，包含作品ID、排序方式、分页信息等
func (r *CommentRepo) ListWorkComments(ctx context.Context, params *schema.CommentListReq) ([]model.L36ActionWorkComment, error) {
	query := r.NormalScope(ctx).Where("work_id = ?", params.WorkId).Where("origin_comment_id = -1")
	if params.Sort == "hot" {
		query = query.Order("like_count desc").Order("id desc")
	} else {
		query = query.Order("id desc")
	}
	if params.Sort == "mine" {
		query = query.Where("user_id = ?", params.UserId)
	}
	return r.QueryWithPagination(ctx, query, &params.Pagination)
}

// GetHotCommentListByOriginId 获取原始评论下的热门回复评论
// params:
//   - ctx: 上下文
//   - originId: 原始评论ID
//
// returns: 最多2条热门回复评论
func (r *CommentRepo) GetHotCommentListByOriginId(ctx context.Context, originId int64) (comments []model.L36ActionWorkComment, err error) {
	query := r.NormalScope(ctx).Where("origin_comment_id = ?", originId).
		Order("like_count desc").Order("id desc").Limit(2)
	err = query.Find(&comments).Error
	if err == gorm.ErrRecordNotFound {
		return comments, nil
	}
	if err != nil {
		return nil, fmt.Errorf("get hot comment list by origin id fail: %w, originId: %d", err, originId)
	}
	return comments, nil
}

// ListReplyComments 获取评论的回复列表
// params:
//   - ctx: 上下文
//   - params: 查询参数，包含评论ID和分页信息
func (r *CommentRepo) ListReplyComments(ctx context.Context, params *schema.CommentReplyListReq) ([]model.L36ActionWorkComment, error) {
	query := r.NormalScope(ctx).Where("origin_comment_id = ?", params.CommentId)
	query = query.Order("like_count desc")
	return r.QueryWithPagination(ctx, query, &params.Pagination)
}

// CountByTimeRange 统计指定时间范围内的热门评论数量
// params:
//   - ctx: 上下文
//   - startTime: 开始时间戳
//   - endTime: 结束时间戳
//
// returns: 每个作品的评论计数
func (r *CommentRepo) CountByTimeRange(ctx context.Context, startTime int64, endTime int64) ([]schema.WorkCounter, error) {
	var works []schema.WorkCounter
	err := r.Scope(ctx).Where("is_add_hot = 1 and ctime >= ? and ctime < ?", startTime, endTime).
		Group("work_id").
		Select("work_id, count(id) as count").
		Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		return works, nil
	}
	if err != nil {
		return nil, fmt.Errorf("count by time range fail: %w, startTime: %d, endTime: %d", err, startTime, endTime)
	}
	return works, nil
}

// FindByOriginCommentId 查找原始评论下的所有回复评论
// params:
//   - ctx: 上下文
//   - originCommentId: 原始评论ID
//
// returns: 回复评论列表，如果不存在返回空列表
func (r *CommentRepo) FindByOriginCommentId(ctx context.Context, originCommentId int64) ([]model.L36ActionWorkComment, error) {
	var comments []model.L36ActionWorkComment
	err := r.NormalScope(ctx).Where("origin_comment_id = ?", originCommentId).Find(&comments).Error
	if err == gorm.ErrRecordNotFound {
		return comments, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find by origin comment id fail: %w, originCommentId: %d", err, originCommentId)
	}
	return comments, nil
}

func (r *CommentRepo) FindByWorkId(ctx context.Context, workId int64, limit int) ([]model.L36ActionWorkComment, error) {
	var comments []model.L36ActionWorkComment
	err := r.NormalScope(ctx).Where("work_id = ?", workId).Order("id desc").Limit(limit).Find(&comments).Error
	if err == gorm.ErrRecordNotFound {
		return comments, nil
	}
	return comments, err
}
