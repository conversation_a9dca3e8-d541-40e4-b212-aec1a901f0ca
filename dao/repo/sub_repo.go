package repo

import (
	"context"
	"sync"

	"gorm.io/gorm"
)

// SubBaseRepo 分表仓库
type SubBaseRepo[T any] struct {
	rawTable  string
	shardFunc func(params T) string
	repoMap   map[string]*BaseRepo[T]
	initLock  sync.Mutex
}

// NewSubBaseRepo 创建分表仓库
func NewSubBaseRepo[T any](rawTable string, shardFunc func(params T) string) *SubBaseRepo[T] {
	return &SubBaseRepo[T]{
		rawTable:  rawTable,
		shardFunc: shardFunc,
		repoMap:   make(map[string]*BaseRepo[T]),
		initLock:  sync.Mutex{},
	}
}

// GetRepo 从记录获取仓库
func (r *SubBaseRepo[T]) GetRepo(record T) *BaseRepo[T] {
	shardKey := r.shardFunc(record)
	return r.GetRepoByShardKey(shardKey)
}

// GetRepoByShardKey 通过分片键获取仓库 shardKey 分片键
func (r *SubBaseRepo[T]) GetRepoByShardKey(shardKey string) *BaseRepo[T] {
	if repo, ok := r.repoMap[shardKey]; ok {
		return repo
	}
	r.initLock.Lock()
	defer r.initLock.Unlock()
	repo := NewBaseRepo[T]()
	repo.customScope = func(ctx context.Context) *gorm.DB {
		tableName := r.rawTable + "_" + shardKey
		return repo.DB.WithContext(ctx).Model(new(T)).Table(tableName)
	}
	r.repoMap[shardKey] = repo
	return repo
}

// CreateSubTable 创建分表
func (r *SubBaseRepo[T]) CreateSubTable(ctx context.Context, record T) error {
	shardKey := r.shardFunc(record)
	return r.CreateSubTableByShard(ctx, shardKey)
}

// CreateSubTableByShard 通过分片键创建分表
func (r *SubBaseRepo[T]) CreateSubTableByShard(ctx context.Context, shard string) error {
	sql := "CREATE TABLE IF NOT EXISTS " + r.rawTable + "_" + shard + " LIKE " + r.rawTable
	repo := r.GetRepoByShardKey(shard)
	return repo.DB.Exec(sql).Error
}

// DropSubTable 删除分表
func (r *SubBaseRepo[T]) DropSubTable(ctx context.Context, record T) error {
	shard := r.shardFunc(record)
	return r.DropSubTableByShard(ctx, shard)
}

// DropSubTableByShard 通过分片键删除分表
func (r *SubBaseRepo[T]) DropSubTableByShard(ctx context.Context, shard string) error {
	sql := "DROP TABLE IF EXISTS " + r.rawTable + "_" + shard
	repo := r.GetRepoByShardKey(shard)
	return repo.DB.Exec(sql).Error
}
