package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/constant"
	"app/schema"
	"app/util"

	"gorm.io/gorm"
)

// BaseRepo 基础仓库
type BaseRepo[T any] struct {
	DB          *gorm.DB
	customScope func(ctx context.Context) *gorm.DB
}

func NewBaseRepo[T any]() *BaseRepo[T] {
	return &BaseRepo[T]{
		DB: conn.GetDB(),
	}
}

// Scope 设置自定义查询条件
func (r *BaseRepo[T]) Scope(ctx context.Context) *gorm.DB {
	if r.customScope != nil {
		return r.customScope(ctx)
	}
	return r.DB.WithContext(ctx).Model(new(T))
}

// NormalScope 设置正常状态查询条件
func (r *BaseRepo[T]) NormalScope(ctx context.Context) *gorm.DB {
	return r.Scope(ctx).Where("is_delete = ?", constant.StatusNormal)
}

// Create 创建
func (r *BaseRepo[T]) Create(ctx context.Context, record *T) error {
	return r.Scope(ctx).Create(record).Error
}

// Save 保存
func (r *BaseRepo[T]) Save(ctx context.Context, record *T) error {
	arr := []T{*record}
	return r.Scope(ctx).Save(arr).Error
}

// CreateBatch 批量创建
func (r *BaseRepo[T]) CreateBatch(ctx context.Context, records []T) error {
	return r.Scope(ctx).CreateInBatches(records, 100).Error
}

// FindById 根据ID查找
func (r *BaseRepo[T]) FindById(ctx context.Context, id any) (record *T, err error) {
	record = new(T)
	err = r.Scope(ctx).Where("id = ?", id).Take(record).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find by id fail: %w, id: %d", err, id)
	}
	return record, nil
}

// FindByIds 根据IDs查找
func (r *BaseRepo[T]) FindByIds(ctx context.Context, ids ...any) (records []T, err error) {
	if len(ids) == 0 {
		return records, nil
	}
	flattenIds := make([]interface{}, 0)
	for _, id := range ids {
		flattenIds = append(flattenIds, util.AssertToArray(id)...)
	}
	if len(flattenIds) == 0 {
		return
	}
	err = r.Scope(ctx).Where("id IN ?", flattenIds).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return records, nil
	}
	return
}

// FindNormalByIds 根据IDs查找正常状态
func (r *BaseRepo[T]) FindNormalByIds(ctx context.Context, ids ...any) (records []T, err error) {
	if len(ids) == 0 {
		return records, nil
	}
	flattenIds := make([]interface{}, 0)
	for _, id := range ids {
		flattenIds = append(flattenIds, util.AssertToArray(id)...)
	}
	if len(flattenIds) == 0 {
		return
	}
	err = r.NormalScope(ctx).Where("id IN ?", flattenIds).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}

// FindNormalById 根据ID查找正常状态
func (r *BaseRepo[T]) FindNormalById(ctx context.Context, id int64) (record *T, err error) {
	record = new(T)
	err = r.NormalScope(ctx).Where("id = ?", id).Take(record).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return
}

// UpdateById 根据ID更新
func (r *BaseRepo[T]) UpdateById(ctx context.Context, id any, update map[string]interface{}) error {
	if err := r.Scope(ctx).Where("id = ?", id).Updates(update).Error; err != nil {
		return fmt.Errorf("update by id fail: %w, id: %d", err, id)
	}
	return nil
}

// UpdateByIds 根据IDs更新
func (r *BaseRepo[T]) UpdateByIds(ctx context.Context, ids any, update map[string]interface{}) error {
	realIds := util.AssertToArray(ids)
	if len(realIds) == 0 {
		return nil
	}
	return r.Scope(ctx).Where("id IN ?", realIds).Updates(update).Error
}

// QueryWithPagination 分页查询
func (r *BaseRepo[T]) QueryWithPagination(ctx context.Context, query *gorm.DB, pagination *schema.Pagination) (records []T, err error) {
	err = query.Offset((pagination.Page - 1) * pagination.PageSize).Limit(pagination.PageSize).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return records, nil
	}
	return
}

// IncrById 根据ID增加
func (r *BaseRepo[T]) IncrById(ctx context.Context, id int64, col string, incr int) error {
	update := map[string]interface{}{}
	if incr > 0 {
		update[col] = gorm.Expr(col+" + ?", incr)
	} else {
		update[col] = gorm.Expr("case when "+col+" <? then 0 else "+col+" - ? end", -incr, -incr)
	}
	return r.Scope(ctx).Where("id = ?", id).Updates(update).Error
}

// IncrByIds 根据IDs增加
func (r *BaseRepo[T]) IncrByIds(ctx context.Context, ids any, col string, incr int) error {
	realIds := util.AssertToArray(ids)
	if len(realIds) == 0 {
		return nil
	}
	update := map[string]interface{}{}
	if incr > 0 {
		update[col] = gorm.Expr(col+" + ?", incr)
	} else {
		update[col] = gorm.Expr("case when "+col+" <? then 0 else "+col+" - ? end", -incr, -incr)
	}
	return r.Scope(ctx).Where("id IN ?", realIds).Updates(update).Error
}

// IncrMapById 根据ID增加多个字段
func (r *BaseRepo[T]) IncrMapById(ctx context.Context, id any, incr map[string]int) error {
	update := map[string]interface{}{}
	for k, v := range incr {
		if v > 0 {
			update[k] = gorm.Expr(k+" + ?", v)
		} else if v < 0 {
			update[k] = gorm.Expr("case when "+k+" <? then 0 else "+k+" - ? end", -v, -v)
		}
	}
	return r.Scope(ctx).Where("id = ?", id).Updates(update).Error
}

// IncrMapByIds 根据IDs增加多个字段
func (r *BaseRepo[T]) DeleteById(ctx context.Context, id any) error {
	return r.Scope(ctx).Where("id = ?", id).Delete(new(T)).Error
}
