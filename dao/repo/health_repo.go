package repo

import "context"

type HealthInfo struct {
	IsOk bool `json:"is_ok"`
}

type HealthRepo struct {
	BaseRepo[HealthInfo]
}

func NewHealthRepo() *HealthRepo {
	return &HealthRepo{
		BaseRepo: *NewBaseRepo[HealthInfo](),
	}
}

func (r *HealthRepo) Health(ctx context.Context) (HealthInfo, error) {
	if err := r.DB.Exec("SELECT 1").Error; err != nil {
		return HealthInfo{IsOk: false}, err
	}
	return HealthInfo{IsOk: true}, nil
}
