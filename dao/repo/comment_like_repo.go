package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CommentLikeRepo struct {
	BaseRepo[model.L36ActionWorkCommentLike]
}

func NewCommentLikeRepo() *CommentLikeRepo {
	return &CommentLikeRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkCommentLike]{
			DB: conn.GetDB(),
		},
	}
}

// FindOneByUserComment 查询用户对特定评论的点赞记录
//
// ctx 上下文
// userId 用户ID
// commentId 评论ID
// 返回点赞记录，如果不存在返回nil
func (r *CommentLikeRepo) FindOneByUserComment(ctx context.Context, userId int64, commentId int64) (*model.L36ActionWorkCommentLike, error) {
	var like model.L36ActionWorkCommentLike
	err := r.Scope(ctx).Where("user_id = ? AND comment_id = ?", userId, commentId).First(&like).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &like, err
}

// GetUserCommentLikeMap 批量查询用户对多个评论的点赞状态
//
// ctx 上下文
// userId 用户ID
// commentIds 评论ID列表
// 返回map[评论ID]是否点赞
func (r *CommentLikeRepo) GetUserCommentLikeMap(ctx context.Context, userId int64, commentIds []int64) (map[int64]bool, error) {
	likeMap := make(map[int64]bool)
	if len(commentIds) == 0 {
		return likeMap, nil
	}

	var likes []model.L36ActionWorkCommentLike
	err := r.NormalScope(ctx).Where("user_id = ? AND comment_id IN (?)", userId, commentIds).Find(&likes).Error
	if err == gorm.ErrRecordNotFound {
		return likeMap, nil
	}
	if err != nil {
		return likeMap, err
	}

	for _, like := range likes {
		likeMap[like.CommentID] = true
	}
	return likeMap, nil
}

// Like 创建或更新点赞记录
//
// ctx 上下文
// userId 用户ID
// roleId 角色ID
// commentId 评论ID
func (r *CommentLikeRepo) Like(ctx context.Context, userId int64, roleId int64, commentId int64) (int64, error) {
	like := model.L36ActionWorkCommentLike{
		UserID:    userId,
		RoleID:    roleId,
		CommentID: commentId,
		IsDelete:  0,
	}
	if err := r.Scope(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{
			"is_delete": 0,
		}),
	}).Create(&like).Error; err != nil {
		return 0, fmt.Errorf("like comment fail: %w, userId: %d, roleId: %d, commentId: %d", err, userId, roleId, commentId)
	}
	return like.ID, nil
}
