package repo

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"app/config"
	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
)

type ExpLimitRepo struct {
	BaseRepo[model.L36ActionUserDesignerExpLimit]
}

func NewExpLimitRepo() *ExpLimitRepo {
	return &ExpLimitRepo{
		BaseRepo: BaseRepo[model.L36ActionUserDesignerExpLimit]{
			DB: conn.GetDB(),
		},
	}
}

type ExpLimitSubRepo struct {
	SubBaseRepo[model.L36ActionUserDesignerExpLimit]
}

func NewExpLimitSubRepo() *ExpLimitSubRepo {
	return &ExpLimitSubRepo{
		SubBaseRepo: *NewSubBaseRepo("l36_action_user_designer_exp_limit", func(params model.L36ActionUserDesignerExpLimit) string {
			mod := params.UserID % 100
			part := strconv.Itoa(int(mod))
			if mod < 10 {
				part = "0" + part
			}
			return part
		}),
	}
}

const (
	FieldWorkCount     = "work_count"
	FieldLikeCount     = "like_count"
	FieldCommentCount  = "comment_count"
	FieldShareCount    = "share_count"
	FieldUseCount      = "use_count"
	FieldCompleteCount = "complete_count"
	FieldCollectCount  = "collect_count"
)

// IsReachUserDesignerExpLimit 检查用户对设计师作品的操作是否达到限制
// params:
//   - ctx: 上下文
//   - userId: 用户ID
//   - designerId: 设计师ID
//   - field: 操作类型，可选值：work_count/like_count/comment_count/share_count/use_count/complete_count/collect_count
//
// returns:
//   - bool: true表示已达到限制，false表示未达到限制
//   - error: 错误信息
func (r *ExpLimitSubRepo) IsReachUserDesignerExpLimit(ctx context.Context, userId int64, designerId int64, field string) (bool, error) {
	repo := r.GetRepo(model.L36ActionUserDesignerExpLimit{
		UserID:     userId,
		DesignerID: designerId,
	})
	record := new(model.L36ActionUserDesignerExpLimit)
	err := repo.Scope(ctx).Where("user_id = ? and designer_id = ?", userId, designerId).Take(record).Error
	if err == gorm.ErrRecordNotFound {
		record = nil
		err = nil
	}
	if err != nil {
		return false, fmt.Errorf("query exp limit failed: %w", err)
	}
	if record == nil {
		record = &model.L36ActionUserDesignerExpLimit{
			UserID:     userId,
			DesignerID: designerId,
		}
	}
	isReach := false
	switch field {
	case FieldWorkCount:
		isReach = record.WorkCount >= int32(config.C.Work.EffectCreateLimit)
		record.WorkCount = record.WorkCount + 1
	case FieldLikeCount:
		isReach = record.LikeCount >= int32(config.C.Work.EffectLikeLimit)
		record.LikeCount = record.LikeCount + 1
	case FieldCommentCount:
		isReach = record.CommentCount >= int32(config.C.Work.EffectCommentLimit)
		record.CommentCount = record.CommentCount + 1
	case FieldShareCount:
		isReach = record.ShareCount >= int32(config.C.Work.EffectShareLimit)
		record.ShareCount = record.ShareCount + 1
	case FieldUseCount:
		isReach = record.UseCount >= int32(config.C.Work.EffectUseLimit)
		record.UseCount = record.UseCount + 1
	case FieldCompleteCount:
		isReach = record.CompleteCount >= int32(config.C.Work.EffectCompletePlayLimit)
		record.CompleteCount = record.CompleteCount + 1
	case FieldCollectCount:
		isReach = record.CollectCount >= int32(config.C.Work.EffectCollectLimit)
		record.CollectCount = record.CollectCount + 1
	default:
		return false, errors.New("field not found")
	}
	if record.ID == 0 {
		if err = repo.Create(ctx, record); err != nil {
			if err == gorm.ErrDuplicatedKey {
				return false, nil
			}
			return false, fmt.Errorf("create exp limit failed: %w", err)
		}
	} else if !isReach {
		if err = repo.UpdateById(ctx, record.ID, map[string]interface{}{
			field: gorm.Expr(field + " + 1"),
		}); err != nil {
			return false, fmt.Errorf("update exp limit failed: %w", err)
		}
	}
	return isReach, err
}
