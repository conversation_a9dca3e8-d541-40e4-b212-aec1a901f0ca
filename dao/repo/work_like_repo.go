package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type WorkLikeRepo struct {
	BaseRepo[model.L36ActionWorkLike]
}

func NewWorkLikeRepo() *WorkLikeRepo {
	return &WorkLikeRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkLike]{
			DB: conn.GetDB(),
		},
	}
}

// GetLikeMap 获取用户对作品的点赞状态映射。
// 根据用户ID和作品ID列表，返回每个作品是否被该用户点赞的映射关系。
//
// ctx 上下文
// userId 用户ID
// workIds 作品ID列表
// 返回map[int64]bool 作品点赞状态映射，key为作品ID，value为是否点赞
// 返回error 查询错误
func (r *WorkLikeRepo) GetLikeMap(ctx context.Context, userId int64, workIds []int64) (map[int64]bool, error) {
	if userId <= 0 || len(workIds) == 0 {
		return make(map[int64]bool), nil
	}

	likeMap := make(map[int64]bool)
	var likes []model.L36ActionWorkLike
	err := r.NormalScope(ctx).Where("user_id = ? and work_id in (?)", userId, workIds).Find(&likes).Error
	if err == gorm.ErrRecordNotFound {
		return likeMap, nil
	}
	if err != nil {
		return likeMap, err
	}

	for _, like := range likes {
		likeMap[like.WorkID] = true
	}
	return likeMap, nil
}

// FindByUserWork 查询用户对指定作品的点赞记录。
//
// ctx 上下文
// userId 用户ID
// workId 作品ID
// 返回*model.L36ActionWorkLike 点赞记录，未找到返回nil
// 返回error 查询错误
func (r *WorkLikeRepo) FindByUserWork(ctx context.Context, userId int64, workId int64) (*model.L36ActionWorkLike, error) {
	if userId <= 0 || workId <= 0 {
		return nil, nil
	}

	var like model.L36ActionWorkLike
	err := r.Scope(ctx).Where("user_id = ? and work_id = ?", userId, workId).Take(&like).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &like, err
}

// Like 创建或更新用户对作品的点赞记录。
// 如果记录已存在则更新为未删除状态。
//
// ctx 上下文
// userId 用户ID
// roleId 角色ID
// workId 作品ID
// 返回error 操作错误
func (r *WorkLikeRepo) Like(ctx context.Context, userId int64, roleId int64, workId int64) (int64, error) {
	if userId <= 0 || workId <= 0 {
		return 0, nil
	}

	like := model.L36ActionWorkLike{
		UserID:   userId,
		RoleID:   roleId,
		WorkID:   workId,
		IsDelete: 0,
	}
	if err := r.Scope(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{
			"is_delete": 0,
		}),
	}).Create(&like).Error; err != nil {
		return 0, fmt.Errorf("like work fail: %w, userId: %d, roleId: %d, workId: %d", err, userId, roleId, workId)
	}
	return like.ID, nil
}

// CountByTimeRange 统计指定时间范围内各作品的点赞数量。
//
// ctx 上下文
// startTime 开始时间戳（秒）
// endTime 结束时间戳（秒）
// 返回[]schema.WorkCounter 作品点赞统计列表
// 返回error 查询错误
func (r *WorkLikeRepo) CountByTimeRange(ctx context.Context, startTime int64, endTime int64) ([]schema.WorkCounter, error) {
	if startTime >= endTime {
		return []schema.WorkCounter{}, nil
	}

	var works []schema.WorkCounter
	err := r.Scope(ctx).
		Where("ctime >= ? and ctime < ?", startTime, endTime).
		Group("work_id").
		Select("work_id, count(id) as count").
		Find(&works).Error

	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}
