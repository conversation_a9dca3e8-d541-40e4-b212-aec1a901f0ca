package repo

import (
	"context"
	"strconv"
	"time"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
)

// WorkPlayRepo 作品播放记录仓库
type WorkPlayRepo struct {
	BaseRepo[model.L36ActionWorkPlay]
}

// NewWorkPlayRepo 创建作品播放记录仓库实例
func NewWorkPlayRepo() *WorkPlayRepo {
	return &WorkPlayRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkPlay]{
			DB: conn.GetDB(),
		},
	}
}

// WorkPlaySubRepo 作品播放记录分表仓库
type WorkPlaySubRepo struct {
	SubBaseRepo[model.L36ActionWorkPlay]
}

// NewWorkPlaySubRepo 创建作品播放记录分表仓库实例
func NewWorkPlaySubRepo() *WorkPlaySubRepo {
	return &WorkPlaySubRepo{
		SubBaseRepo: *NewSubBaseRepo("l36_action_work_play", func(params model.L36ActionWorkPlay) string {
			date := params.Date
			month := date / 100
			return strconv.Itoa(int(month))
		}),
	}
}

// FindByWorkUserDate 查询指定用户在指定日期对作品的播放记录。
//
// ctx 上下文
// workId 作品ID
// userId 用户ID
// date 日期，格式：YYYYMMDD
// 返回 *model.L36ActionWorkPlay 播放记录，未找到返回nil
// 返回 error 查询错误
func (r *WorkPlaySubRepo) FindByWorkUserDate(ctx context.Context, workId int64, userId int64, date int64) (*model.L36ActionWorkPlay, error) {
	if workId <= 0 || userId <= 0 || date <= 0 {
		return nil, nil
	}

	var workPlay model.L36ActionWorkPlay
	repo := r.GetRepo(model.L36ActionWorkPlay{
		Date: date,
	})
	err := repo.Scope(ctx).Where("work_id = ? and user_id = ? and date = ?", workId, userId, date).Take(&workPlay).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &workPlay, err
}

// Create 创建播放记录。
//
// ctx 上下文
// workPlay 播放记录
// 返回 error 操作错误
func (r *WorkPlaySubRepo) Create(ctx context.Context, workPlay *model.L36ActionWorkPlay) error {
	if workPlay == nil {
		return nil
	}

	repo := r.GetRepo(*workPlay)
	return repo.Create(ctx, workPlay)
}

// CountByTimeRange 统计指定时间范围内各作品的播放数量。
//
// ctx 上下文
// startTime 开始时间戳（毫秒）
// endTime 结束时间戳（毫秒）
// 返回 []schema.WorkCounter 作品播放统计列表
// 返回 error 查询错误
func (r *WorkPlaySubRepo) CountByTimeRange(ctx context.Context, startTime int64, endTime int64) ([]schema.WorkCounter, error) {
	if startTime >= endTime {
		return []schema.WorkCounter{}, nil
	}

	var works []schema.WorkCounter
	t := time.UnixMilli(startTime)
	date := int64(t.Year()*10000 + int(t.Month())*100 + t.Day())
	repo := r.GetRepo(model.L36ActionWorkPlay{
		Date: date,
	})

	err := repo.Scope(ctx).
		Where("ctime >= ? and ctime < ?", startTime, endTime).
		Group("work_id").
		Select("work_id, count(id) as count").
		Find(&works).Error

	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}
