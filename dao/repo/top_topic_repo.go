package repo

import (
	"context"
	"time"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
)

type TopTopicRepo struct {
	BaseRepo[model.L36ActionTopTopic]
}

func NewTopTopicRepo() *TopTopicRepo {
	return &TopTopicRepo{
		BaseRepo: BaseRepo[model.L36ActionTopTopic]{
			DB: conn.GetDB(),
		},
	}
}

// GetValidTopTopics 获取有效的置顶话题
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - 有效的置顶话题列表
//   - 错误信息
func (r *TopTopicRepo) GetValidTopTopics(ctx context.Context) ([]model.L36ActionTopTopic, error) {
	var topTopics []model.L36ActionTopTopic
	now := time.Now().UnixMilli()
	err := r.NormalScope(ctx).
		Where("start_time <= ? AND end_time >= ?", now, now).
		Order("weight desc").
		Find(&topTopics).Error
	if err != nil {
		return nil, err
	}
	return topTopics, nil
}

func (r *TopTopicRepo) FindByTopicID(ctx context.Context, topicID int64) (*model.L36ActionTopTopic, error) {
	var topTopic model.L36ActionTopTopic
	err := r.NormalScope(ctx).Where("topic_id = ?", topicID).First(&topTopic).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &topTopic, nil
}
