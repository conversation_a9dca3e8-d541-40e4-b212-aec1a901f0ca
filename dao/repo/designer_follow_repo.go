package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DesignerFollowRepo struct {
	BaseRepo[model.L36ActionDesignerFollow]
}

func NewDesignerFollowRepo() *DesignerFollowRepo {
	return &DesignerFollowRepo{
		BaseRepo: BaseRepo[model.L36ActionDesignerFollow]{
			DB: conn.GetDB(),
		},
	}
}

// GetFollowMap 获取用户对多个设计师的关注状态映射
//
// ctx 上下文
// userId 用户ID
// designerIds 设计师ID列表
// 返回map[设计师ID]是否关注
func (r *DesignerFollowRepo) GetFollowMap(ctx context.Context, userId int64, designerIds []int64) (map[int64]bool, error) {
	followMap := make(map[int64]bool)
	if len(designerIds) == 0 {
		return followMap, nil
	}

	var follows []model.L36ActionDesignerFollow
	err := r.NormalScope(ctx).Where("user_id = ? and designer_id in (?)", userId, designerIds).Find(&follows).Error
	if err != nil {
		return nil, err
	}

	for _, follow := range follows {
		followMap[follow.DesignerID] = true
	}
	return followMap, nil
}

// Follow 关注设计师
//
// ctx 上下文
// userId 用户ID
// roleId 角色ID
// designerId 设计师ID
func (r *DesignerFollowRepo) Follow(ctx context.Context, userId int64, roleId int64, designerId int64) error {
	follow := model.L36ActionDesignerFollow{
		UserID:     userId,
		RoleID:     roleId,
		DesignerID: designerId,
		IsDelete:   0,
	}
	err := r.Scope(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{
			"is_delete": 0,
		}),
	}).Create(&follow).Error
	if err != nil {
		return fmt.Errorf("follow designer fail: %w, userId: %d, roleId: %d, designerId: %d", err, userId, roleId, designerId)
	}
	return nil
}

// FindOneByUserDesigner 查询用户对某设计师的关注记录
//
// ctx 上下文
// userId 用户ID
// designerId 设计师ID
// 返回关注记录，不存在时返回nil
func (r *DesignerFollowRepo) FindOneByUserDesigner(ctx context.Context, userId int64, designerId int64) (*model.L36ActionDesignerFollow, error) {
	var follow model.L36ActionDesignerFollow
	err := r.Scope(ctx).Where("user_id = ? and designer_id = ?", userId, designerId).Take(&follow).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &follow, err
}

// ListDesignerFollow 获取用户关注的设计师列表
//
// ctx 上下文
// params 查询参数，包含用户ID和分页信息
func (r *DesignerFollowRepo) ListDesignerFollow(ctx context.Context, params *schema.DesignerListFollowReq) ([]model.L36ActionDesignerFollow, error) {
	query := r.NormalScope(ctx).Where("user_id = ?", params.UserId).Order("id desc")
	return r.QueryWithPagination(ctx, query, &params.Pagination)
}
