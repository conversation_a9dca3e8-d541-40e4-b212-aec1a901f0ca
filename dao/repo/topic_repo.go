package repo

import (
	"context"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TopicRepo struct {
	BaseRepo[model.L36ActionTopic]
}

func NewTopicRepo() *TopicRepo {
	return &TopicRepo{
		BaseRepo: BaseRepo[model.L36ActionTopic]{
			DB: conn.GetDB(),
		},
	}
}

// FindOrInitByNames 根据话题名称列表查找或初始化话题
//
// ctx 上下文
// names 话题名称列表
// 返回话题列表
// 返回查询错误信息
func (r *TopicRepo) FindOrInitByNames(ctx context.Context, names []string) ([]model.L36ActionTopic, error) {
	if len(names) == 0 {
		return []model.L36ActionTopic{}, nil
	}

	var topics []model.L36ActionTopic
	err := r.<PERSON>(ctx).Where("name in (?)", names).Find(&topics).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	if err != nil {
		return nil, err
	}

	// 如果全部找到，直接返回
	if len(topics) == len(names) {
		return topics, nil
	}

	// 找出缺失的话题名称
	existMap := make(map[string]bool)
	for _, topic := range topics {
		existMap[topic.Name] = true
	}

	var missNames []string
	for _, name := range names {
		if !existMap[name] {
			missNames = append(missNames, name)
		}
	}

	// 创建新话题
	newTopics := make([]model.L36ActionTopic, len(missNames))
	for i, name := range missNames {
		newTopics[i] = model.L36ActionTopic{Name: name}
	}

	err = r.Scope(ctx).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&newTopics).Error
	if err == gorm.ErrDuplicatedKey {
		err = nil
	}
	if err != nil {
		return nil, err
	}

	return append(topics, newTopics...), nil
}

// FindByName 根据话题名称查询单个话题
//
// ctx 上下文
// name 话题名称
// 返回话题信息，未找到返回nil
// 返回查询错误信息
func (r *TopicRepo) FindByName(ctx context.Context, name string) (*model.L36ActionTopic, error) {
	if name == "" {
		return nil, nil
	}

	var topic model.L36ActionTopic
	err := r.Scope(ctx).Where("name = ?", name).Take(&topic).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &topic, err
}

// SearchTopics 根据关键词搜索话题列表
//
// ctx 上下文
// params 搜索参数
// 返回话题列表
// 返回查询错误信息
func (r *TopicRepo) SearchTopics(ctx context.Context, params *schema.TopicSearchReq) ([]model.L36ActionTopic, error) {
	if params == nil {
		return []model.L36ActionTopic{}, nil
	}

	var topics []model.L36ActionTopic
	query := r.NormalScope(ctx)

	if params.Keyword != "" {
		query = query.Where("name like ?", "%"+params.Keyword+"%")
	}

	err := query.Order("hot desc").
		Limit(params.PageSize).
		Offset(params.PageSize * (params.Page - 1)).
		Find(&topics).Error

	if err == gorm.ErrRecordNotFound {
		return topics, nil
	}
	return topics, err
}

// ListTopics 获取热门话题列表
//
// ctx 上下文
// params 分页参数
// 返回话题列表
// 返回查询错误信息
func (r *TopicRepo) ListTopics(ctx context.Context, params *schema.TopicListReq) ([]model.L36ActionTopic, error) {
	if params == nil {
		return []model.L36ActionTopic{}, nil
	}

	var topics []model.L36ActionTopic
	err := r.NormalScope(ctx).
		Order("hot desc").
		Limit(params.PageSize).
		Offset(params.PageSize * (params.Page - 1)).
		Find(&topics).Error

	if err == gorm.ErrRecordNotFound {
		return topics, nil
	}
	return topics, err
}
