package repo

import (
	"context"
	"strings"

	"app/conn"
	"app/constant"
	"app/dao/model"
	"app/schema"
	"app/util"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type WorkRepo struct {
	BaseRepo[model.L36ActionWork]
}

func NewWorkRepo() *WorkRepo {
	return &WorkRepo{
		BaseRepo: BaseRepo[model.L36ActionWork]{
			DB: conn.GetDB(),
		},
	}
}

func NewReadonlyWorkRepo() *WorkRepo {
	return &WorkRepo{
		BaseRepo: BaseRepo[model.L36ActionWork]{
			DB: conn.GetDB(conn.SLAVE),
		},
	}
}

// GetTopHotWorks 获取指定类型的热门作品列表。
//
// ctx 上下文
// workType 作品类型
// count 返回数量
// 返回 []model.L36ActionWork 作品列表
// 返回 error 查询错误
func (r *WorkRepo) GetTopHotWorks(ctx context.Context, workType int, count int) ([]model.L36ActionWork, error) {
	if count <= 0 {
		return []model.L36ActionWork{}, nil
	}

	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Where("visibility = ? and type = ? ", constant.VisibilityPublic, workType).Order("hot desc").Limit(count).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) GetAllTopHotWorks(ctx context.Context, count int) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Where("visibility = ? ", constant.VisibilityPublic).Order("hot desc").Limit(count).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) FindPublicByIds(ctx context.Context, workIds []int64) ([]model.L36ActionWork, error) {
	works := make([]model.L36ActionWork, 0)
	if len(workIds) == 0 {
		return works, nil
	}

	err := r.NormalScope(ctx).Where("visibility = ? and id in (?) and audit_status = ?", constant.VisibilityPublic, workIds, constant.FpReviewStatusPass).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		return works, nil
	}
	if err != nil {
		return works, err
	}
	// 按照workIds排序
	workMap := lo.KeyBy(works, func(work model.L36ActionWork) int64 {
		return work.ID
	})
	sortedWorks := make([]model.L36ActionWork, 0)
	for _, workId := range workIds {
		if work, ok := workMap[workId]; ok {
			sortedWorks = append(sortedWorks, work)
		}
	}
	return sortedWorks, nil
}

func (r *WorkRepo) GetWorkSort(sort string, order string) []string {
	if sort == "" {
		sort = "new"
	}
	if order == "" {
		order = "desc"
	}
	sortKey := sort
	switch sort {
	case "new":
		sortKey = "id"
	case "collect":
		sortKey = "(collect_count + fake_collect_count)"
	case "like":
		sortKey = "(like_count + fake_like_count)"
	case "comment":
		sortKey = "(comment_count + fake_comment_count)"
	case "hot":
		sortKey = "hot"
	default:
		sortKey = "id"
	}
	return []string{sortKey, order}
}

func (r *WorkRepo) ListWorkMine(ctx context.Context, params *schema.WorkListMineReq) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	query := r.NormalScope(ctx)
	if params.UserId != 0 {
		query = query.Where("user_id = ?", params.UserId)
	}
	if params.Keyword != "" {
		query = query.Where("(name like ? or summary like ?)", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}
	if params.Gender != 0 {
		query = query.Where("gender", params.Gender-1)
	}
	if params.Contents != "" {
		contents, err := util.CsvStringToIntSlice(params.Contents)
		if err != nil {
			return nil, err
		}
		val := util.IntSliceToDigit(contents)
		query = query.Where("contents & ? = ?", val, val)
	}
	sort := r.GetWorkSort(params.Sort, params.Order)
	err := query.Order(sort[0] + " " + sort[1]).Limit(params.PageSize).Offset(params.PageSize * (params.Page - 1)).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) ListWorkDesigner(ctx context.Context, params *schema.WorkListDesignerReq) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	query := r.NormalScope(ctx)
	if params.DesignerId != 0 {
		query = query.Where("user_id = ?", params.DesignerId)
	}
	if params.Keyword != "" {
		query = query.Where("(name like ? or summary like ?)", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}
	if params.DesignerId != params.UserId {
		query = query.Where("visibility", constant.VisibilityPublic)
	}
	if params.Gender != 0 {
		query = query.Where("gender", params.Gender-1)
	}
	if params.Contents != "" {
		contents, err := util.CsvStringToIntSlice(params.Contents)
		if err != nil {
			return nil, err
		}
		val := util.IntSliceToDigit(contents)
		query = query.Where("contents & ? = ?", val, val)
	}
	sort := r.GetWorkSort(params.Sort, params.Order)
	query = query.Order(sort[0] + " " + sort[1])
	if sort[0] != "id" { // 排序字段不是id时，id倒序
		query = query.Order("id desc")
	}
	err := query.Limit(params.PageSize).Offset(params.PageSize * (params.Page - 1)).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) ListWorkCollect(ctx context.Context, params *schema.WorkListCollectReq) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	query := r.Scope(ctx).Table(model.TableNameL36ActionWork+" as w").Joins("left join l36_action_work_collect as c on w.id = c.work_id").Select("w.*").Where("c.user_id = ? and c.is_delete = 0", params.UserId)
	if params.Keyword != "" {
		query = query.Where("(w.name like ? or w.summary like ?)", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}
	if params.Gender != 0 {
		query = query.Where("w.gender", params.Gender-1)
	}
	if params.Contents != "" {
		contents, err := util.CsvStringToIntSlice(params.Contents)
		if err != nil {
			return nil, err
		}
		val := util.IntSliceToDigit(contents)
		query = query.Where("w.contents & ? = ?", val, val)
	}
	query = query.Where("w.visibility", constant.VisibilityPublic).Where("w.is_delete = 0")

	// 添加排序支持
	sort := r.GetWorkSort(params.Sort, params.Order)
	sortKey := sort[0]
	if sort[0] == "id" {
		sortKey = "c.id" // 对于id排序，使用收藏表的id以保持收藏时间顺序
	} else if strings.Contains(sort[0], "(") {
		// 处理包含假数据的排序字段，直接写死具体的情况
		switch sort[0] {
		case "(like_count + fake_like_count)":
			sortKey = "(w.like_count + w.fake_like_count)"
		case "(collect_count + fake_collect_count)":
			sortKey = "(w.collect_count + w.fake_collect_count)"
		case "(comment_count + fake_comment_count)":
			sortKey = "(w.comment_count + w.fake_comment_count)"
		default:
			sortKey = "w." + sort[0] // 兜底处理
		}
	} else {
		sortKey = "w." + sort[0] // 由于使用了表别名，需要加上w.前缀
	}
	query = query.Order(sortKey + " " + sort[1])
	if sort[0] != "id" { // 排序字段不是id时，id倒序
		query = query.Order("c.id desc")
	}

	err := query.Limit(params.PageSize).Offset(params.PageSize * (params.Page - 1)).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) FindUserTopWorks(ctx context.Context, userId int64, count int) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Where("user_id = ? and visibility = ?", userId, constant.VisibilityPublic).Order("hot desc").Limit(count).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) FindReviewingWorksBetween(ctx context.Context, start, end int64) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Where("audit_status = ? and ctime >= ? and ctime <= ? ", constant.FpReviewStatusReviewing, start, end).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) FindToDecayWorks(ctx context.Context, offset int, limit int, todayStart int64) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Where("id > ? and audit_status = ? and visibility = ? and hot > 0 and ctime < ?", offset, constant.FpReviewStatusPass, constant.VisibilityPublic, todayStart).Order("id asc").Limit(limit).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) FindAll(ctx context.Context) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkRepo) GetIdByGteCTime(ctx context.Context, ctime int64) (int64, error) {
	var work model.L36ActionWork
	err := r.NormalScope(ctx).Where("ctime >= ?", ctime).Order("id asc").Limit(1).Take(&work).Error
	if err == gorm.ErrRecordNotFound {
		return 0, nil
	}
	return work.ID, err
}

func (r *WorkRepo) FindByUserId(ctx context.Context, userId int64, limit int) ([]model.L36ActionWork, error) {
	var works []model.L36ActionWork
	err := r.NormalScope(ctx).Where("user_id = ?", userId).Order("id desc").Limit(limit).Find(&works).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return works, err
}
