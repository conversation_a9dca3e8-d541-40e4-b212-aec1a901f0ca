package repo

import (
	"context"
	"fmt"
	"time"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type GmCreatorBanRepo struct {
	BaseRepo[model.L36ActionGmCreatorBan]
}

func NewGmCreatorBanRepo() *GmCreatorBanRepo {
	return &GmCreatorBanRepo{
		BaseRepo: BaseRepo[model.L36ActionGmCreatorBan]{
			DB: conn.GetDB(),
		},
	}
}

func (r *GmCreatorBanRepo) FindOneByUserBanType(ctx context.Context, userId int64, banType int) (*model.L36ActionGmCreatorBan, error) {
	var ban model.L36ActionGmCreatorBan
	err := r.Scope(ctx).Where("user_id = ? and ban_type = ?", userId, banType).First(&ban).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &ban, err
}

func (r *GmCreatorBanRepo) UpdateBan(ctx context.Context, ban *model.L36ActionGmCreatorBan) error {
	err := r.<PERSON><PERSON>(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "ban_type"}},
		DoUpdates: clause.AssignmentColumns([]string{"expire_time"}),
	}).Save(ban).Error
	if err != nil {
		return fmt.Errorf("update ban error, user_id: %d, expire_time: %d, %w", ban.UserID, ban.ExpireTime, err)
	}
	return nil
}

func (r *GmCreatorBanRepo) IsBanned(ctx context.Context, userId int64, banType int) (bool, error) {
	ban, err := r.FindOneByUserBanType(ctx, userId, banType)
	if err != nil {
		return false, err
	}
	return ban != nil && ban.ExpireTime > time.Now().UnixMilli(), nil
}
