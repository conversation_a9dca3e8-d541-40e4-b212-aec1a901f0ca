package repo

import (
	"context"

	"app/config"
	"app/conn"
	"app/dao/model"
	"app/util"

	"gorm.io/gorm"
)

type FashionWorkRepo struct {
	BaseRepo[model.L36FashionWork]
}

func NewFashionWorkRepo() *FashionWorkRepo {
	return &FashionWorkRepo{
		BaseRepo: BaseRepo[model.L36FashionWork]{
			DB: conn.GetDB(),
		},
	}
}

func NewOnlineFashionWorkRepo() *FashionWorkRepo {
	return &FashionWorkRepo{
		BaseRepo: BaseRepo[model.L36FashionWork]{
			DB: conn.GetDB(conn.ONLINE),
		},
	}
}

func GetFashionWorkRepo() *FashionWorkRepo {
	if config.C.OnlineMysql.Host != "" {
		return NewOnlineFashionWorkRepo()
	}
	return NewFashionWorkRepo()
}

func (r *FashionWorkRepo) FindNormalByIds(ctx context.Context, ids ...any) (records []model.L36FashionWork, err error) {
	if len(ids) == 0 {
		return records, nil
	}
	flattenIds := make([]interface{}, 0)
	for _, id := range ids {
		flattenIds = append(flattenIds, util.AssertToArray(id)...)
	}
	if len(flattenIds) == 0 {
		return
	}
	err = r.Scope(ctx).Where("id IN ? and status = 0 and visibility = 0 and auditStatus = 1", flattenIds).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}
