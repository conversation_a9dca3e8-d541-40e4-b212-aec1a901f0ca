package repo

import (
	"context"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserRepo struct {
	BaseRepo[model.L36ActionUser]
}

func NewUserRepo() *UserRepo {
	return &UserRepo{
		BaseRepo: BaseRepo[model.L36ActionUser]{
			DB: conn.GetDB(),
		},
	}
}

// FindOneByAid 根据aid查询单个用户信息
//
// ctx 上下文
// aid 用户aid
// 返回用户信息，未找到返回nil
// 返回查询错误信息
func (r *UserRepo) FindOneByAid(ctx context.Context, aid string) (*model.L36ActionUser, error) {
	if aid == "" {
		return nil, nil
	}

	var user model.L36ActionUser
	err := r.Scope(ctx).Where("aid = ?", aid).First(&user).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &user, err
}

// FindOneByRoleId 根据角色ID查询单个用户信息
//
// ctx 上下文
// roleId 角色ID
// 返回用户信息，未找到返回nil
// 返回查询错误信息
func (r *UserRepo) FindOneByRoleId(ctx context.Context, roleId int64) (*model.L36ActionUser, error) {
	if roleId <= 0 {
		return nil, nil
	}

	var user model.L36ActionUser
	err := r.Scope(ctx).Where("role_id = ?", roleId).First(&user).Error
	if err == gorm.ErrRecordNotFound {
		role, err := NewRoleRepo().FindOneByRoleId(ctx, roleId)
		if err != nil {
			return nil, err
		}
		if role == nil {
			return nil, nil
		}
		aid := role.Aid
		if aid == "" {
			return nil, nil
		}
		return r.FindOneByAid(ctx, aid)
	}
	return &user, err
}

// getDefaultName 根据aid生成默认用户名
//
// aid 用户aid
// 返回生成的默认用户名
func (r *UserRepo) getDefaultName(aid string) string {
	name := "导演"
	switch len(aid) {
	case 0:
		return name
	case 1:
		return name + aid + "*" + aid
	default:
		return name + aid[:1] + "*" + aid[len(aid)-1:]
	}
}

// SyncUser 同步用户信息，不存在则创建，角色ID不一致则更新
//
// ctx 上下文
// aid 用户aid
// roleId 角色ID
// 返回用户信息
// 返回操作错误信息
func (r *UserRepo) SyncUser(ctx context.Context, aid string, roleId int64) (*model.L36ActionUser, error) {
	if aid == "" || roleId <= 0 {
		return nil, nil
	}

	user, err := r.FindOneByAid(ctx, aid)
	if err != nil {
		return nil, err
	}

	if user == nil {
		role, err := NewRoleRepo().FindOneByRoleId(ctx, roleId)
		if err != nil {
			return nil, err
		}
		roleName := r.getDefaultName(aid)
		if role != nil {
			roleName = role.RoleName
		}
		user = &model.L36ActionUser{
			Aid:          aid,
			RoleID:       roleId,
			Name:         roleName,
			AvatarRoleID: roleId,
			NameRoleID:   roleId,
		}
		err = r.Init(ctx, user)
		if err != nil {
			return nil, err
		}
		return r.FindOneByAid(ctx, aid)
	}

	if user.RoleID != roleId {
		err = r.UpdateRoleId(ctx, aid, roleId)
		if err != nil {
			return nil, err
		}
		user.RoleID = roleId
	}
	return user, nil
}

// Init 初始化用户信息
//
// ctx 上下文
// user 用户信息
// 返回操作错误信息
func (r *UserRepo) Init(ctx context.Context, user *model.L36ActionUser) error {
	if user == nil {
		return nil
	}

	return r.Scope(ctx).Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{
			"role_id": user.RoleID,
		}),
	}).Create(user).Error
}

// UpdateRoleId 更新用户角色ID
//
// ctx 上下文
// aid 用户aid
// roleId 角色ID
// 返回操作错误信息
func (r *UserRepo) UpdateRoleId(ctx context.Context, aid string, roleId int64) error {
	if aid == "" || roleId <= 0 {
		return nil
	}

	return r.Scope(ctx).Where("aid = ?", aid).Update("role_id", roleId).Error
}

// FindTopExpUsers 查询经验值排名前列的用户列表
//
// ctx 上下文
// limit 限制数量
// 返回用户列表
// 返回查询错误信息
func (r *UserRepo) FindTopExpUsers(ctx context.Context, limit int) ([]model.L36ActionUser, error) {
	if limit <= 0 {
		return []model.L36ActionUser{}, nil
	}

	var users []model.L36ActionUser
	err := r.Scope(ctx).Order("exp desc").Limit(limit).Find(&users).Error
	if err == gorm.ErrRecordNotFound {
		return users, nil
	}
	return users, err
}

func (r *UserRepo) FindByNameRoleId(ctx context.Context, roleIds []int64) ([]model.L36ActionUser, error) {
	var users []model.L36ActionUser
	err := r.Scope(ctx).Where("name_role_id IN ?", roleIds).Find(&users).Error
	if err == gorm.ErrRecordNotFound {
		return users, nil
	}
	return users, err
}
