package repo

import (
	"context"
	"fmt"

	"app/conn"
	"app/dao/model"
	"app/schema"

	"gorm.io/gorm"
)

// WorkShareRepo 作品分享记录仓库
type WorkShareRepo struct {
	BaseRepo[model.L36ActionWorkShare]
}

// NewWorkShareRepo 创建作品分享记录仓库实例
func NewWorkShareRepo() *WorkShareRepo {
	return &WorkShareRepo{
		BaseRepo: BaseRepo[model.L36ActionWorkShare]{
			DB: conn.GetDB(),
		},
	}
}

// CountByTimeRange 统计指定时间范围内各作品的分享数量。
//
// ctx 上下文
// startTime 开始时间戳（秒）
// endTime 结束时间戳（秒）
// 返回 []schema.WorkCounter 作品分享统计列表
// 返回 error 查询错误
func (r *WorkShareRepo) CountByTimeRange(ctx context.Context, startTime int64, endTime int64) ([]schema.WorkCounter, error) {
	if startTime >= endTime {
		return []schema.WorkCounter{}, nil
	}

	var works []schema.WorkCounter
	err := r.Scope(ctx).
		Where("effect_time >= ? and effect_time < ? ", startTime, endTime).
		Group("work_id").
		Select("work_id, count(id) as count").
		Find(&works).Error

	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return works, err
}

func (r *WorkShareRepo) FindByWorkUser(ctx context.Context, workId int64, userId int64) (*model.L36ActionWorkShare, error) {
	var share model.L36ActionWorkShare
	err := r.Scope(ctx).Where("work_id = ? and user_id = ?", workId, userId).Take(&share).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("find work share by work id %d and user id %d failed: %w", workId, userId, err)
	}
	return &share, nil
}
