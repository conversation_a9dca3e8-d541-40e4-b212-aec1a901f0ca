package repo

import (
	"context"
	"fmt"

	"app/config"
	"app/conn"
	"app/dao/model"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type RoleRepo struct {
	BaseRepo[model.L36RoleInfo]
}

func NewRoleRepo() *RoleRepo {
	return &RoleRepo{
		BaseRepo: BaseRepo[model.L36RoleInfo]{
			DB: conn.GetDB(),
		},
	}
}

func GetOnlineRoleRepo() *RoleRepo {
	if config.C.OnlineMysql.Host != "" {
		return &RoleRepo{
			BaseRepo: BaseRepo[model.L36RoleInfo]{
				DB: conn.GetDB(conn.ONLINE),
			},
		}
	}
	return NewRoleRepo()
}

// FindByRoleIds 根据角色ID列表批量查询角色信息
//
// ctx 上下文
// roleIds 角色ID列表
// 返回角色信息列表
// 返回查询错误信息
func (r *RoleRepo) FindByRoleIds(ctx context.Context, roleIds []int64) ([]model.L36RoleInfo, error) {
	if len(roleIds) == 0 {
		return []model.L36RoleInfo{}, nil
	}
	var records []model.L36RoleInfo
	err := r.Scope(ctx).Where("RoleId IN (?)", roleIds).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return records, nil
	}
	return records, err
}

// FindOneByRoleId 根据角色ID查询单个角色信息
//
// ctx 上下文
// roleId 角色ID
// 返回角色信息，未找到返回nil
// 返回查询错误信息
func (r *RoleRepo) FindOneByRoleId(ctx context.Context, roleId int64) (*model.L36RoleInfo, error) {
	var record model.L36RoleInfo
	err := r.Scope(ctx).Where("RoleId = ?", roleId).Take(&record).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &record, err
}

// FindByAid 根据aid查询角色信息列表
//
// ctx 上下文
// aid 用户aid
// 返回角色信息列表
// 返回查询错误信息
func (r *RoleRepo) FindByAid(ctx context.Context, aid string) ([]model.L36RoleInfo, error) {
	if aid == "" {
		return []model.L36RoleInfo{}, nil
	}
	var records []model.L36RoleInfo
	err := r.Scope(ctx).Where("aid = ?", aid).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return records, nil
	}
	if err != nil {
		return records, fmt.Errorf("find by aid error: %w", err)
	}
	return records, nil
}

func (r *RoleRepo) FindRelatedRoleMap(ctx context.Context, users []model.L36ActionUser) (map[int64]model.L36RoleInfo, error) {
	if len(users) == 0 {
		return map[int64]model.L36RoleInfo{}, nil
	}
	avatarRoleIds := lo.Map(users, func(user model.L36ActionUser, i int) int64 {
		return user.AvatarRoleID
	})
	nameRoleIds := lo.Map(users, func(user model.L36ActionUser, i int) int64 {
		return user.NameRoleID
	})
	roleIds := lo.Uniq(lo.Union(avatarRoleIds, nameRoleIds))
	roles, err := r.FindByRoleIds(ctx, roleIds)
	if err != nil {
		return nil, err
	}
	return lo.KeyBy(roles, func(role model.L36RoleInfo) int64 {
		return role.RoleID
	}), nil
}

func (r *RoleRepo) FindByName(ctx context.Context, name string) ([]model.L36RoleInfo, error) {
	var records []model.L36RoleInfo
	err := r.Scope(ctx).Where("RoleName = ?", name).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return records, err
}
