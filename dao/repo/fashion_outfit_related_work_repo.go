package repo

import (
	"context"

	"app/config"
	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
)

type FashionOutfitRelatedWorkRepo struct {
	BaseRepo[model.L36FashionOutfitRelatedWork]
}

func NewFashionOutfitRelatedWorkRepo() *FashionOutfitRelatedWorkRepo {
	return &FashionOutfitRelatedWorkRepo{
		BaseRepo: BaseRepo[model.L36FashionOutfitRelatedWork]{
			DB: conn.GetDB(),
		},
	}
}

func NewOnlineFashionOutfitRelatedWorkRepo() *FashionOutfitRelatedWorkRepo {
	return &FashionOutfitRelatedWorkRepo{
		BaseRepo: BaseRepo[model.L36FashionOutfitRelatedWork]{
			DB: conn.GetDB(conn.ONLINE),
		},
	}
}

func GetFashionOutfitRelatedWorkRepo() *FashionOutfitRelatedWorkRepo {
	if config.C.OnlineMysql.Host != "" {
		return NewOnlineFashionOutfitRelatedWorkRepo()
	}
	return NewFashionOutfitRelatedWorkRepo()
}

func (r *FashionOutfitRelatedWorkRepo) FindByWorkId(ctx context.Context, workId int64) ([]model.L36FashionOutfitRelatedWork, error) {
	var relatedWorks []model.L36FashionOutfitRelatedWork
	if err := r.Scope(ctx).Where("workId = ?", workId).Find(&relatedWorks).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return relatedWorks, nil
		}
		return nil, err
	}
	return relatedWorks, nil
}

func (r *FashionOutfitRelatedWorkRepo) FindMapByWorkIds(ctx context.Context, workIds []int64) (map[int64][]model.L36FashionOutfitRelatedWork, error) {
	var relatedWorks []model.L36FashionOutfitRelatedWork
	relatedWorkMap := make(map[int64][]model.L36FashionOutfitRelatedWork)
	if err := r.Scope(ctx).Where("workId IN ?", workIds).Find(&relatedWorks).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return relatedWorkMap, nil
		}
		return nil, err
	}
	for _, relatedWork := range relatedWorks {
		if _, ok := relatedWorkMap[relatedWork.WorkID]; !ok {
			relatedWorkMap[relatedWork.WorkID] = []model.L36FashionOutfitRelatedWork{}
		}
		relatedWorkMap[relatedWork.WorkID] = append(relatedWorkMap[relatedWork.WorkID], relatedWork)
	}
	return relatedWorkMap, nil
}
