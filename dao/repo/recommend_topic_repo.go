package repo

import (
	"context"

	"app/conn"
	"app/dao/model"

	"gorm.io/gorm"
)

type RecommendTopicRepo struct {
	BaseRepo[model.L36ActionRecommendTopic]
}

func NewRecommendTopicRepo() *RecommendTopicRepo {
	return &RecommendTopicRepo{
		BaseRepo: BaseRepo[model.L36ActionRecommendTopic]{
			DB: conn.GetDB(),
		},
	}
}

func (r *RecommendTopicRepo) FindAll(ctx context.Context) ([]*model.L36ActionRecommendTopic, error) {
	var topics []*model.L36ActionRecommendTopic
	err := r.NormalScope(ctx).Order("weight desc").Find(&topics).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return topics, nil
}
