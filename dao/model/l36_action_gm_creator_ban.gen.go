// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionGmCreatorBan = "l36_action_gm_creator_ban"

// L36ActionGmCreatorBan mapped from table <l36_action_gm_creator_ban>
type L36ActionGmCreatorBan struct {
	UserID     int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`         // 用户id
	RoleID     int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`         // 角色id
	BanType    int8  `gorm:"column:ban_type;not null;comment:禁言类型" json:"ban_type"`       // 禁言类型
	ExpireTime int64 `gorm:"column:expire_time;not null;comment:过期时间" json:"expire_time"` // 过期时间
	IsDelete   int8  `gorm:"column:is_delete;not null;comment:是否删除" json:"is_delete"`     // 是否删除
	BaseModel  `json:""`
}

// TableName L36ActionGmCreatorBan's table name
func (*L36ActionGmCreatorBan) TableName() string {
	return TableNameL36ActionGmCreatorBan
}
