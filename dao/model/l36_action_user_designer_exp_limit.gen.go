// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionUserDesignerExpLimit = "l36_action_user_designer_exp_limit"

// L36ActionUserDesignerExpLimit mapped from table <l36_action_user_designer_exp_limit>
type L36ActionUserDesignerExpLimit struct {
	UserID        int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                // 用户id
	DesignerID    int64 `gorm:"column:designer_id;not null;comment:设计师id" json:"designer_id"`       // 设计师id
	WorkCount     int32 `gorm:"column:work_count;not null;comment:作品数" json:"work_count"`           // 作品数
	CommentCount  int32 `gorm:"column:comment_count;not null;comment:评论数" json:"comment_count"`     // 评论数
	LikeCount     int32 `gorm:"column:like_count;not null;comment:点赞数" json:"like_count"`           // 点赞数
	CollectCount  int32 `gorm:"column:collect_count;not null;comment:收藏数" json:"collect_count"`     // 收藏数
	ShareCount    int32 `gorm:"column:share_count;not null;comment:分享数" json:"share_count"`         // 分享数
	UseCount      int32 `gorm:"column:use_count;not null;comment:使用数" json:"use_count"`             // 使用数
	CompleteCount int32 `gorm:"column:complete_count;not null;comment:有效浏览数" json:"complete_count"` // 有效浏览数
	BaseModel     `json:""`
}

// TableName L36ActionUserDesignerExpLimit's table name
func (*L36ActionUserDesignerExpLimit) TableName() string {
	return TableNameL36ActionUserDesignerExpLimit
}
