// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkShare = "l36_action_work_share"

// L36ActionWorkShare mapped from table <l36_action_work_share>
type L36ActionWorkShare struct {
	WorkID     int64 `gorm:"column:work_id;not null;comment:作品id" json:"work_id"`               // 作品id
	UserID     int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`               // 用户id
	RoleID     int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`               // 角色id
	IsEffect   int8  `gorm:"column:is_effect;not null;comment:是否有效 0=无效 1=有效" json:"is_effect"` // 是否有效 0=无效 1=有效
	EffectTime int64 `gorm:"column:effect_time;not null;comment:生效时间" json:"effect_time"`       // 生效时间
	BaseModel  `json:""`
}

// TableName L36ActionWorkShare's table name
func (*L36ActionWorkShare) TableName() string {
	return TableNameL36ActionWorkShare
}
