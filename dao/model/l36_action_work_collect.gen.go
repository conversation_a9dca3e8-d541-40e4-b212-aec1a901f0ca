// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkCollect = "l36_action_work_collect"

// L36ActionWorkCollect mapped from table <l36_action_work_collect>
type L36ActionWorkCollect struct {
	UserID    int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`               // 用户id
	RoleID    int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`               // 角色id
	WorkID    int64 `gorm:"column:work_id;not null;comment:作品id" json:"work_id"`               // 作品id
	IsDelete  int8  `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"` // 删除状态 0=正常 1=删除
	BaseModel `json:""`
}

// TableName L36ActionWorkCollect's table name
func (*L36ActionWorkCollect) TableName() string {
	return TableNameL36ActionWorkCollect
}
