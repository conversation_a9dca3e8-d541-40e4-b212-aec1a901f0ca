// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionUser = "l36_action_user"

// L36ActionUser mapped from table <l36_action_user>
type L36ActionUser struct {
	Aid          string `gorm:"column:aid;not null;comment:计费id" json:"aid"`                         // 计费id
	RoleID       int64  `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`                 // 角色id
	Fans         int32  `gorm:"column:fans;not null;comment:粉丝数" json:"fans"`                        // 粉丝数
	Name         string `gorm:"column:name;not null;comment:名称" json:"name"`                         // 名称
	AvatarRoleID int64  `gorm:"column:avatar_role_id;not null;comment:头像角色id" json:"avatar_role_id"` // 头像角色id
	NameRoleID   int64  `gorm:"column:name_role_id;not null;comment:名称头像id" json:"name_role_id"`     // 名称头像id
	Exp          int32  `gorm:"column:exp;not null;comment:经验值" json:"exp"`                          // 经验值
	FansCount    int32  `gorm:"column:fans_count;not null;comment:粉丝数" json:"fans_count"`            // 粉丝数
	FollowCount  int32  `gorm:"column:follow_count;not null;comment:关注数" json:"follow_count"`        // 关注数
	WorkCount    int32  `gorm:"column:work_count;not null;comment:作品数" json:"work_count"`            // 作品数
	CollectCount int32  `gorm:"column:collect_count;not null;comment:收藏数" json:"collect_count"`      // 收藏数
	LikedCount   int32  `gorm:"column:liked_count;not null;comment:被点赞数" json:"liked_count"`         // 被点赞数
	BaseModel    `json:""`
}

// TableName L36ActionUser's table name
func (*L36ActionUser) TableName() string {
	return TableNameL36ActionUser
}
