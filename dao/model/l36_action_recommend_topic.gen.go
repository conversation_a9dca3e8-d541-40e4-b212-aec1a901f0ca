// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionRecommendTopic = "l36_action_recommend_topic"

// L36ActionRecommendTopic mapped from table <l36_action_recommend_topic>
type L36ActionRecommendTopic struct {
	TopicID   int64 `gorm:"column:topic_id;not null;comment:话题id" json:"topic_id"`             // 话题id
	IsDelete  int8  `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"` // 删除状态 0=正常 1=删除
	Weight    int32 `gorm:"column:weight;not null;comment:权重" json:"weight"`                   // 权重
	BaseModel `json:""`
}

// TableName L36ActionRecommendTopic's table name
func (*L36ActionRecommendTopic) TableName() string {
	return TableNameL36ActionRecommendTopic
}
