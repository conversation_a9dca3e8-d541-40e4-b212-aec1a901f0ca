// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkPlay = "l36_action_work_play"

// L36ActionWorkPlay mapped from table <l36_action_work_play>
type L36ActionWorkPlay struct {
	WorkID    int64 `gorm:"column:work_id;not null;comment:作品id" json:"work_id"` // 作品id
	UserID    int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"` // 用户id
	RoleID    int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"` // 角色id
	Date      int64 `gorm:"column:date;not null;comment:日期" json:"date"`         // 日期
	BaseModel `json:""`
}

// TableName L36ActionWorkPlay's table name
func (*L36ActionWorkPlay) TableName() string {
	return TableNameL36ActionWorkPlay
}
