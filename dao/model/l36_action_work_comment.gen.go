// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkComment = "l36_action_work_comment"

// L36ActionWorkComment mapped from table <l36_action_work_comment>
type L36ActionWorkComment struct {
	UserID          int64  `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                       // 用户id
	RoleID          int64  `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`                       // 角色id
	WorkID          int64  `gorm:"column:work_id;not null;comment:作品id" json:"work_id"`                       // 作品id
	OriginCommentID int64  `gorm:"column:origin_comment_id;not null;comment:原始评论id" json:"origin_comment_id"` // 原始评论id
	ReplyCommentID  int64  `gorm:"column:reply_comment_id;not null;comment:回复评论id" json:"reply_comment_id"`   // 回复评论id
	ReplyUserID     int64  `gorm:"column:reply_user_id;not null;comment:回复用户id" json:"reply_user_id"`         // 回复用户id
	ReplyRoleID     int64  `gorm:"column:reply_role_id;not null;comment:回复角色id" json:"reply_role_id"`         // 回复角色id
	Content         string `gorm:"column:content;not null;comment:评论内容" json:"content"`                       // 评论内容
	ReplyCount      int32  `gorm:"column:reply_count;not null;comment:回复数" json:"reply_count"`                // 回复数
	LikeCount       int32  `gorm:"column:like_count;not null;comment:点赞数" json:"like_count"`                  // 点赞数
	IsDelete        int8   `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"`         // 删除状态 0=正常 1=删除
	IsAddHot        int8   `gorm:"column:is_add_hot;not null;comment:是否加热度 0=否 1=是" json:"is_add_hot"`        // 是否加热度 0=否 1=是
	BaseModel       `json:""`
}

// TableName L36ActionWorkComment's table name
func (*L36ActionWorkComment) TableName() string {
	return TableNameL36ActionWorkComment
}
