// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionTopic = "l36_action_topic"

// L36ActionTopic mapped from table <l36_action_topic>
type L36ActionTopic struct {
	Name      string `gorm:"column:name;not null;comment:名称" json:"name"`                       // 名称
	IsDelete  int8   `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"` // 删除状态 0=正常 1=删除
	Hot       int32  `gorm:"column:hot;not null;comment:热度" json:"hot"`                         // 热度
	BaseModel `json:""`
}

// TableName L36ActionTopic's table name
func (*L36ActionTopic) TableName() string {
	return TableNameL36ActionTopic
}
