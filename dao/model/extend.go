package model

import (
	"app/config"
	"app/constant"
	"app/schema"
)

func (l *L36ActionWork) IsAuditPass() bool {
	return l.AuditStatus == int8(constant.FpReviewStatusPass)
}

var DefaultRoleInfo = &L36RoleInfo{
	RoleName:    "剧组导演",
	Avatar:      1,
	AvatarFrame: 1,
}

func (l *L36RoleInfo) GetAvatarInfo() schema.AvatarInfo {
	role := l
	if l == nil || role.RoleID == 0 {
		role = DefaultRoleInfo
	}
	return schema.AvatarInfo{
		RoleId:      role.RoleID,
		Job:         int8(role.Job),
		Gender:      int8(role.Gender),
		Avatar:      role.Avatar,
		AvatarFrame: role.AvatarFrame,
	}
}

func (l *L36RoleInfo) GetRoleName(user *L36ActionUser) string {
	if l != nil && l.RoleID != 0 {
		return l.RoleName
	}
	return DefaultRoleInfo.RoleName
}

func (l *L36ActionWork) IsShowForOther(userId int64) bool {
	if l.AuditStatus != int8(constant.FpReviewStatusPass) {
		return false
	}
	if l.Visibility == int8(constant.VisibilityPublic) {
		return true
	}
	if l.UserID == userId {
		return true
	}
	return false
}

func (w *L36ActionWork) IsPrivateWork() bool {
	return w.Visibility == int8(constant.VisibilityPrivate)
}

// DecodeContents 解码内容
func (w *L36ActionWork) DecodeContents() []int {
	contents := w.Contents
	result := []int{}
	for i := 1; i <= 5; i++ {
		if contents&(1<<(i-1)) != 0 {
			result = append(result, i)
		}
	}
	return result
}

// GetHot 获取热度, 临时处理因为客户端显示问题
func (w *L36ActionWork) GetHot() int {
	if config.C.Work.GameCompatible && w.Hot >= 1000 {
		return int(w.Hot * 10)
	}
	return int(w.Hot)
}

// GetLikeCount 获取点赞数, 临时处理因为客户端显示问题
func (w *L36ActionWork) GetLikeCount() int {
	likeCount := w.LikeCount + w.FakeLikeCount
	if config.C.Work.GameCompatible && likeCount >= 1000 {
		return int(likeCount * 10)
	}
	return int(likeCount)
}

// GetCollectCount 获取收藏数, 临时处理因为客户端显示问题
func (w *L36ActionWork) GetCollectCount() int {
	collectCount := w.CollectCount + w.FakeCollectCount
	if config.C.Work.GameCompatible && collectCount >= 1000 {
		return int(collectCount * 10)
	}
	return int(collectCount)
}

// GetCommentCount 获取评论数, 临时处理因为客户端显示问题
func (w *L36ActionWork) GetCommentCount() int {
	collectCount := w.CommentCount + w.FakeCommentCount
	if config.C.Work.GameCompatible && collectCount >= 1000 {
		return int(collectCount * 10)
	}
	return int(collectCount)
}

// GetLikedCount 获取点赞数, 临时处理因为客户端显示问题
func (u *L36ActionUser) GetLikedCount() int {
	likedCount := u.LikedCount
	if config.C.Work.GameCompatible && likedCount >= 1000 {
		return int(likedCount * 10)
	}
	return int(likedCount)
}
