// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionReviewMedium = "l36_action_review_media"

// L36ActionReviewMedium mapped from table <l36_action_review_media>
type L36ActionReviewMedium struct {
	WorkID      int64  `gorm:"column:work_id;not null;comment:作品id" json:"work_id"`           // 作品id
	FileID      string `gorm:"column:file_id;not null;comment:文件id" json:"file_id"`           // 文件id
	AuditStatus int8   `gorm:"column:audit_status;not null;comment:审核状态" json:"audit_status"` // 审核状态
	BaseModel   `json:""`
}

// TableName L36ActionReviewMedium's table name
func (*L36ActionReviewMedium) TableName() string {
	return TableNameL36ActionReviewMedium
}
