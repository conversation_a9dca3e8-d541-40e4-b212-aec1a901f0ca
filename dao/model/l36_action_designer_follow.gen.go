// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionDesignerFollow = "l36_action_designer_follow"

// L36ActionDesignerFollow mapped from table <l36_action_designer_follow>
type L36ActionDesignerFollow struct {
	UserID     int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`               // 用户id
	RoleID     int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`               // 角色id
	DesignerID int64 `gorm:"column:designer_id;not null;comment:设计师id" json:"designer_id"`      // 设计师id
	IsDelete   int8  `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"` // 删除状态 0=正常 1=删除
	BaseModel  `json:""`
}

// TableName L36ActionDesignerFollow's table name
func (*L36ActionDesignerFollow) TableName() string {
	return TableNameL36ActionDesignerFollow
}
