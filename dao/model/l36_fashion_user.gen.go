// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36FashionUser = "l36_fashion_user"

// L36FashionUser mapped from table <l36_fashion_user>
type L36FashionUser struct {
	Aid              string `gorm:"column:aid;not null;comment:账号Id" json:"aid"`                 // 账号Id
	Urs              string `gorm:"column:urs;not null;comment:用户角色" json:"urs"`                 // 用户角色
	RoleID           int64  `gorm:"column:roleId;not null;comment:角色Id" json:"roleId"`           // 角色Id
	Name             string `gorm:"column:name;not null;comment:名称" json:"name"`                 // 名称
	Description      string `gorm:"column:description;not null;comment:描述" json:"description"`   // 描述
	Level            int8   `gorm:"column:level;not null;default:1;comment:等级" json:"level"`     // 等级
	Avatar           string `gorm:"column:avatar;not null;comment:头像" json:"avatar"`             // 头像
	Fans             int32  `gorm:"column:fans;not null;comment:粉丝数" json:"fans"`                // 粉丝数
	IsDesigner       int8   `gorm:"column:isDesigner;not null;comment:是否为设计师" json:"isDesigner"` // 是否为设计师
	WorkUpdateTime   int64  `gorm:"column:workUpdateTime;not null" json:"workUpdateTime"`
	WorkSetName      string `gorm:"column:workSetName;not null;comment:作品集名称" json:"workSetName"`              // 作品集名称
	WorkSetLogo      string `gorm:"column:workSetLogo;not null;comment:作品集logo" json:"workSetLogo"`            // 作品集logo
	Exp              int32  `gorm:"column:exp;not null;comment:经验值" json:"exp"`                                // 经验值
	Hot              int32  `gorm:"column:hot;not null;comment:总热度" json:"hot"`                                // 总热度
	WeekHot          int32  `gorm:"column:weekHot;not null;comment:周热度" json:"weekHot"`                        // 周热度
	DayHot           int32  `gorm:"column:dayHot;not null;comment:日热度" json:"dayHot"`                          // 日热度
	WorkCollects     int32  `gorm:"column:workCollects;not null;comment:作品被收藏总数" json:"workCollects"`          // 作品被收藏总数
	WorkUnlocks      int32  `gorm:"column:workUnlocks;not null;comment:作品被解锁总数" json:"workUnlocks"`            // 作品被解锁总数
	WorkMaxCollects  int32  `gorm:"column:workMaxCollects;not null;comment:最大收藏数" json:"workMaxCollects"`      // 最大收藏数
	MaxCollectWorkID int64  `gorm:"column:maxCollectWorkId;not null;comment:最大收藏作品id" json:"maxCollectWorkId"` // 最大收藏作品id
	WorkMaxUnlocks   int32  `gorm:"column:workMaxUnlocks;not null;comment:最大解锁数" json:"workMaxUnlocks"`        // 最大解锁数
	MaxUnlockWorkID  int64  `gorm:"column:maxUnlockWorkId;not null;comment:最大解锁作品id" json:"maxUnlockWorkId"`   // 最大解锁作品id
	FanMaxUnlocks    int32  `gorm:"column:fanMaxUnlocks;not null;comment:最大粉丝解锁数" json:"fanMaxUnlocks"`        // 最大粉丝解锁数
	MaxUnlockFansID  int64  `gorm:"column:maxUnlockFansId;not null;comment:解锁数最多的粉丝id" json:"maxUnlockFansId"` // 解锁数最多的粉丝id
	TotalFans        int32  `gorm:"column:totalFans;not null;comment:累计粉丝数" json:"totalFans"`                  // 累计粉丝数
	TotalWorks       int32  `gorm:"column:totalWorks;not null;comment:累计作品数" json:"totalWorks"`                // 累计作品数
	WorkCount        int32  `gorm:"column:workCount;not null;comment:作品数" json:"workCount"`                    // 作品数
	ShareWorkCount   int32  `gorm:"column:shareWorkCount;not null;comment:分享作品数,不含曾用作品" json:"shareWorkCount"` // 分享作品数,不含曾用作品
	CreateTime       int64  `gorm:"column:createTime;not null;comment:创建时间" json:"createTime"`                 // 创建时间
	UpdateTime       int64  `gorm:"column:updateTime;not null;comment:更新时间" json:"updateTime"`                 // 更新时间
	ServerID         int32  `gorm:"column:serverId;not null;comment:服务器id" json:"serverId"`                    // 服务器id
	CollectLimit     int32  `gorm:"column:collectLimit;not null;comment:收藏上限" json:"collectLimit"`             // 收藏上限
	FashionHot       int32  `gorm:"column:fashionHot;not null;comment:时装热度" json:"fashionHot"`                 // 时装热度
	FashionWeekHot   int32  `gorm:"column:fashionWeekHot;not null;comment:时装周热度" json:"fashionWeekHot"`        // 时装周热度
	FashionDayHot    int32  `gorm:"column:fashionDayHot;not null;comment:时装日热度" json:"fashionDayHot"`          // 时装日热度
	ApplyTimes       int32  `gorm:"column:applyTimes;not null;comment:应用次数" json:"applyTimes"`                 // 应用次数
	Gender           int8   `gorm:"column:gender;not null;default:-1;comment: -1 01" json:"gender"`            //  -1 01
	IsBaned          int8   `gorm:"column:isBaned;not null;comment:是否被处罚" json:"isBaned"`                      // 是否被处罚
	BaseModel        `json:""`
}

// TableName L36FashionUser's table name
func (*L36FashionUser) TableName() string {
	return TableNameL36FashionUser
}
