// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWork = "l36_action_work"

// L36ActionWork mapped from table <l36_action_work>
type L36ActionWork struct {
	Type              int32  `gorm:"column:type;not null;comment:类型" json:"type"`                                   // 类型
	Name              string `gorm:"column:name;not null;comment:名称" json:"name"`                                   // 名称
	Summary           string `gorm:"column:summary;not null;comment:描述" json:"summary"`                             // 描述
	Cover             string `gorm:"column:cover;not null;comment:封面" json:"cover"`                                 // 封面
	Video             string `gorm:"column:video;not null;comment:视频" json:"video"`                                 // 视频
	Images            string `gorm:"column:images;not null;comment:图片" json:"images"`                               // 图片
	Property          string `gorm:"column:property;not null;comment:属性" json:"property"`                           // 属性
	RawProperty       string `gorm:"column:raw_property;not null;comment:客户端提交的属性url" json:"raw_property"`          // 客户端提交的属性url
	Contents          int32  `gorm:"column:contents;not null;comment:内容" json:"contents"`                           // 内容
	ResampleURL       string `gorm:"column:resample_url;not null;comment:重采样地址" json:"resample_url"`                // 重采样地址
	Scene             int64  `gorm:"column:scene;not null;comment:场景" json:"scene"`                                 // 场景
	Gender            int8   `gorm:"column:gender;not null;comment:性别 0=男 1=女" json:"gender"`                       // 性别 0=男 1=女
	Visibility        int8   `gorm:"column:visibility;not null;comment:可见性 1=所有人可见 2=仅自己可见" json:"visibility"`      // 可见性 1=所有人可见 2=仅自己可见
	IsDelete          int8   `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"`             // 删除状态 0=正常 1=删除
	UserID            int64  `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                           // 用户id
	RoleID            int64  `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`                           // 角色id
	Hot               int32  `gorm:"column:hot;not null;comment:热度" json:"hot"`                                     // 热度
	DayHot            int32  `gorm:"column:day_hot;not null;comment:日热度" json:"day_hot"`                            // 日热度
	WeekHot           int32  `gorm:"column:week_hot;not null;comment:周热度" json:"week_hot"`                          // 周热度
	LikeCount         int32  `gorm:"column:like_count;not null;comment:点赞数" json:"like_count"`                      // 点赞数
	CommentCount      int32  `gorm:"column:comment_count;not null;comment:评论数" json:"comment_count"`                // 评论数
	CollectCount      int32  `gorm:"column:collect_count;not null;comment:收藏数" json:"collect_count"`                // 收藏数
	UseCount          int32  `gorm:"column:use_count;not null;comment:使用数" json:"use_count"`                        // 使用数
	ShareCount        int32  `gorm:"column:share_count;not null;comment:分享数" json:"share_count"`                    // 分享数
	CompleteCount     int32  `gorm:"column:complete_count;not null;comment:有效浏览数" json:"complete_count"`            // 有效浏览数
	AuditStatus       int8   `gorm:"column:audit_status;not null;comment:审核状态" json:"audit_status"`                 // 审核状态
	AuditInfo         string `gorm:"column:audit_info;not null;default:0;comment:审核信息" json:"audit_info"`           // 审核信息
	LastDecayTime     int64  `gorm:"column:last_decay_time;not null;comment:上次衰减时间" json:"last_decay_time"`         // 上次衰减时间
	YesterdayDecayHot int32  `gorm:"column:yesterday_decay_hot;not null;comment:昨日衰减热度" json:"yesterday_decay_hot"` // 昨日衰减热度
	FakeLikeCount     int32  `gorm:"column:fake_like_count;not null;comment:假点赞数" json:"fake_like_count"`           // 假点赞数
	FakeCommentCount  int32  `gorm:"column:fake_comment_count;not null;comment:假评论数" json:"fake_comment_count"`     // 假评论数
	FakeCollectCount  int32  `gorm:"column:fake_collect_count;not null;comment:假收藏数" json:"fake_collect_count"`     // 假收藏数
	BaseModel         `json:""`
}

// TableName L36ActionWork's table name
func (*L36ActionWork) TableName() string {
	return TableNameL36ActionWork
}
