// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkUse = "l36_action_work_use"

// L36ActionWorkUse mapped from table <l36_action_work_use>
type L36ActionWorkUse struct {
	WorkID    int64 `gorm:"column:work_id;not null;comment:作品id" json:"work_id"` // 作品id
	UserID    int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"` // 用户id
	RoleID    int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"` // 角色id
	BaseModel `json:""`
}

// TableName L36ActionWorkUse's table name
func (*L36ActionWorkUse) TableName() string {
	return TableNameL36ActionWorkUse
}
