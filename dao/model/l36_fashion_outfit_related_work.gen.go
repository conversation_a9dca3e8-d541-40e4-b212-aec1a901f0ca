// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36FashionOutfitRelatedWork = "l36_fashion_outfit_related_work"

// L36FashionOutfitRelatedWork mapped from table <l36_fashion_outfit_related_work>
type L36FashionOutfitRelatedWork struct {
	WorkID        int64 `gorm:"column:workId;not null;comment:作品id" json:"workId"`               // 作品id
	RelatedWorkID int64 `gorm:"column:relatedWorkId;not null;comment:搭配id" json:"relatedWorkId"` // 搭配id
	CreateTime    int64 `gorm:"column:createTime;not null;comment:创建时间" json:"createTime"`       // 创建时间
	BaseModel     `json:""`
}

// TableName L36FashionOutfitRelatedWork's table name
func (*L36FashionOutfitRelatedWork) TableName() string {
	return TableNameL36FashionOutfitRelatedWork
}
