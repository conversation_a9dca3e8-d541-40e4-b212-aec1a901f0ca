// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkDelete = "l36_action_work_delete"

// L36ActionWorkDelete mapped from table <l36_action_work_delete>
type L36ActionWorkDelete struct {
	WorkID    int64 `gorm:"column:work_id;not null;comment:作品id" json:"work_id"` // 作品id
	BaseModel `json:""`
}

// TableName L36ActionWorkDelete's table name
func (*L36ActionWorkDelete) TableName() string {
	return TableNameL36ActionWorkDelete
}
