// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkTopic = "l36_action_work_topic"

// L36ActionWorkTopic mapped from table <l36_action_work_topic>
type L36ActionWorkTopic struct {
	WorkID    int64 `gorm:"column:work_id;not null;comment:作品id" json:"work_id"`   // 作品id
	TopicID   int64 `gorm:"column:topic_id;not null;comment:话题id" json:"topic_id"` // 话题id
	BaseModel `json:""`
}

// TableName L36ActionWorkTopic's table name
func (*L36ActionWorkTopic) TableName() string {
	return TableNameL36ActionWorkTopic
}
