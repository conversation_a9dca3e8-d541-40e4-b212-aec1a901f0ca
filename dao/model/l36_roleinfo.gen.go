// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36RoleInfo = "l36_roleInfo"

// L36RoleInfo mapped from table <l36_roleInfo>
type L36RoleInfo struct {
	RoleID                 int64   `gorm:"column:RoleId;primaryKey;comment:角色ID" json:"RoleId"` // 角色ID
	Level                  int32   `gorm:"column:Level;default:1;comment:角色等级" json:"Level"`    // 角色等级
	ServerID               int32   `gorm:"column:ServerId;comment:服务器ID" json:"ServerId"`       // 服务器ID
	RoleName               string  `gorm:"column:RoleName;comment:角色昵称" json:"RoleName"`        // 角色昵称
	Nickname               string  `gorm:"column:Nickname;comment:角色小名" json:"Nickname"`        // 角色小名
	Job                    int8    `gorm:"column:Job;default:1;comment:角色职业" json:"Job"`        // 角色职业
	Gender                 int8    `gorm:"column:Gender;comment:性别" json:"Gender"`              // 性别
	Avatar                 int32   `gorm:"column:Avatar;comment:头像" json:"Avatar"`              // 头像
	AvatarFrame            int32   `gorm:"column:AvatarFrame;comment:头像边框" json:"AvatarFrame"`  // 头像边框
	Location               string  `gorm:"column:Location;comment:城市" json:"Location"`          // 城市
	GangID                 int32   `gorm:"column:GangId;comment:帮会id" json:"GangId"`            // 帮会id
	Gang                   string  `gorm:"column:Gang;comment:帮会名称" json:"Gang"`                // 帮会名称
	Signature              string  `gorm:"column:Signature;comment:签名" json:"Signature"`        // 签名
	CreateTime             int64   `gorm:"column:CreateTime" json:"CreateTime"`
	UpdateTime             int64   `gorm:"column:UpdateTime" json:"UpdateTime"`
	RenQi                  int64   `gorm:"column:RenQi;not null;comment:人气" json:"RenQi"` // 人气
	Urs                    string  `gorm:"column:Urs;comment:网易通行证" json:"Urs"`           // 网易通行证
	Aid                    string  `gorm:"column:Aid" json:"Aid"`
	HpThemeID              int32   `gorm:"column:HpThemeId;comment:头像背景" json:"HpThemeId"`          // 头像背景
	IP                     string  `gorm:"column:Ip;comment:玩家ip地址" json:"Ip"`                      // 玩家ip地址
	ProvinceCode           string  `gorm:"column:ProvinceCode;comment:玩家省code" json:"ProvinceCode"` // 玩家省code
	Guildjob               string  `gorm:"column:Guildjob;comment:帮会职位" json:"Guildjob"`            // 帮会职位
	AccountID              string  `gorm:"column:AccountId" json:"AccountId"`
	LoginChannel           string  `gorm:"column:LoginChannel;not null;comment:登录渠道" json:"LoginChannel"`                                       // 登录渠道
	HomeHot                int64   `gorm:"column:HomeHot;not null;comment:主页热度" json:"HomeHot"`                                                 // 主页热度
	FlowerHeadIcon         int32   `gorm:"column:FlowerHeadIcon;not null;comment:动效头像" json:"FlowerHeadIcon"`                                   // 动效头像
	MomentLikeCount        int64   `gorm:"column:MomentLikeCount;not null;comment:动态收到的点赞数" json:"MomentLikeCount"`                             // 动态收到的点赞数
	MomentCommentCount     int64   `gorm:"column:MomentCommentCount;not null;comment:动态收到的评论数" json:"MomentCommentCount"`                       // 动态收到的评论数
	MomentForwardCount     int64   `gorm:"column:MomentForwardCount;not null;comment:动态收到的转发数" json:"MomentForwardCount"`                       // 动态收到的转发数
	CreditScore            int32   `gorm:"column:CreditScore;comment:信用分" json:"CreditScore"`                                                   // 信用分
	CheatAttribute         int32   `gorm:"column:CheatAttribute;comment:作弊属性" json:"CheatAttribute"`                                            // 作弊属性
	HomePageThemeToPyq     int8    `gorm:"column:HomePageThemeToPyq;comment:个人主页主题是否同步到朋友圈" json:"HomePageThemeToPyq"`                          // 个人主页主题是否同步到朋友圈
	PyqMomentIconID        int32   `gorm:"column:PyqMomentIconId;comment:朋友圈动态装饰id" json:"PyqMomentIconId"`                                     // 朋友圈动态装饰id
	PyqPageIconID          int32   `gorm:"column:PyqPageIconId;comment:朋友圈背景id" json:"PyqPageIconId"`                                           // 朋友圈背景id
	PyqThemeIconID         int32   `gorm:"column:PyqThemeIconId;comment:朋友圈主题装饰id" json:"PyqThemeIconId"`                                       // 朋友圈主题装饰id
	AvatarFrameDecorate    string  `gorm:"column:AvatarFrameDecorate;comment:头像框装饰" json:"AvatarFrameDecorate"`                                 // 头像框装饰
	FlowerCount            int32   `gorm:"column:FlowerCount;comment:鲜花数量" json:"FlowerCount"`                                                  // 鲜花数量
	LoginStatus            int8    `gorm:"column:LoginStatus;not null;comment:是否登录" json:"LoginStatus"`                                         // 是否登录
	BuLingHYDCurNumNoClean float64 `gorm:"column:BuLingHYDCurNumNoClean;not null;default:0.00;comment:补领活跃度之前数量" json:"BuLingHYDCurNumNoClean"` // 补领活跃度之前数量
	BuLingHYDCurNum        float64 `gorm:"column:BuLingHYDCurNum;not null;default:0.00;comment:补领活跃度当前数量" json:"BuLingHYDCurNum"`               // 补领活跃度当前数量
	BaseModel              `json:""`
}

// TableName L36RoleInfo's table name
func (*L36RoleInfo) TableName() string {
	return TableNameL36RoleInfo
}
