// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionTopTopic = "l36_action_top_topic"

// L36ActionTopTopic mapped from table <l36_action_top_topic>
type L36ActionTopTopic struct {
	TopicID   int64 `gorm:"column:topic_id;not null;comment:话题id" json:"topic_id"`             // 话题id
	Weight    int32 `gorm:"column:weight;not null;comment:权重" json:"weight"`                   // 权重
	StartTime int64 `gorm:"column:start_time;not null;comment:开始时间" json:"start_time"`         // 开始时间
	EndTime   int64 `gorm:"column:end_time;not null;comment:结束时间" json:"end_time"`             // 结束时间
	IsDelete  int8  `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"` // 删除状态 0=正常 1=删除
	BaseModel `json:""`
}

// TableName L36ActionTopTopic's table name
func (*L36ActionTopTopic) TableName() string {
	return TableNameL36ActionTopTopic
}
