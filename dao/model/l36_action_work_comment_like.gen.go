// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36ActionWorkCommentLike = "l36_action_work_comment_like"

// L36ActionWorkCommentLike mapped from table <l36_action_work_comment_like>
type L36ActionWorkCommentLike struct {
	UserID    int64 `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`               // 用户id
	RoleID    int64 `gorm:"column:role_id;not null;comment:角色id" json:"role_id"`               // 角色id
	CommentID int64 `gorm:"column:comment_id;not null;comment:评论id" json:"comment_id"`         // 评论id
	IsDelete  int8  `gorm:"column:is_delete;not null;comment:删除状态 0=正常 1=删除" json:"is_delete"` // 删除状态 0=正常 1=删除
	BaseModel `json:""`
}

// TableName L36ActionWorkCommentLike's table name
func (*L36ActionWorkCommentLike) TableName() string {
	return TableNameL36ActionWorkCommentLike
}
