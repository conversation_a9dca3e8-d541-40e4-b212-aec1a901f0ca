// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameL36FashionWork = "l36_fashion_work"

// L36FashionWork mapped from table <l36_fashion_work>
type L36FashionWork struct {
	Type            int8   `gorm:"column:type;not null;comment:类型" json:"type"`                                      // 类型
	Subtype         int8   `gorm:"column:subtype;not null;comment:子类型" json:"subtype"`                               // 子类型
	UserID          int64  `gorm:"column:userId;not null;comment:用户id" json:"userId"`                                // 用户id
	RoleID          int64  `gorm:"column:roleId;not null;comment:角色id" json:"roleId"`                                // 角色id
	Name            string `gorm:"column:name;not null;comment:名称" json:"name"`                                      // 名称
	Description     string `gorm:"column:description;not null;comment:描述" json:"description"`                        // 描述
	PreviewURL      string `gorm:"column:previewUrl;not null;comment:预览效果地址" json:"previewUrl"`                      // 预览效果地址
	VideoURL        string `gorm:"column:videoUrl;not null;comment:视频地址" json:"videoUrl"`                            // 视频地址
	VideoTemplateID int32  `gorm:"column:videoTemplateId;not null;default:-1;comment:视频模板id" json:"videoTemplateId"` // 视频模板id
	Property        string `gorm:"column:property;not null;comment:属性" json:"property"`                              // 属性
	Material        string `gorm:"column:material;not null;comment:解锁所需资源" json:"material"`                          // 解锁所需资源
	Hot             int32  `gorm:"column:hot;not null;comment:热度" json:"hot"`                                        // 热度
	Collects        int32  `gorm:"column:collects;not null;comment:收藏数" json:"collects"`                             // 收藏数
	Comments        int32  `gorm:"column:comments;not null;comment:评论数" json:"comments"`                             // 评论数
	Unlocks         int32  `gorm:"column:unlocks;not null;comment:解锁数" json:"unlocks"`                               // 解锁数
	CreateTime      int64  `gorm:"column:createTime;not null;comment:创建时间" json:"createTime"`                        // 创建时间
	UpdateTime      int64  `gorm:"column:updateTime;not null;comment:更新时间" json:"updateTime"`                        // 更新时间
	TopTime         int64  `gorm:"column:topTime;comment:置顶时间" json:"topTime"`                                       // 置顶时间
	Status          int8   `gorm:"column:status;not null;comment:状态" json:"status"`                                  // 状态
	WeekHot         int32  `gorm:"column:weekHot;not null;comment:周热度" json:"weekHot"`                               // 周热度
	DayHot          int32  `gorm:"column:dayHot;not null;comment:日热度" json:"dayHot"`                                 // 日热度
	IsFree          int8   `gorm:"column:isFree;not null;comment:是否免费" json:"isFree"`                                // 是否免费
	Views           int32  `gorm:"column:views;not null;comment:浏览量" json:"views"`                                   // 浏览量
	/*
		作品适配的性别 0男1女

	*/
	Gender          int8   `gorm:"column:gender;default:-1;comment:作品适配的性别 0男1女\n" json:"gender"`
	VideoPreviewURL string `gorm:"column:videoPreviewUrl;not null;comment:视频预览地址" json:"videoPreviewUrl"`                   // 视频预览地址
	VideoDuration   int32  `gorm:"column:videoDuration;not null;default:-1;comment:视频长度ms" json:"videoDuration"`            // 视频长度ms
	Visibility      int8   `gorm:"column:visibility;not null;comment:可见性 0公开 1私密" json:"visibility"`                        // 可见性 0公开 1私密
	PublicTime      int64  `gorm:"column:publicTime;not null;comment:公开时间" json:"publicTime"`                               // 公开时间
	IsUsed          int8   `gorm:"column:isUsed;not null;comment:是否为曾用作品" json:"isUsed"`                                    // 是否为曾用作品
	AuditExtra      string `gorm:"column:auditExtra;not null;comment:审核额外信息" json:"auditExtra"`                             // 审核额外信息
	AuditStatus     int8   `gorm:"column:auditStatus;not null;comment:审核状态  0=未审核 1=审核通过 2=审核不通过" json:"auditStatus"`       // 审核状态  0=未审核 1=审核通过 2=审核不通过
	VideoCover      string `gorm:"column:videoCover;not null;comment:视频封面" json:"videoCover"`                               // 视频封面
	Qrcode          string `gorm:"column:qrcode;not null;comment:二维码" json:"qrcode"`                                        // 二维码
	FashionID       int32  `gorm:"column:fashionId;not null;default:-1;comment:时装id" json:"fashionId"`                      // 时装id
	HotUpdateTime   int64  `gorm:"column:hotUpdateTime;not null;default:1692621765379;comment:热度更新时间" json:"hotUpdateTime"` // 热度更新时间
	MatchID         string `gorm:"column:matchId;not null;comment:穿搭id" json:"matchId"`                                     // 穿搭id
	SearchStatus    int8   `gorm:"column:searchStatus;not null;comment:搜索状态 0=已索引 -1=已删除" json:"searchStatus"`              // 搜索状态 0=已索引 -1=已删除
	ServerID        int32  `gorm:"column:serverId;not null;comment:服务器id" json:"serverId"`                                  // 服务器id
	PlaceLabelID    int32  `gorm:"column:placeLabelId;not null;comment:场景标签id" json:"placeLabelId"`                         // 场景标签id
	WeatherLabelID  int32  `gorm:"column:weatherLabelId;not null;comment:天气标签id" json:"weatherLabelId"`                     // 天气标签id
	TimeLabelID     int32  `gorm:"column:timeLabelId;not null;comment:时间标签id" json:"timeLabelId"`                           // 时间标签id
	StyleLabelIds   int64  `gorm:"column:styleLabelIds;not null;comment:风格标签ids" json:"styleLabelIds"`                      // 风格标签ids
	Layout          int8   `gorm:"column:layout;not null;comment:布局" json:"layout"`                                         // 布局
	CostType        int8   `gorm:"column:costType;not null;comment:耗材类型" json:"costType"`                                   // 耗材类型
	IsMainMatch     int8   `gorm:"column:isMainMatch;not null;comment:是否为主穿搭" json:"isMainMatch"`                           // 是否为主穿搭
	PatternLabelID  int64  `gorm:"column:patternLabelId;not null;comment:模式标签id" json:"patternLabelId"`                     // 模式标签id
	IsMain          int8   `gorm:"column:isMain;not null;comment:是否为主作品" json:"isMain"`                                     // 是否为主作品
	ApplyTimes      int32  `gorm:"column:applyTimes;not null;comment:被应用次数" json:"applyTimes"`                              // 被应用次数
	PaddingComments int32  `gorm:"column:paddingComments;comment:注水评论数" json:"paddingComments"`                             // 注水评论数
	PaddingCollects int32  `gorm:"column:paddingCollects;comment:注水评论数" json:"paddingCollects"`                             // 注水评论数
	ColorIds        int64  `gorm:"column:colorIds;not null;comment:颜色ids" json:"colorIds"`                                  // 颜色ids
	Clip            int8   `gorm:"column:clip;not null;comment:是否裁剪" json:"clip"`                                           // 是否裁剪
	IsGmHide        int8   `gorm:"column:isGmHide;not null;comment:是否为GM隐藏作品" json:"isGmHide"`                              // 是否为GM隐藏作品
	BaseModel       `json:""`
}

// TableName L36FashionWork's table name
func (*L36FashionWork) TableName() string {
	return TableNameL36FashionWork
}
