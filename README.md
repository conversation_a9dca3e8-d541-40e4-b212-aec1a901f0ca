# L36 剧本站相关接口

## 项目结构
```
.
├── api/            # API 层：HTTP/gRPC 接口定义和处理器
├── cmd/            # 应用程序入口点
├── conf/           # 配置文件目录
├── config/         # 配置代码：配置结构体和加载逻辑
├── conn/           # 外部连接管理：数据库、缓存等连接池
├── constant/       # 常量定义
├── consumer/       # 消息消费者
├── dao/            # 数据访问层
│   ├── cache/      # 缓存实现
│   ├── es/         # ElasticSearch 实现
│   ├── model/      # 数据库模型（由 gorm 自动生成）
│   └── repo/       # 常用查询实现
├── database/       # 数据库相关文件
├── design/         # 设计文档
├── docs/           # Swagger API 文档
├── errors/         # 错误定义和错误处理
├── external/       # 外部服务集成
├── gen/            # 代码生成工具
├── logs/           # 日志文件目录
├── middleware/     # HTTP/gRPC 中间件
├── models/         # 领域模型
├── router/         # 路由配置
├── schema/         # 数据结构和验证
├── service/        # 业务逻辑层
└── util/           # 工具函数
    ├── crypto/     # 加密相关
    ├── lock/       # 分布式锁
    ├── log/        # 日志工具
    └── wait/       # 等待和超时控制
```

## 开发指南

### API 文档生成
```bash
# 安装 swag 工具
go install github.com/swaggo/swag/cmd/swag@latest

# 生成 API 文档
swag init
```
注意：记得修改配置文件的 BasePath
接口文档地址：http://localhost:9992/swagger/index.html#/auth/post_auth_login

### 数据库模型生成
```bash
# 使用 GORM 生成数据库模型
go run gen/gorm/gen.go
```

本项目使用 [GORM](https://gorm.io/) 作为 ORM 框架，主要特点：
- 支持多种数据库（MySQL、PostgreSQL、SQLite、SQL Server）
- 支持事务、关联、钩子、预加载等特性
- 提供丰富的查询构建器
- 支持自动迁移和模型生成
- 内置软删除、时间戳等常用功能

### 依赖外部接口
1. crew_website
   - 地址：https://l36-crew-website.apps-sl.danlu.netease.com/redoc#tag/audio

## 项目规范

### 代码组织
- 遵循 Clean Architecture 原则，将代码分为接口层、业务逻辑层和数据访问层
- 使用依赖注入模式，通过接口解耦各层之间的依赖
- 保持代码简洁，每个函数只做一件事
- 使用 context 进行上下文传递和超时控制

### 错误处理
- 使用自定义错误类型，便于错误追踪和处理
- 错误信息应该清晰明确，包含必要的上下文信息
- 在日志中记录详细的错误信息

### 日志规范
- 使用结构化日志
- 包含请求 ID、用户信息等上下文
- 合理使用日志级别（DEBUG、INFO、WARN、ERROR）

### 测试规范
- 编写单元测试，保持测试覆盖率
- 使用表驱动测试方式
- 模拟外部依赖，确保测试的独立性

### 性能考虑
- 合理使用缓存
- 优化数据库查询
- 使用连接池管理外部连接
- 实现优雅关闭机制
``` 

### swag 文档
``` bash
go install github.com/swaggo/swag/cmd/swag@latest
swag init
```
记得修改配置文件的BasePath
接口文档地址
http://localhost:9992/action-station/swagger/index.html#/auth/post_auth_login

### go orm 生成 model
``` bash
go run gen/gorm/gen.go
```

### 依赖外部接口
1. crew_website
https://l36-crew-website.apps-sl.danlu.netease.com/redoc#tag/audio