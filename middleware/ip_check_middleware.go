package middleware

import (
	"app/config"

	"github.com/gin-gonic/gin"
)

// CheckIp 检查IP中间件
func CheckIp(ctx *gin.Context) {
	config := config.C.IpCheck
	if !config.Enable {
		ctx.Next()
		return
	}
	ip := ctx.ClientIP()
	for _, v := range config.WhiteList {
		if v == ip {
			ctx.Next()
			return
		}
	}
	ctx.JSON(403, gin.H{
		"code": 403,
		"msg":  "ip not in white list",
	})
	ctx.Abort()
}

// CheckGmIp 检查GM IP中间件
func CheckGmIp(ctx *gin.Context) {
	config := config.C.IpCheck
	if !config.Enable {
		ctx.Next()
		return
	}
	ip := ctx.ClientIP()
	for _, v := range config.GmWhiteList {
		if v == ip {
			ctx.Next()
			return
		}
	}
	ctx.JSON(403, gin.H{
		"code": 403,
		"msg":  "ip not in gm white list",
	})
	ctx.Abort()
}
