package middleware

import (
	"strconv"
	"time"

	"app/api"
	"app/conn"
	"app/errors"
	"app/util"
	"app/util/lock"

	"github.com/gin-gonic/gin"
)

// Lock 用户级别接口分布式锁中间件，
// 此中间件依赖请求上下文中的role_id，一般需注册在LoginCheck中间件之后，
// 尝试（非阻塞）获取用户级别接口分布式锁，
// 获取不成功将直接向调用方响应对应标准错误。
func Lock(ctx *gin.Context) {
	// 初始化锁获取器
	url := ctx.Request.URL.Path
	key := "lu:" + strconv.FormatInt(util.GetRoleIdFromCtx(ctx), 10) + ":" + url
	locker := lock.NewOperationLock(conn.GetRedisConn(), key)
	// 尝试获得锁
	ok, err := locker.Lock(ctx, time.Second*10)
	if nil != err {
		api.JsonReturnError(ctx, errors.New(errors.ServerError))
		ctx.Abort()
		return
	}
	if !ok {
		api.JsonReturnError(ctx, errors.New(errors.OperationTooFrequency))
		ctx.Abort()
		return
	}

	// 继续handlers chain
	ctx.Next()

	// 释放锁
	locker.Unlock(ctx)
}
