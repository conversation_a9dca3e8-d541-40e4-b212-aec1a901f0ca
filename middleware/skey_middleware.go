package middleware

import (
	"time"

	"app/api"
	"app/config"
	"app/errors"
	"app/schema"
	"app/service"
	"app/util"

	"github.com/gin-gonic/gin"
)

var skeyService = service.NewSkeyService()

func checkSkey(params schema.UserBase, skey string, conf *config.Conf) *errors.Err {
	skeyClaims, err2 := skeyService.ParseSkey(skey)
	if err2 != nil {
		return errors.New(errors.InvalidSkey)
	}
	if skeyClaims.RoleId != params.RoleId {
		return errors.New(errors.RoleIdMisMatch)
	}
	if skeyClaims.UserId > 0 && skeyClaims.UserId != params.UserId {
		return errors.New(errors.RoleIdMisMatch)
	}
	// 非测试环境需要校验过期时间
	if !conf.Test.TestEnv {
		minValidTime := time.Now().Unix() - int64(conf.Skey.Expire*60*60)
		if skeyClaims.Time < minValidTime {
			return errors.New(errors.SkeyExpired)
		}
	}
	return nil
}

// CheckSkey 检查skey是否合法
func CheckSkey(ctx *gin.Context) {
	conf := config.C

	var params schema.UserBase
	skey := ctx.GetHeader("skey")
	if err := util.Bind(ctx, &params); err != nil {
		api.JsonReturnValidateError(ctx, err)
		ctx.Abort()
		return
	}

	err := checkSkey(params, skey, conf)
	if err != nil {
		if conf.Skey.Enable && !(conf.Test.TestEnv && skey == conf.Skey.TestSkey) {
			api.JsonReturnError(ctx, err)
			ctx.Abort()
			return
		}
	}

	util.SetCtxRoleId(ctx, params.RoleId)
	ctx.Next()
}
