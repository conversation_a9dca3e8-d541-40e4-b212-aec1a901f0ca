package middleware

import (
	"app/api"
	"app/config"
	"app/errors"
	"app/util"
	"app/util/yidun"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type CheckParams struct {
	Content string `json:"content"`
	Name    string `json:"name"`
	Summary string `json:"summary"`
}

func YidunMiddleware(ctx *gin.Context) {
	if config.C.YiDun.Disable {
		return
	}
	var params CheckParams
	if err := ctx.ShouldBindBodyWith(&params, binding.JSON); err != nil {
		api.JsonReturnError(ctx, errors.New(errors.InvalidParams))
		ctx.Abort()
		return
	}
	if params.Content != "" {
		ret := yidun.TextCheckSignalWithLRuCache(ctx, yidun.TextCheckSignalParams{
			Content: params.Content,
			DataId:  util.RandString(10),
		})
		if ret == yidun.CheckSuggestionFailed {
			if !config.C.YiDun.ApiErrorAsPass {
				api.JsonReturnError(ctx, errors.New(errors.ServerError))
				ctx.Abort()
				return
			}
		}
		if ret == yidun.CheckSuggestionSuspected && !config.C.YiDun.SuspicionAsPass || ret == yidun.CheckSuggestionBlock {
			api.JsonReturnError(ctx, errors.New(errors.ReviewWordError))
			ctx.Abort()
			return
		}

	}
	if params.Name != "" {
		ret := yidun.TextCheckSignalWithLRuCache(ctx, yidun.TextCheckSignalParams{
			Content: params.Name,
			DataId:  util.RandString(10),
		})
		if ret == yidun.CheckSuggestionFailed {
			if !config.C.YiDun.ApiErrorAsPass {
				api.JsonReturnError(ctx, errors.New(errors.ServerError))
				ctx.Abort()
				return
			}
		}
		if ret == yidun.CheckSuggestionSuspected && !config.C.YiDun.SuspicionAsPass || ret == yidun.CheckSuggestionBlock {
			api.JsonReturnError(ctx, errors.New(errors.ReviewWordError))
			ctx.Abort()
			return
		}
	}
	if params.Summary != "" {
		ret := yidun.TextCheckSignalWithLRuCache(ctx, yidun.TextCheckSignalParams{
			Content: params.Summary,
			DataId:  util.RandString(10),
		})
		if ret == yidun.CheckSuggestionFailed {
			if !config.C.YiDun.ApiErrorAsPass {
				api.JsonReturnError(ctx, errors.New(errors.ServerError))
				ctx.Abort()
				return
			}
		}
		if ret == yidun.CheckSuggestionSuspected && !config.C.YiDun.SuspicionAsPass || ret == yidun.CheckSuggestionBlock {
			api.JsonReturnError(ctx, errors.New(errors.ReviewWordError))
			ctx.Abort()
			return
		}
	}
}
