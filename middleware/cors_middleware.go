package middleware

import (
	"net/http"
	"slices"

	"app/config"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		originHeader := c.Request.Header.Get("Origin")
		if originHeader != "" && slices.Contains(config.C.Biz.CrossDomainOrigin, originHeader) {
			c.<PERSON>("Access-Control-Allow-Origin", originHeader)
			c.<PERSON><PERSON>("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
			c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization")
			c.<PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
			c.<PERSON>("Access-Control-Allow-Credentials", "true")
		}
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		c.Next()
	}
}

func Cors2() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowAllOrigins:  false,
		AllowOrigins:     config.C.Biz.CrossDomainOrigin,
		AllowMethods:     []string{"GET", "POST", "OPTIONS", "PUT", "DELETE", "UPDATE"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "Accept"},
		AllowCredentials: true,
		ExposeHeaders:    []string{"Content-Length", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Cache-Control", "Content-Language", "Content-Type"},
		AllowWildcard:    true,
	})
}
