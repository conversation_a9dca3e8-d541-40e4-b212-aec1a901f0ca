package config

import (
	"path"
	"runtime"
	"sync"

	"github.com/BurntSushi/toml"
)

var conf Conf

var once = sync.Once{}

func LoadConfig() (Conf, error) {
	once.Do(func() {
		_, currentFile, _, _ := runtime.Caller(0)
		configFile := path.Join(currentFile, "../../conf/config.toml")

		if _, err := toml.DecodeFile(configFile, &conf); err != nil {
			panic("Failed to decode config file: " + err.Error())
		}

		C = &conf
	})
	return conf, nil
}

func LoadConfigMust() Conf {
	conf, err := LoadConfig()
	if err != nil {
		panic(err)
	}
	return conf
}
