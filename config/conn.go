package config

// MySQLConfig 数据库配置
type MySQLConfig struct {
	Host            string // 默认127.0.0.1
	Port            string // 默认3306
	DBName          string
	Username        string
	Password        string
	MaxOpen         int // 最大可打开连接数，上限后并发进来新的DB请求将阻塞等待其他连接空闲释放，默认500
	MaxIdle         int // 最大空闲连接数，上限后并发进来新的DB请求将新创建一次性连接承接，直至连接数到达MaxOpen限制，默认200
	ConnMaxLifetime int // 连接最大存活生命周期，单位：分钟，默认5
	LogLevel        int `default:"2"`
	SlowThreshold   int // 慢查询阈值，单位：秒，默认200
	Debug           bool
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr      string // 默认127.0.0.1:6379
	Password  string
	DB        int // 默认0
	MaxActive int // 最大连接数，默认0
	IsTLS     int // tls开关，0-关闭；1-打开，默认0
	Prefix    string
	Debug     bool
}

// HttpClientConfig HTTP Client配置
type HttpClientConfig struct {
	Timeout             int // 请求超时时限，单位：毫秒，默认5000
	MaxIdleConns        int // 最大空闲连接，默认0
	MaxIdleConnsPerHost int // 单Host最大空闲连接，默认200
	MaxConnsPerHost     int // 单Host最大打开连接，默认500
	IdleConnTimeout     int // 连接最大空闲生命周期，单位：分钟，默认5
}
