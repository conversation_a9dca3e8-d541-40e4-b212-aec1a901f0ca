package config

import (
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/openapi"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/z/popo"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/z/pusher"
)

var C = &Conf{
	App: AppConfig{
		HttpListen: ":9992",
	},
	Gin: GinConfig{
		MaxMultipartMemory: 8,
		Pprof:              0,
		Log: GinLogConfig{
			LogPath:        "./logs/",
			AccessFileName: "access.log",
			ErrorFileName:  "error.log",
			ReserveDays:    30,
			ReserveCount:   300,
			MaxSize:        1024,
		},
	},
	Log: LogConfig{
		LogPath:      "./logs/",
		FileName:     "app.log",
		ReserveDays:  30,
		ReserveCount: 300,
		MaxSize:      1024,
		Level:        elog.LvINFO,
	},
	Mysql: MySQLConfig{
		Host:            "127.0.0.1",
		Port:            "3306",
		MaxOpen:         500,
		MaxIdle:         200,
		ConnMaxLifetime: 5,
		LogLevel:        2,
	},
	Redis: RedisConfig{
		Addr: "127.0.0.1:6379",
	},
	HttpClient: HttpClientConfig{
		Timeout:             5000,
		MaxIdleConnsPerHost: 200,
		MaxConnsPerHost:     500,
		IdleConnTimeout:     5,
	},
	Popo: popo.Config{
		Timeout: 5000,
	},
	Server: ServerConfig{
		Host:     "localhost:9992",
		BasePath: "/",
	},
	EnvSdk: EnvSdkCfg{},
}

// Conf 项目完整配置
type Conf struct {
	App              AppConfig
	Gin              GinConfig
	Log              LogConfig
	Mysql            MySQLConfig
	SlaveMysql       MySQLConfig
	OnlineMysql      MySQLConfig
	Redis            RedisConfig
	HttpClient       HttpClientConfig
	Popo             popo.Config
	Biz              BizConfig
	HttpCode         HttpCodeConfig
	Server           ServerConfig
	Skey             SkeyConfig
	Test             TestCfg
	EnvSdk           EnvSdkCfg
	Filepicker       FilepickerCfg
	IpCheck          IpCheckCfg
	Pusher           pusher.Config
	Work             WorkCfg
	Fuxi             FuxiCfg
	Es               EsCfg
	UserLevel        UserLevelCfg
	FashionRecommend FashionRecommendCfg
	Kafka            KafkaCfg
	YiDun            YiDunCfg
	Inform           InformCfg
	CrewWebsite      CrewWebsiteCfg
}

// AppConfig 应用配置
type AppConfig struct {
	AppName    string   // 应用名称，同时影响日志的基础app字段属性
	HttpListen string   // Http Server监听地址，默认:9992
	RpcListen  string   // Rpc Server监听地址（暂时无用）
	Language   string   // 应用语言（已废弃），错误消息请通过HttpCodeConfig实现
	Maintainer []string // 服务维护者，作用于POPO自动告警时的at人员列表（此为临时方案，后将跟随告警策略调整而消亡）
}

// DocConfig OpenAPI(swagger)文档配置
type DocConfig struct {
	Title         string           // 项目Title
	Summary       string           // 项目Summary
	Description   string           // 项目Description
	Version       string           // 当前版本
	GroupKeyStart int              // 接口分组路由开始段
	GroupKeyEnd   int              // 接口分组路由结束段
	Servers       []openapi.Server // 项目Servers列表
}

// GinConfig Gin框架相关开发配置
type GinConfig struct {
	MaxMultipartMemory int // 具体见gin.Engine.MaxMultipartMemory，单位：MB，默认8
	Pprof              int // Gin pprof开关，0-关；1-打开，默认0
	Log                GinLogConfig
}

// GinLogConfig Gin日志配置
type GinLogConfig struct {
	LogPath        string // Gin日志路径，默认./logs
	AccessFileName string // Gin access日志文件名，默认access.log
	ErrorFileName  string // Gin error日志文件名，默认error.log
	ReserveDays    int    // 日志保留天数，默认30
	ReserveCount   int    // 日志保留数量，默认300
	MaxSize        int    // 触发日志切割大小，单位：MB，默认1024
	Schema         string // 伏羲采集指定Schema，根据项目自行修改
}

// LogConfig 应用日志配置
type LogConfig struct {
	LogPath        string     // 日志路径，默认./logs
	FileName       string     // 日志文件，默认app.log
	ReserveDays    int        // 日志保留天数，默认30
	ReserveCount   int        // 日志保留数量，默认300
	MaxSize        int        // 触发日志切割大小，单位：MB，默认1024
	Level          elog.Level // 日志等级，默认(elog.LvINFO)4
	YunyingLogPath string     // 运营日志路径，默认./logs
	GlobalFields   struct {
		Schema string // 伏羲采集指定Schema，根据项目自行修改
	}
}

// HttpCodeConfig HTTP业务状态码信息配置列表
type HttpCodeConfig map[string]string

// ServerConfig Swagger文档服务配置
type ServerConfig struct {
	Host     string
	BasePath string
	Prefix   string
}

// SkeyConfig Skey配置
type SkeyConfig struct {
	// 检查token是否合法的Secret
	TokenSecret string
	// Skey 本身的Secret
	JwtSecret string
	// 是否开启Skey校验
	Enable bool `default:"true"`
	// 是否开启Token校验
	EnableToken bool `default:"true"`
	// 测试环境下的Skey
	TestSkey string `default:"skey_in_test"`
	// Skey 过期时间，单位小时
	Expire int64 `default:"24"`
}

// TestCfg 测试环境配置
type TestCfg struct {
	// 是否处于测试环境
	TestEnv bool
	// 是否开启请求日志
	RequestLog bool
	// 是否开启Pprof
	Pprof bool
}

// EsConfig  es配置
type EsCfg struct {
	Address  string
	User     string
	Password string
	Suffix   string
}

// EnvSdkConfig  envsdk配置
type EnvSdkCfg struct {
	Disable bool `default:"false"`
	Host    string
	Timeout int
	Channel string `default:"default"`
	Level   int    `default:"1"`
}

// FilepickerCfg  filepicker配置
type FilepickerCfg struct {
	Project   string
	SecretKey string
	DevMode   bool
	Region    string
	Review    int
	Policy    *struct {
		FsizeLimit [2]int
		MimeLimit  []string
	}
	TokenExpire int64
	BasePath    string
	SecretToken string
	SkipAudit   bool
}

// IpCheckCfg  ip检查配置
type IpCheckCfg struct {
	Enable      bool `default:"true"`
	WhiteList   []string
	GmWhiteList []string
}

// WorkCfg 作品配置
type WorkCfg struct {
	// 每人发布作品上限
	CreateLimit int
	// 每人收藏上限
	CollectLimit int
	// 发布作品经验值
	CreateExp int
	// 发布作品最大生效次数
	EffectCreateLimit int
	// 评论经验值
	CommentExp int
	// 评论热度
	CommentHot int
	// 每人对同个作者所有作品有效评论上限
	EffectCommentLimit int
	// 点赞经验值
	LikeExp int
	// 点赞热度
	LikeHot int
	// 每人对同个作者所有作品有效点赞上限
	EffectLikeLimit int
	// 收藏经验值
	CollectExp int
	// 收藏热度
	CollectHot int
	// 每人对同个作者所有作品有效收藏上限
	EffectCollectLimit int
	// 有效浏览经验值
	CompletePlayExp int
	// 有效浏览热度
	CompletePlayHot int
	// 每人对同个作者所有作品有效浏览上限
	EffectCompletePlayLimit int
	// 翻拍热度
	UseHot int
	// 翻拍经验值
	UseExp int
	// 每人每日单个作品有效翻拍上限
	DailyEffectUseLimit int
	// 每人对同个作者所有作品有效翻拍上限
	EffectUseLimit int
	// 有效分享热度
	ShareHot int
	// 有效分享经验值
	ShareExp int
	// 每人对同个作者所有作品有效分享上限
	EffectShareLimit int
	// 热度衰减系数
	HotDecay float64
	// 是否给游戏兼容展示数据
	GameCompatible bool
	// 是否开启处理属性url
	ProcessRawProperty bool
	// 是否开启删除文件
	DeleteFile bool
}

type UserLevelExpCfg struct {
	Level  int
	MinExp int
	MaxExp int
}

type UserLevelCfg struct {
	Exp      []UserLevelExpCfg
	MaxLevel int
}

// 推荐接口配置
type FuxiCfg struct {
	// 推荐接口地址
	Host string
	// 不启用
	Disable bool
	// 保底推荐数量
	BackupCount int
	// FuxiQps
	Qps int
}

// 时装推荐配置
type FashionRecommendCfg struct {
	// 推荐接口地址
	Host string
}

type KafkaCfg struct {
	Topic    string
	Address  string
	Group    string
	User     string
	Password string
}

type YiDunCfg struct {
	Disable         bool
	BusinessId      string
	SecretId        string
	SecretKey       string
	SuspicionAsPass bool // 疑似违规是否处理成通过
	ApiErrorAsPass  bool // 调用接口出错是是否处理成通过
}

// InformCfg 通知配置
type InformCfg struct {
	// 通知接口地址
	Host string
	// 通知接口密钥
	TokenSecret string
}

// CrewWebsiteCfg crew website配置
type CrewWebsiteCfg struct {
	// 接口地址
	Host string
	// Router
	Router string
	// HMAC 用户名
	HMACUser string
	// HMAC 密钥
	HMACSecret string
}
