# 应用配置
[App]
AppName = "demo" # 应用名称，同时影响日志的基础app字段属性
HttpListen = ":9992" # Http Server监听地址，默认:9992
Maintainer = ["<EMAIL>"] # 服务维护者，作用于POPO自动告警时的at人员列表（此为临时方案，后将跟随告警策略调整而消亡）

# OpenAPI(swagger)文档配置-项目Servers列表
[[Doc.Servers]]
Url = "https://localhost:9992"
Description = "本地环境"

# Gin日志配置
[Gin.Log]
LogPath = "./logs" # Gin日志路径，默认./logs
Schema = "com.netease.leihuo.ccc.base.model.tables.v1.GwSitesAllLog" # 伏羲采集指定Schema，根据项目自行修改

# 应用日志配置
[Log]
LogPath = "./logs" # 日志路径，默认./logs
GlobalFields.Schema = "com.netease.leihuo.ccc.base.model.tables.v1.GwSitesAllLog" # 伏羲采集指定Schema，根据项目自行修改

# 数据库配置
[Mysql]
Host = "127.0.0.1" # 默认127.0.0.1
Port = "3306" # 默认3306
DBName = "gw"
Username = "gw_user"
Password = "gw_pass"

# Redis配置
[Redis]
Addr = "127.0.0.1:6379" # 默认127.0.0.1:6379
Password = ""
Prefix = ""

## POPO配置
[Popo]
Url = "https://lhpp-popo-server.apps-hp.danlu.netease.com/popo/popo/msg/send"
Salt = "lOMvNtTgjrT7WJrmXOknYA=="
Project = "test" # 项目key，比如yjwj
Biz = "test" # 业务key，比如card-2024
Env = "test" # 环境标识，可选项：preview|test|release

# 业务配置
[Biz]
# 默认跨域中间件跨域Domain列表，需先route中打开跨域中间件方能生效
CrossDomainOrigin = [
    # 本地/测试等环境通配，正式环境必须去除这些
    "https://test.163.com*",
    "https://test.nie.163.com*",
    "https://test.yjwujian.cn*",
    # 生产环境需配置精准域名列表
    "https://xxx.163.com",
]

[Server]
Host = "http://localhost:9992"
BasePath = "/nshm/action-station-server"