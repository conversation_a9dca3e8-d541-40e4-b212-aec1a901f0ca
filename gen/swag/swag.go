package main

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"

	"app/errors"
)

func replaceDescription(filepath string, placeholder string, description string) {
	file, err := os.Open(filepath)
	if err != nil {
		panic(err)
	}
	defer file.Close()
	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		panic(err)
	}
	// 替换文件内容
	newContent := strings.Replace(string(content), placeholder, description, -1)
	// 写入文件
	err = os.WriteFile(filepath, []byte(newContent), 0o777)
	if err != nil {
		panic(err)
	}
}

func main() {
	cmd := exec.Command("swag", "init")
	output, err := cmd.CombinedOutput()
	if err != nil {
		panic(err)
	}
	fmt.Println(string(output))
	// 修改 docs 目录下的 Description
	placeholder := "接口文档"
	rawDescription := `"` + placeholder + `"`
	errsMarkdown := errors.ToMarkdown("errors/code.go")
	replaceDescription("docs/docs.go", rawDescription, "`"+placeholder+"\n"+errsMarkdown+"`")
	fmt.Println("generate swag docs success")
}
