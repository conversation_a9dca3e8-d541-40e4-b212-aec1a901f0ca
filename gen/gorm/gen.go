package main

import (
	"fmt"
	"os"

	"app/config"
	"app/conn"
	"app/dao/model"

	"github.com/BurntSushi/toml"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func bootstrap() *config.Conf {
	// 加载并解析配置
	cfgFile := "conf/config.toml"
	if _, err := toml.DecodeFile(cfgFile, config.C); err != nil {
		fmt.Fprintln(os.Stderr, "Failed to decode config file", err)
		os.Exit(1)
	}
	fmt.Println("start with config file : ", cfgFile)
	return config.C
}

func main() {
	conf := bootstrap()
	conn.ConnectDB1(&conf.Mysql)
	g := gen.NewGenerator(gen.Config{
		OutPath: "./dao/query",
		Mode:    gen.WithDefaultQuery,
	})
	m := map[string]func(detailType gorm.ColumnType) (dataType string){
		"tinyint": func(detailType gorm.ColumnType) (dataType string) { return "int8" },
	}
	g.WithDataTypeMap(m)

	db := conn.GetDB()
	g.UseDB(db)

	tables := []string{
		"l36_roleInfo",
		"l36_action_work",
		"l36_action_user",
		"l36_action_designer_follow",
		"l36_action_work_like",
		"l36_action_work_collect",
		"l36_action_work_comment",
		"l36_action_work_comment_like",
		"l36_action_review_media",
		"l36_action_topic",
		"l36_action_work_topic",
		"l36_action_work_use",
		"l36_action_work_play",
		"l36_action_work_share",
		"l36_action_user_designer_exp_limit",
		"l36_fashion_work",
		"l36_fashion_user",
		"l36_action_work_delete",
		"l36_action_recommend_topic",
		"l36_action_gm_creator_ban",
		"l36_fashion_outfit_related_work",
		"l36_action_top_topic",
	}

	fmt.Println("init tables", tables)

	for _, table := range tables {
		_ = g.GenerateModel(table,
			gen.FieldIgnore("id", "ctime", "utime"),
			gen.FieldRelateModel(field.BelongsTo, "", model.BaseModel{},
				&field.RelateConfig{
					//RelateSlice: true,
					//GORMTag: map[string][]string{
					//	"": {"-"},
					//},
				}),
		)
	}

	g.Execute()
}
