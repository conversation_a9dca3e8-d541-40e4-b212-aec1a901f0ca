module app

go 1.23

toolchain go1.23.8

require (
	ccc-gitlab.leihuo.netease.com/pkgo/envsdk-tools v0.0.0-20240709122220-90f51e78d07a
	ccc-gitlab.leihuo.netease.com/pkgo/filepicker-tools v0.0.0-20250208100024-0e0a6a74eccf
	ccc-gitlab.leihuo.netease.com/pkgo/golib/v2 v2.17.1
	ccc-gitlab.leihuo.netease.com/pkgo/yunying-log v0.0.0-20240511103408-bbde149f03cb
	github.com/BurntSushi/toml v1.3.2
	github.com/bytedance/sonic v1.12.6
	github.com/elastic/go-elasticsearch/v8 v8.14.0
	github.com/facebookgo/grace v0.0.0-20180706040059-75cf19382434
	github.com/gin-contrib/cors v1.7.2
	github.com/gin-contrib/pprof v1.4.0
	github.com/gin-gonic/gin v1.10.0
	github.com/go-playground/validator v9.31.0+incompatible
	github.com/golang-jwt/jwt v3.2.2+incompatible
	github.com/redis/go-redis/v9 v9.7.0
	github.com/robfig/cron/v3 v3.0.1
	github.com/sirupsen/logrus v1.9.3
	github.com/spf13/cobra v1.8.0
	github.com/stretchr/testify v1.10.0
	github.com/swaggo/files v1.0.1
	github.com/swaggo/gin-swagger v1.6.0
	github.com/swaggo/swag v1.16.3
	github.com/xuri/excelize/v2 v2.9.0
	github.com/yidun/yidun-golang-sdk v1.0.23
	golang.org/x/exp v0.0.0-20240808152545-0cdaa3abc0fa
	golang.org/x/sync v0.11.0
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
	gorm.io/driver/mysql v1.5.0
	gorm.io/gen v0.3.26
	gorm.io/gorm v1.25.9
)

require (
	github.com/agiledragon/gomonkey/v2 v2.12.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/klauspost/compress v1.17.2 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.15 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.**************-5d4384ee4fb2 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.4 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/xuri/efp v0.0.0-**************-9ad904a10d6d // indirect
	github.com/xuri/nfp v0.0.0-**************-ab9948c2c4a7 // indirect
)

require (
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/PuerkitoBio/purell v1.1.1 // indirect
	github.com/PuerkitoBio/urlesc v0.0.0-**************-de5bf2ad4578 // indirect
	github.com/araddon/dateparse v0.0.0-**************-6b43995a97de
	github.com/bytedance/sonic/loader v0.2.1 // indirect
	github.com/cch123/supermonkey v1.0.1
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-**************-9f7001d12a5f // indirect
	github.com/elastic/elastic-transport-go/v8 v8.6.0 // indirect
	github.com/facebookgo/clock v0.0.0-**************-600d898af40a // indirect
	github.com/facebookgo/ensure v0.0.0-**************-63f1cf65ac4c // indirect
	github.com/facebookgo/freeport v0.0.0-**************-d4adf43b75b9 // indirect
	github.com/facebookgo/httpdown v0.0.0-**************-5979d39b15c2 // indirect
	github.com/facebookgo/stack v0.0.0-**************-************ // indirect
	github.com/facebookgo/stats v0.0.0-20151006221625-1b76add642e4 // indirect
	github.com/facebookgo/subset v0.0.0-20200203212716-c811ad88dec4 // indirect
	github.com/gabriel-vasile/mimetype v1.4.7 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-openapi/jsonpointer v0.19.5 // indirect
	github.com/go-openapi/jsonreference v0.19.6 // indirect
	github.com/go-openapi/spec v0.20.4 // indirect
	github.com/go-openapi/swag v0.19.15 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.23.0
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/goccy/go-json v0.10.4 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/cpuid/v2 v2.2.9 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mailru/easyjson v0.7.6 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/panjf2000/ants/v2 v2.11.3
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/samber/lo v1.47.0
	github.com/segmentio/kafka-go v0.4.47
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	go.opentelemetry.io/otel v1.24.0 // indirect
	go.opentelemetry.io/otel/metric v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	golang.org/x/arch v0.12.0 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/mod v0.20.0 // indirect
	golang.org/x/net v0.33.0 // indirect
	golang.org/x/sys v0.29.0 // indirect
	golang.org/x/text v0.21.0
	golang.org/x/tools v0.24.0 // indirect
	google.golang.org/protobuf v1.36.1 // indirect
	gopkg.in/go-playground/assert.v1 v1.2.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/datatypes v1.1.1-0.20230130040222-c43177d3cf8c // indirect
	gorm.io/hints v1.1.0 // indirect
	gorm.io/plugin/dbresolver v1.5.0 // indirect
)
