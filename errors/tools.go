package errors

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path"
	"sort"
	"strconv"
	"strings"
)

type ErrorDict struct {
	Name string
	Code int64
	Msg  map[string]string
}

// ParseErrors 解析错误文件
func ParseErrors(filePath string) []ErrorDict {
	errorMap := make([]ErrorDict, 0)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		panic(err)
	}

	reader, err := os.ReadFile(filePath)
	if err != nil {
		panic(err)
	}

	fset := token.NewFileSet() // positions are relative to fset
	f, err := parser.ParseFile(fset, "", string(reader), parser.ParseComments)
	if err != nil {
		panic(err)
	}

	for _, i := range f.Decls {
		decl, ok := i.(*ast.GenDecl)
		if !ok {
			continue
		}

		for _, s := range decl.Specs {
			var code int
			var name string
			if spec, ok := s.(*ast.ValueSpec); ok {
				// 判断是否为const常量
				if spec.Names[0].Obj.Kind != ast.Con {
					continue
				}

				name = spec.Names[0].Name
				// 获取常量的值并转换为int值
				if lit, ok2 := spec.Values[0].(*ast.BasicLit); ok2 {
					if v, err := strconv.Atoi(lit.Value); err == nil {
						code = v
					}
				} else if lit, ok2 := spec.Values[0].(*ast.UnaryExpr); ok2 && lit.Op == token.SUB {
					if lit2, ok3 := lit.X.(*ast.BasicLit); ok3 {
						if v, err := strconv.Atoi(lit2.Value); err == nil {
							code = -v
						}
					}
				}
				dict := ErrorDict{
					Code: int64(code),
					Name: name,
				}
				// 获取注释
				if spec.Doc != nil {
					dict.parseComments(spec.Doc.List)
				} else if decl.Doc != nil {
					dict.parseComments(decl.Doc.List)
				}
				errorMap = append(errorMap, dict)
			}
		}
	}

	return errorMap
}

func (d *ErrorDict) parseComments(comments []*ast.Comment) {
	dmsg := make(map[string]string)
	for _, c := range comments {
		msg := strings.TrimSpace(c.Text[2:])
		if msg[0:1] == "@" {
			msgs := strings.Split(msg, " ")
			lang := msgs[0][1:]
			langlen := len(lang) + 1
			dmsg[lang] = strings.TrimSpace(msg[langlen:])
			//if k == 0 {
			//	dmsg["default"] = dmsg[lang]
			//}
		} else {
			// dmsg["default"] = msg
		}
	}
	d.Msg = dmsg
}

// 写入message文件
func WriteMsgFile(dicts []ErrorDict, file string) {
	str := toErrorsGoFileStr(dicts)
	writeStrToFile(str, file)
}

// 写入文档
func WriteMDFile(dicts []ErrorDict, file string) {
	str := toMDFileStr(dicts)
	writeStrToFile(str, file)
}

func toMDFileStr(m []ErrorDict) string {
	str := "### 错误码\n\n"
	str += "| 错误码 | 说明 |\n"
	str += "| --- | --- |\n"
	for _, k := range m {
		if k.Msg == nil {
			str += fmt.Sprintf("| %d | %s |\n", k.Code, "未设置")
		} else {
			msg := k.Msg["default"]
			if m, ok := k.Msg["zh-cn"]; ok {
				msg = m
			}
			str += fmt.Sprintf("| %d | %s |\n", k.Code, msg)
		}
	}
	return str
}

func toErrorsGoFileStr(m []ErrorDict) string {
	// 生成默认的
	str := "package errors \n\n"
	str += "var MsgFlags = map[int64]map[string]string{\n"
	for _, k := range m {
		if k.Msg == nil {
			str += k.Name + " : nil,\n"
		} else {
			str += "\t" + k.Name + " : {\n"
			// 对 mk 进行排序
			var keys []string
			for mk := range k.Msg {
				keys = append(keys, mk)
			}
			sort.Strings(keys)
			for _, mk := range keys {
				mv := k.Msg[mk]
				str += fmt.Sprintf("\t\t\"%s\" : \"%s\",\n", mk, mv)
			}
			str += "\t},\n"
		}
	}
	str += "}"
	return str
}

// 写入文件内容
func writeStrToFile(str string, filePath string) error {
	fp := path.Dir(filePath)
	if _, err := os.Stat(fp); os.IsNotExist(err) {
		os.Mkdir(fp, 0o777)
		os.Chmod(fp, 0o777)
	}
	var f *os.File
	var err error
	os.Remove(filePath)
	f, err = os.Create(filePath)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(str)
	return err
}

// ToMarkdown 生成markdown文档
func ToMarkdown(path string) string {
	errs := ParseErrors(path)
	return toMDFileStr(errs)
}
