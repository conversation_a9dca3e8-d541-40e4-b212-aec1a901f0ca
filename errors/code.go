//go:generate stringer -type=Pill
package errors

// OK 成功
// @en-us success
// @zh-cn 成功
const OK = 0

// 系统错误码
const (
	// ServerError 服务器错误
	// @en-us server error
	// @zh-cn 服务器错误
	ServerError = -1

	// InvalidSkey 连接失败，请稍后再试
	// @en-us connection failed, please try again later
	// @zh-cn 连接失败，请稍后再试
	InvalidSkey = -2

	// RoleIdMisMatch skey玩家信息不匹配
	// @en-us player information does not match
	// @zh-cn 玩家信息不匹配
	RoleIdMisMatch = -3

	// SkeyExpired 连接错误，请稍后再试
	// @en-us connection error, please try again later
	// @zh-cn 连接错误，请稍后再试
	SkeyExpired = -5

	// TokenError Token非法
	// @en-us Token error
	// @zh-cn Token非法
	TokenError = -6

	// InvalidParams 参数错误
	// @en-us Parameter error
	// @zh-cn 参数错误
	InvalidParams = -7
	// NoPermission 请找超级管理员开通权限
	// @en-us Please contact the super administrator
	// @zh-cn 请找超级管理员开通权限
	NoPermission = -8

	// OperationTooFrequency 操作过于频繁,请稍后再试
	// @en-us Operation too frequency
	// @zh-cn 操作过于频繁,请稍后再试
	OperationTooFrequency = -10

	// OpenIdNotLogin OpenId未登录
	// @en-us OpenId not login
	// @zh-cn OpenId未登录
	OpenIdNotLogin = -11
	// NotLogin 未登录
	// @en-us not login
	// @zh-cn 未登录
	NotLogin = -12
)

// 业务错误码
const (
	// RoleIdNotEmpty 角色Id不能为空
	// @en-us role not allow empty
	// @zh-cn 角色Id不能为空
	RoleIdNotEmpty = -1000

	// RoleNotExist 角色不存在
	// @en-us role not exist
	// @zh-cn 角色不存在
	RoleNotExist = -1003

	// InvalidLink 无效的媒体
	// @en-us invalid link
	// @zh-cn 无效的链接
	InvalidLink = -1004

	// UserNotExist 用户不存在
	// @en-us user not exist
	// @zh-cn 用户不存在
	UserNotExist = -1005

	// WorkNotExist 作品不存在
	// @en-us work not exist
	// @zh-cn 作品不存在
	WorkNotExist = -1006

	// WorkNoAuth 暂无权限操作作品
	// @en-us no permission to operate work
	// @zh-cn 暂无权限操作作品
	WorkNoAuth = -1007

	// WorkLikeExist 作品已点赞
	// @en-us work has been liked
	// @zh-cn 作品已点赞
	WorkLikeExist = -1008

	// WorkLikeNotExist 作品未点赞
	// @en-us work has not been liked
	// @zh-cn 作品未点赞
	WorkLikeNotExist = -1009

	// WorkCollectExist 作品已收藏
	// @en-us work has been collected
	// @zh-cn 作品已收藏
	WorkCollectExist = -1010

	// WorkCollectNotExist 作品未收藏
	// @en-us work has not been collected
	// @zh-cn 作品未收藏
	WorkCollectNotExist = -1011

	// UserCollectLimit 用户收藏作品数量达到上限
	// @en-us user collect work limit
	// @zh-cn 用户收藏作品数量达到上限
	UserCollectLimit = -1012

	// InvalidWorkName 作品名称不合法
	// @en-us invalid work name
	// @zh-cn 作品名称不合法
	InvalidWorkName = -1013

	// InvalidWorkSummary 作品描述不合法
	// @en-us invalid work summary
	// @zh-cn 作品描述不合法
	InvalidWorkSummary = -1014

	// StringIsEmptyCodeErr 请输入内容
	// @en-us Please enter content
	// @zh-cn 请输入内容
	StringIsEmptyCodeErr = -1015

	// CommentTextTooLong 评论内容过长
	// @en-us Comment text is too long
	// @zh-cn 评论内容过长
	CommentTextTooLong = -1016

	// InvalidCommentText 评论内容不合法
	// @en-us Invalid comment content
	// @zh-cn 评论内容不合法
	InvalidCommentText = -1017

	// ReplyNotExist 回复不存在
	// @en-us Reply does not exist
	// @zh-cn 回复不存在
	ReplyNotExist = -1018

	// CommentNotExist 评论不存在
	// @en-us Comment does not exist
	// @zh-cn 评论不存在
	CommentNotExist = -1019

	// NoCommentAuth 无评论权限
	// @en-us No comment permission
	// @zh-cn 无评论权限
	NoCommentAuth = -1020

	// CommentLikeExist 评论已点赞
	// @en-us Comment has been liked
	// @zh-cn 评论已点赞
	CommentLikeExist = -1021

	// CommentLikeNotExist 评论未点赞
	// @en-us Comment has not been liked
	// @zh-cn 评论未点赞
	CommentLikeNotExist = -1022

	// CannotFollowSelf
	// @en-us Cannot follow yourself
	// @zh-cn 不能关注自己
	CannotFollowSelf = -1023

	// DesignerNotExist 设计师不存在
	// @en-us Designer does not exist
	// @zh-cn 设计师不存在
	DesignerNotExist = -1024

	// DesignerNotFollow 设计师未关注
	// @en-us Designer not followed
	// @zh-cn 设计师未关注
	DesignerNotFollow = -1025

	// WorkIsPrivate 作品已设置为私密
	// @en-us Work is set to private
	// @zh-cn 作品已设置为私密
	WorkIsPrivate = -1026

	// WorkShareNotExist 作品分享不存在
	// @en-us Work share does not exist
	// @zh-cn 作品分享不存在
	WorkShareNotExist = -1027

	// InvalidAvatarRoleId 头像角色ID不合法
	// @en-us Invalid avatar role ID
	// @zh-cn 头像角色ID不合法
	InvalidAvatarRoleId = -1028

	// WorkCreateLimit 用户创建作品数量达到上限
	// @en-us User create work limit
	// @zh-cn 用户创建作品数量达到上限
	WorkCreateLimit = -1029

	// WorkAuditNotPass 作品审核尚未通过
	// @en-us Work audit not pass
	// @zh-cn 作品审核尚未通过
	WorkAuditNotPass = -1030

	// InvalidNameRoleId 昵称角色ID不合法
	// @en-us Invalid name role ID
	// @zh-cn 昵称角色ID不合法
	InvalidNameRoleId = -1031

	// VideoAndImagesIsEmpty 视频和图片不能同时为空
	// @en-us Video and images cannot be empty
	// @zh-cn 视频和图片不能同时为空
	VideoAndImagesIsEmpty = -1032

	// LikeNotExist 您已取消点赞
	// @en-us You have already canceled the like
	// @zh-cn 您已取消点赞
	LikeNotExist = -1033

	// WorkNotDelete 作品未删除
	// @en-us Work is not deleted
	// @zh-cn 作品未删除
	WorkNotDelete = -1034

	// CreatorBannedWork 已被禁止发布作品
	// @en-us Creator is banned to publish works
	// @zh-cn 已被禁止发布作品
	CreatorBannedWork = -1035

	// CreatorBannedComment 已被禁止发布评论
	// @en-us Creator is banned to publish comments
	// @zh-cn 创作者被禁止发布评论
	CreatorBannedComment = -1036

	// ReviewWordError 发布内容含有敏感信息, 请检查后重试
	// @en-us The content contains sensitive information, please check and try again
	// @zh-cn 发布内容含有敏感信息, 请检查后重试
	ReviewWordError = -1037

	// CloneFileError 克隆文件失败
	// @en-us Clone file failed
	// @zh-cn 克隆文件失败
	CloneFileError = -1038

	// PropertyIsEmpty 属性不能为空
	// @en-us Property cannot be empty
	// @zh-cn 属性不能为空
	PropertyIsEmpty = -1039

	// ResampleUrlIsEmpty 重采样Url不能为空
	// @en-us Resample url cannot be empty
	// @zh-cn 重采样Url不能为空
	ResampleUrlIsEmpty = -1040
)
