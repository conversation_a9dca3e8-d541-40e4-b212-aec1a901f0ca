package errors

import (
	"encoding/json"
)

var language = "zh-cn"

// SetLanguage 设置错误语言
func SetLanguage(lang string) {
	language = lang
}

// Err 统一错误
type Err struct {
	ErrCode    int64  `json:"code"`
	ErrMessage string `json:"message"`
}

// New 创建新的错误
func New(code int64) *Err {
	return &Err{
		ErrCode:    code,
		ErrMessage: GetMessage(code),
	}
}

func (e *Err) Error() string {
	err, _ := json.Marshal(e)
	return string(err)
}

// GetMessage 获取错误信息
func GetMessage(code int64) string {
	msg, ok := MsgFlags[code]
	if ok {
		if lanMsg, ok := msg[language]; ok {
			return lanMsg
		}
		//if lanMsg, ok := msg[language]; ok {
		//	return lanMsg
		//}
	}
	return MsgFlags[ServerError][language]
}
