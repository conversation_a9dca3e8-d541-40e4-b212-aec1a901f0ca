package errors

var MsgFlags = map[int64]map[string]string{
	OK: {
		"en-us": "success",
		"zh-cn": "成功",
	},
	ServerError: {
		"en-us": "server error",
		"zh-cn": "服务器错误",
	},
	InvalidSkey: {
		"en-us": "connection failed, please try again later",
		"zh-cn": "连接失败，请稍后再试",
	},
	RoleIdMisMatch: {
		"en-us": "player information does not match",
		"zh-cn": "玩家信息不匹配",
	},
	SkeyExpired: {
		"en-us": "connection error, please try again later",
		"zh-cn": "连接错误，请稍后再试",
	},
	TokenError: {
		"en-us": "Token error",
		"zh-cn": "Token非法",
	},
	InvalidParams: {
		"en-us": "Parameter error",
		"zh-cn": "参数错误",
	},
	NoPermission: {
		"en-us": "Please contact the super administrator",
		"zh-cn": "请找超级管理员开通权限",
	},
	OperationTooFrequency: {
		"en-us": "Operation too frequency",
		"zh-cn": "操作过于频繁,请稍后再试",
	},
	OpenIdNotLogin: {
		"en-us": "OpenId not login",
		"zh-cn": "OpenId未登录",
	},
	NotLogin: {
		"en-us": "not login",
		"zh-cn": "未登录",
	},
	RoleIdNotEmpty: {
		"en-us": "role not allow empty",
		"zh-cn": "角色Id不能为空",
	},
	RoleNotExist: {
		"en-us": "role not exist",
		"zh-cn": "角色不存在",
	},
	InvalidLink: {
		"en-us": "invalid link",
		"zh-cn": "无效的链接",
	},
	UserNotExist: {
		"en-us": "user not exist",
		"zh-cn": "用户不存在",
	},
	WorkNotExist: {
		"en-us": "work not exist",
		"zh-cn": "作品不存在",
	},
	WorkNoAuth: {
		"en-us": "no permission to operate work",
		"zh-cn": "暂无权限操作作品",
	},
	WorkLikeExist: {
		"en-us": "work has been liked",
		"zh-cn": "作品已点赞",
	},
	WorkLikeNotExist: {
		"en-us": "work has not been liked",
		"zh-cn": "作品未点赞",
	},
	WorkCollectExist: {
		"en-us": "work has been collected",
		"zh-cn": "作品已收藏",
	},
	WorkCollectNotExist: {
		"en-us": "work has not been collected",
		"zh-cn": "作品未收藏",
	},
	UserCollectLimit: {
		"en-us": "user collect work limit",
		"zh-cn": "用户收藏作品数量达到上限",
	},
	InvalidWorkName: {
		"en-us": "invalid work name",
		"zh-cn": "作品名称不合法",
	},
	InvalidWorkSummary: {
		"en-us": "invalid work summary",
		"zh-cn": "作品描述不合法",
	},
	StringIsEmptyCodeErr: {
		"en-us": "Please enter content",
		"zh-cn": "请输入内容",
	},
	CommentTextTooLong: {
		"en-us": "Comment text is too long",
		"zh-cn": "评论内容过长",
	},
	InvalidCommentText: {
		"en-us": "Invalid comment content",
		"zh-cn": "评论内容不合法",
	},
	ReplyNotExist: {
		"en-us": "Reply does not exist",
		"zh-cn": "回复不存在",
	},
	CommentNotExist: {
		"en-us": "Comment does not exist",
		"zh-cn": "评论不存在",
	},
	NoCommentAuth: {
		"en-us": "No comment permission",
		"zh-cn": "无评论权限",
	},
	CommentLikeExist: {
		"en-us": "Comment has been liked",
		"zh-cn": "评论已点赞",
	},
	CommentLikeNotExist: {
		"en-us": "Comment has not been liked",
		"zh-cn": "评论未点赞",
	},
	CannotFollowSelf: {
		"en-us": "Cannot follow yourself",
		"zh-cn": "不能关注自己",
	},
	DesignerNotExist: {
		"en-us": "Designer does not exist",
		"zh-cn": "设计师不存在",
	},
	DesignerNotFollow: {
		"en-us": "Designer not followed",
		"zh-cn": "设计师未关注",
	},
	WorkIsPrivate: {
		"en-us": "Work is set to private",
		"zh-cn": "作品已设置为私密",
	},
	WorkShareNotExist: {
		"en-us": "Work share does not exist",
		"zh-cn": "作品分享不存在",
	},
	InvalidAvatarRoleId: {
		"en-us": "Invalid avatar role ID",
		"zh-cn": "头像角色ID不合法",
	},
	WorkCreateLimit: {
		"en-us": "User create work limit",
		"zh-cn": "用户创建作品数量达到上限",
	},
	WorkAuditNotPass: {
		"en-us": "Work audit not pass",
		"zh-cn": "作品审核尚未通过",
	},
	InvalidNameRoleId: {
		"en-us": "Invalid name role ID",
		"zh-cn": "昵称角色ID不合法",
	},
	VideoAndImagesIsEmpty: {
		"en-us": "Video and images cannot be empty",
		"zh-cn": "视频和图片不能同时为空",
	},
	LikeNotExist: {
		"en-us": "You have already canceled the like",
		"zh-cn": "您已取消点赞",
	},
	WorkNotDelete: {
		"en-us": "Work is not deleted",
		"zh-cn": "作品未删除",
	},
	CreatorBannedWork: {
		"en-us": "Creator is banned to publish works",
		"zh-cn": "已被禁止发布作品",
	},
	CreatorBannedComment: {
		"en-us": "Creator is banned to publish comments",
		"zh-cn": "创作者被禁止发布评论",
	},
	ReviewWordError: {
		"en-us": "The content contains sensitive information, please check and try again",
		"zh-cn": "发布内容含有敏感信息, 请检查后重试",
	},
	CloneFileError: {
		"en-us": "Clone file failed",
		"zh-cn": "克隆文件失败",
	},
	PropertyIsEmpty: {
		"en-us": "Property cannot be empty",
		"zh-cn": "属性不能为空",
	},
	ResampleUrlIsEmpty: {
		"en-us": "Resample url cannot be empty",
		"zh-cn": "重采样Url不能为空",
	},
}
