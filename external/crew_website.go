package external

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"app/config"
	"app/conn"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
)

// AudioNormalizeReq 音频标准化请求参数
type AudioNormalizeReq struct {
	URL     string `json:"url"`     // 语音URL
	UID     string `json:"uid"`     // 用户标识
	Expires *int   `json:"expires"` // 克隆文件的过期时间，单位为秒，默认不过期
}

// AudioNormalizeResp 音频标准化响应
type AudioNormalizeResp struct {
	AudioURL string `json:"audio_url"` // 语音URL
}

// FilePickerCloneReq 文件克隆请求参数
type FilePickerCloneReq struct {
	URL     string `json:"url"`     // 源文件URL
	UID     string `json:"uid"`     // 源文件的用户标识
	Expires *int   `json:"expires"` // 克隆文件的过期时间，单位为秒，默认不过期
}

// FilePickerCloneResp 文件克隆响应
type FilePickerCloneResp struct {
	CloneURL string `json:"clone_url"` // 克隆文件URL
}

// FilePickerDeleteReq 文件删除请求参数
type FilePickerDeleteReq struct {
	URL string `json:"url"` // 废弃文件URL
}

// makeHMACSign 生成 HMAC 签名
func makeHMACSign(hmacUser, secret, host, url string, body interface{}) (string, string, string, error) {
	// 获取当前 UTC 时间
	dateGMT := time.Now().UTC().Format("Mon, 02 Jan 2006 15:04:05 GMT")

	// 计算 body 的 SHA-256 摘要
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return "", "", "", fmt.Errorf("marshal body failed: %v", err)
	}
	bodyDigest := fmt.Sprintf("SHA-256=%s", base64.StdEncoding.EncodeToString(sha256.New().Sum(bodyBytes)))

	// 构建签名字符串
	method := "POST"
	protocol := "HTTP/1.1"
	requestLine := fmt.Sprintf("%s %s %s", method, url, protocol)
	signingStr := fmt.Sprintf("date: %s\nhost: %s\ndigest: %s\n%s", dateGMT, host, bodyDigest, requestLine)

	// 计算 HMAC 签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signingStr))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	// 构建 Authorization 头
	authorization := fmt.Sprintf(`hmac username="%s", algorithm="hmac-sha256", headers="date host digest request-line", signature="%s"`,
		hmacUser, signature)

	// 添加调试日志
	elog.WithFields(elog.Fields{
		"signature":     signature,
		"authorization": authorization,
	}).Debug("HMAC signature details")

	return dateGMT, bodyDigest, authorization, nil
}

// postRequest 发送带 HMAC 鉴权的请求
func postRequest(ctx context.Context, path string, body interface{}, resp interface{}) error {
	host := config.C.CrewWebsite.Host
	url := config.C.CrewWebsite.Router + path

	dateGMT, bodyDigest, authorization, err := makeHMACSign(
		config.C.CrewWebsite.HMACUser,
		config.C.CrewWebsite.HMACSecret,
		host,
		url,
		body,
	)
	if err != nil {
		return fmt.Errorf("make hmac sign failed: %v", err)
	}

	// 设置请求头
	headers := map[string]string{
		"host":          host,
		"date":          dateGMT,
		"digest":        bodyDigest,
		"authorization": authorization,
	}

	elog.WithContext(ctx).WithFields(elog.Fields{
		"headers": headers,
		"body":    body,
	}).Debug("Request details")

	// 发送请求
	requestUrl := fmt.Sprintf("https://%s%s", host, url)
	_, err = kit.HttpPostJsonWithHeader2(ctx, resp, requestUrl, body, headers, conn.GetClient())
	return err
}

// NormalizeAudio 音频标准化
func NormalizeAudio(ctx context.Context, req *AudioNormalizeReq) (*AudioNormalizeResp, error) {
	var resp AudioNormalizeResp
	err := postRequest(ctx, "/api/v1/audio/normalize", req, &resp)
	if err != nil {
		return &resp, err
	}

	elog.WithContext(ctx).WithFields(elog.Fields{
		"req":  req,
		"resp": resp,
	}).Debug("normalize audio resp")

	// 如果返回的url 是 http 的，返回 https
	if strings.HasPrefix(resp.AudioURL, "http://") {
		resp.AudioURL = strings.Replace(resp.AudioURL, "http://", "https://", 1)
	}
	return &resp, nil
}

// CloneFile 克隆文件
func CloneFile(ctx context.Context, req *FilePickerCloneReq) (*FilePickerCloneResp, error) {
	var resp FilePickerCloneResp
	err := postRequest(ctx, "/api/v1/filepicker/clone", req, &resp)
	if err != nil {
		return &resp, err
	}

	elog.WithContext(ctx).WithFields(elog.Fields{
		"req":  req,
		"resp": resp,
	}).Info("clone file resp")
	return &resp, nil
}

// DeleteFile 删除文件
func DeleteFile(ctx context.Context, req *FilePickerDeleteReq) error {
	var resp interface{}
	err := postRequest(ctx, "/api/v1/filepicker/delete", req, &resp)
	if err != nil {
		return err
	}
	elog.WithContext(ctx).WithFields(elog.Fields{
		"req":  req,
		"resp": resp,
	}).Info("delete file success")
	return nil
}
