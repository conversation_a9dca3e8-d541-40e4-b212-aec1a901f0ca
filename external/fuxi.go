package external

import (
	"context"
	"errors"
	"strconv"

	"app/config"
	"app/conn"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
)

type FuxiRecommendParams struct {
	RoleId    int64  `json:"role_id"`
	TraceId   string `json:"trace_id"`
	SessionId string `json:"sessionid"`
	Type      int    `json:"type"`
	TopN      int    `json:"top_n"`
}

type FuxiRecommendData struct {
	Results   []int64 `json:"results"`
	RoleId    string  `json:"role_id"`
	Scm       string  `json:"scm"`
	SessionId string  `json:"sessionid"`
	TraceId   string  `json:"traceid"`
}

type FuxiRecommendResponse struct {
	Code int               `json:"code"`
	Data FuxiRecommendData `json:"data"`
	Msg  string            `json:"msg"`
}

func GetFuxiRecommendWorks(ctx context.Context, params *FuxiRecommendParams) (*FuxiRecommendResponse, error) {
	var resp FuxiRecommendResponse
	if config.C.Fuxi.Disable {
		return &resp, errors.New("fuxi is not enabled")
	}
	_, err := kit.HttpPostJsonWithHeader2(ctx, &resp, config.C.Fuxi.Host, map[string]any{
		"role_id":   strconv.Itoa(int(params.RoleId)),
		"trace_id":  params.TraceId,
		"sessionid": params.SessionId,
		"type":      params.Type,
		"top_n":     params.TopN,
	}, map[string]string{
		"Content-Type": "application/json",
		"Custom-ID":    strconv.Itoa(int(params.RoleId)),
		"referer":      params.TraceId,
	}, conn.GetClient())
	if err != nil {
		return &resp, err
	}
	elog.WithContext(ctx).WithFields(elog.Fields{
		"resp": resp,
	}).Debug("fuxi recommend works resp")
	return &resp, nil
}
