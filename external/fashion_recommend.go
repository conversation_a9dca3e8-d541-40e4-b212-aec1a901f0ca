package external

import (
	"context"
	"errors"

	"app/config"
	"app/conn"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
)

// FashionRecommendParams 时装推荐请求参数
type FashionRecommendParams struct {
	Summary          string                 `json:"summary"`                     // 作品描述
	Name             string                 `json:"name"`                        // 作品名称
	ResampleUrl      string                 `json:"resample_url"`                // 动作url
	ImageUrl         string                 `json:"image_url"`                   // 封面数据url
	Mode             string                 `json:"mode"`                        // 推荐模式: "owned" 或 "official_station"
	PlayerAppearance map[string]interface{} `json:"player_appearance,omitempty"` // 玩家已拥有的外观信息
	Property         string                 `json:"property,omitempty"`          // 数据内容url
	Video            string                 `json:"video,omitempty"`             // 动作视频url
	PlayerId         string                 `json:"player_id"`                   // 用户游戏账号id
	StationId        string                 `json:"station_id"`                  // 用户装扮站账号id
	Role             string                 `json:"role"`                        // 角色信息: "230"(男) 或 "233"(女)
}

// FashionRecommendResponse 时装推荐响应
type FashionRecommendResponse struct {
	Code int         `json:"code"` // 状态代码
	Msg  string      `json:"msg"`  // 状态原因
	UUID string      `json:"uuid"` // 请求唯一标识
	Data interface{} `json:"data"` // 推荐结果
}

// OwnedFashionRecommendData mode="owned" 时的推荐结果
type OwnedFashionRecommendData struct {
	Body struct {
		Style string `json:"style"` // 款式
		Dye   string `json:"dye"`   // 染色uuid
	} `json:"body"`
	Pant struct {
		Style string `json:"style"` // 款式
		Dye   string `json:"dye"`   // 染色uuid
	} `json:"pant"`
	Hair struct {
		Style string `json:"style"` // 款式
		Dye   string `json:"dye"`   // 染色uuid
	} `json:"hair"`
}

// GetFashionRecommend 获取时装推荐
func GetFashionRecommend(ctx context.Context, params *FashionRecommendParams) (*FashionRecommendResponse, error) {
	// 检查配置是否启用
	if config.C.FashionRecommend.Host == "" {
		return nil, errors.New("fashion recommend is not configured")
	}

	// 检查 Mode 参数
	if params.Mode != "owned" && params.Mode != "official_station" {
		return nil, errors.New("invalid mode parameter")
	}

	// 检查 Role 参数
	if params.Role != "230" && params.Role != "233" {
		return nil, errors.New("invalid role parameter")
	}

	// 如果是 owned 模式，检查 PlayerAppearance 参数
	if params.Mode == "owned" && params.PlayerAppearance == nil {
		return nil, errors.New("player_appearance is required for owned mode")
	}

	var resp FashionRecommendResponse

	// 发起HTTP POST请求
	_, err := kit.HttpPostJsonWithHeader2(ctx, &resp,
		config.C.FashionRecommend.Host+"/api/motion_to_cloth/",
		params,
		map[string]string{
			"Content-Type": "application/json",
		},
		conn.GetClient())
	if err != nil {
		return nil, err
	}

	return &resp, nil
}
