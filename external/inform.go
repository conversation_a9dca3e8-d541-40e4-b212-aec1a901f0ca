package external

import (
	"context"
	"errors"
	"fmt"
	"time"

	"app/config"
	"app/conn"
	"app/util"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
)

// EInformResourceType 通知资源类型
type EInformResourceType string

const (
	// ActionResourceType 剧本站资源类型
	ActionResourceType EInformResourceType = "action"
)

// EInformType 通知类型
type EInformType int

const (
	// InformTypeActionComment 动作站评论通知
	InformTypeActionComment EInformType = 121
	// InformTypeActionCommentReply 动作站评论被回复通知
	InformTypeActionCommentReply EInformType = 122
	// InformTypeActionCommentLike 动作站评论被点赞通知
	InformTypeActionCommentLike EInformType = 123
	// InformTypeActionCollect 动作站收藏通知
	InformTypeActionCollect EInformType = 124
	// InformTypeActionLike 动作站点赞通知
	InformTypeActionLike EInformType = 125
	// InformTypeActionShare 动作站分享通知
	InformTypeActionShare EInformType = 126
)

type InformOtherInfo struct {
	OriginText string `json:"originText"`
}

// InformAddParams 添加通知参数
type InformAddParams struct {
	TargetID       int64               `json:"targetId"`
	ObjectID       int64               `json:"objectId"`
	ResourceID     int64               `json:"resourceId"`
	ResourceType   EInformResourceType `json:"resourceType"`
	Text           string              `json:"text"`
	ImgList        []string            `json:"imgList"`
	Type           EInformType         `json:"type"`
	ActionWorkName string              `json:"actionWorkName"`
	OtherInfo      *InformOtherInfo    `json:"otherInfo,omitempty"`
}

// InformDelParams 删除通知参数
type InformDelParams struct {
	ResourceID   int64               `json:"resourceId"`
	ResourceType EInformResourceType `json:"resourceType"`
	Type         *EInformType        `json:"type,omitempty"`
	ObjectID     *int64              `json:"objectId,omitempty"`
}

// InformApiResponse 通知API响应
type InformApiResponse struct {
	Code int `json:"code"`
}

// requestInformApi 请求通知API
func requestInformApi(ctx context.Context, path string, roleID int64, params interface{}) error {
	time := time.Now().UnixMilli()
	token := util.Md5(fmt.Sprintf("%d%d%s", roleID, time, config.C.Inform.TokenSecret))
	base := config.C.Inform.Host + path
	url := fmt.Sprintf("%s?roleId=%d&time=%d&token=%s", base, roleID, time, token)

	var resp InformApiResponse
	_, err := kit.HttpPostJsonWithHeader2(ctx, &resp, url, params, map[string]string{
		"Content-Type": "application/json",
	}, conn.GetClient())
	if err != nil {
		return err
	}

	if resp.Code != 0 {
		return errors.New(fmt.Sprintf("requestInformApiFail: %d", resp.Code))
	}

	return nil
}

// SendInformAdd 发送添加通知
func SendInformAdd(ctx context.Context, roleID int64, params *InformAddParams) error {
	if roleID == params.TargetID {
		return nil
	}
	return requestInformApi(ctx, "/server/inform/add", roleID, params)
}

// SendInformDel 发送删除通知
func SendInformDel(ctx context.Context, roleID int64, params *InformDelParams) error {
	return requestInformApi(ctx, "/server/inform/del", roleID, params)
}
