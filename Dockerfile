# syntax = docker/dockerfile:experimental

# ARG BASEIMAGES=3.15
# golang版本1.20.7
FROM hub.fuxi.netease.com/leihuo-web-gw/golang:1.23.8 AS builder

# 设置WorkDir
WORKDIR /go/src

# 设置go env
RUN go env -w GONOSUMDB=ccc-gitlab.leihuo.netease.com
RUN go env -w GOPROXY=https://athens-preview-16606-3000.apps-sl.danlu.netease.com,https://proxy.golang.org,direct

# 程序编译
COPY go.mod .
COPY go.sum .
RUN --mount=type=cache,target=/go/pkg/mod go mod download && go mod verify
COPY . .
# RUN make ENV=dev
RUN --mount=type=cache,target=/go/pkg/mod --mount=type=cache,target=/root/.cache/go-build GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -v -o ./main ./main.go

# 默认debian版本
# 默认开8000端口
FROM hub.fuxi.netease.com/leihuo-web-gw/debian-run-go:v1.0.0
LABEL maintainer="ruanjun01 <<EMAIL>>"

# 设置时区
ENV TZ=Asia/Shanghai

#日志目录
RUN mkdir /srv/logs && mkdir /srv/logs/sites && chmod 0777 /srv/logs/sites
RUN mkdir /srv/logs/push && chmod 0777 /srv/logs/push
RUN mkdir /yunying && chmod 0777 /yunying

#配置拷贝
#COPY --from=builder /go/src/conf/ /opt/conf/

# 拷贝执行文件
COPY --from=builder /go/src/main /opt/main

# 运行服务
RUN chmod +x /opt/main
ENTRYPOINT ["/opt/main"]
