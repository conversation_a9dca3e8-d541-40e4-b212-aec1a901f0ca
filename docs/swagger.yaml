definitions:
  schema.AuthLoginReq:
    properties:
      aid:
        description: AID 账号aid
        example: "1"
        type: string
      role_id:
        default: 1
        description: 角色Id
        type: integer
      time:
        description: 时间戳,单位秒
        example: 1712041848
        type: integer
      token:
        description: token,计算方式md5(aid+role_id+time+secret_key)
        example: token
        type: string
    required:
    - aid
    - role_id
    - time
    - token
    type: object
  schema.AuthLoginResData:
    properties:
      skey:
        description: 调用API凭证 skey
        example: skey
        type: string
      user_id:
        description: 用户ID
        example: 1
        type: integer
    type: object
  schema.AvatarInfo:
    properties:
      avatar:
        description: 头像ID
        example: 1
        type: integer
      avatar_frame:
        description: 头像框
        example: 1
        type: integer
      gender:
        description: 性别
        example: 1
        type: integer
      job:
        description: 职业
        example: 1
        type: integer
      role_id:
        description: 头像角色ID
        example: 1
        type: integer
    type: object
  schema.CommentAddReq:
    properties:
      content:
        description: 评论内容
        example: 评论内容
        maxLength: 100
        type: string
      reply_comment_id:
        description: 回复评论ID
        example: 1
        minimum: 0
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scm:
        description: 埋点ID,实验组字段
        type: string
      session_id:
        description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        type: string
      trace_id:
        description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        type: string
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - content
    - role_id
    - user_id
    - work_id
    type: object
  schema.CommentAddRes:
    properties:
      content:
        description: 评论内容
        example: 评论内容
        type: string
      ctime:
        description: 评论时间
        example: 1
        type: integer
      id:
        description: 评论ID
        example: 1
        type: integer
      is_liked:
        description: 是否已点赞
        example: true
        type: boolean
      like_count:
        description: 点赞数
        example: 1
        type: integer
      origin_comment_id:
        description: 原评论ID
        example: 1
        type: integer
      reply_comment_id:
        description: 回复评论ID
        example: 1
        type: integer
      reply_count:
        description: 回复数
        example: 1
        type: integer
      reply_list:
        description: 回复列表，默认返回2个, 只对一级评论有效
        items:
          $ref: '#/definitions/schema.CommentItem'
        type: array
      reply_user:
        allOf:
        - $ref: '#/definitions/schema.UserInfo'
        description: 回复人，只对二级评论有效
      role_id:
        description: 评论人RoleId
        example: 1
        type: integer
      user:
        allOf:
        - $ref: '#/definitions/schema.UserInfo'
        description: 评论人信息
      work_id:
        description: 作品ID
        example: 1
        type: integer
    type: object
  schema.CommentCancelLikeReq:
    properties:
      comment_id:
        description: 评论ID
        example: 1
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - comment_id
    - role_id
    - user_id
    type: object
  schema.CommentDelReq:
    properties:
      comment_id:
        description: 评论ID
        example: 1
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - comment_id
    - role_id
    - user_id
    type: object
  schema.CommentDelRes:
    properties:
      comment_id:
        description: 评论ID
        example: 1
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    type: object
  schema.CommentItem:
    properties:
      content:
        description: 评论内容
        example: 评论内容
        type: string
      ctime:
        description: 评论时间
        example: 1
        type: integer
      id:
        description: 评论ID
        example: 1
        type: integer
      is_liked:
        description: 是否已点赞
        example: true
        type: boolean
      like_count:
        description: 点赞数
        example: 1
        type: integer
      origin_comment_id:
        description: 原评论ID
        example: 1
        type: integer
      reply_comment_id:
        description: 回复评论ID
        example: 1
        type: integer
      reply_count:
        description: 回复数
        example: 1
        type: integer
      reply_list:
        description: 回复列表，默认返回2个, 只对一级评论有效
        items:
          $ref: '#/definitions/schema.CommentItem'
        type: array
      reply_user:
        allOf:
        - $ref: '#/definitions/schema.UserInfo'
        description: 回复人，只对二级评论有效
      role_id:
        description: 评论人RoleId
        example: 1
        type: integer
      user:
        allOf:
        - $ref: '#/definitions/schema.UserInfo'
        description: 评论人信息
      work_id:
        description: 作品ID
        example: 1
        type: integer
    type: object
  schema.CommentLikeReq:
    properties:
      comment_id:
        description: 评论ID
        example: 1
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - comment_id
    - role_id
    - user_id
    type: object
  schema.CommentListRes:
    properties:
      list:
        description: 评论列表
        items:
          $ref: '#/definitions/schema.CommentItem'
        type: array
    type: object
  schema.CommentReplyListRes:
    properties:
      list:
        description: 回复列表
        items:
          $ref: '#/definitions/schema.CommentItem'
        type: array
    type: object
  schema.CommonFpPassTokenRes:
    properties:
      expires:
        description: 过期时间
        example: 1634025600
        type: integer
      project:
        description: 项目
        example: l36-action
        type: string
      token:
        description: 授权Token
        example: token
        type: string
      upload_url:
        description: 上传地址
        example: https://l36-action.fp.ps.netease.com/file/new
        type: string
    type: object
  schema.CommonFpTokenRes:
    properties:
      expires:
        description: 过期时间
        example: 1634025600
        type: integer
      project:
        description: 项目
        example: l36-action
        type: string
      token:
        description: 授权Token
        example: token
        type: string
      upload_url:
        description: 上传地址
        example: https://l36-action.fp.ps.netease.com/file/new
        type: string
    type: object
  schema.CommonResData:
    properties:
      is_ok:
        example: true
        type: boolean
    type: object
  schema.DesignerCancelFollowReq:
    properties:
      designer_id:
        default: 1
        description: 设计师ID
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - designer_id
    - role_id
    - user_id
    type: object
  schema.DesignerDetailRes:
    properties:
      avatar:
        allOf:
        - $ref: '#/definitions/schema.AvatarInfo'
        description: 设计师头像
      designer_level:
        description: 设计师等级
        example: 1
        type: integer
      exp:
        description: 经验值
        example: 1
        type: integer
      exp_threshold:
        description: 下一等级经验值
        example: 1
        type: integer
      fans_count:
        description: 粉丝数
        example: 1
        type: integer
      follow_count:
        description: 关注数
        example: 1
        type: integer
      id:
        description: 设计师ID
        example: 1
        type: integer
      is_follow:
        description: 是否已经关注
        example: true
        type: boolean
      liked_count:
        description: 被点赞数
        example: 1
        type: integer
      name:
        description: 设计师名称
        example: 设计师名称
        type: string
      role_id:
        description: 和名字对应的角色ID
        example: 1
        type: integer
      work_count:
        description: 作品数
        example: 1
        type: integer
    type: object
  schema.DesignerFollowReq:
    properties:
      designer_id:
        default: 1
        description: 设计师ID
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - designer_id
    - role_id
    - user_id
    type: object
  schema.DesignerInfo:
    properties:
      avatar:
        allOf:
        - $ref: '#/definitions/schema.AvatarInfo'
        description: 设计师头像
      id:
        description: 设计师ID
        example: 1
        type: integer
      is_follow:
        description: 是否已经关注
        example: true
        type: boolean
      name:
        description: 设计师名称
        example: 设计师名称
        type: string
      role_id:
        description: 和名字对应的角色ID
        example: 1
        type: integer
    type: object
  schema.DesignerItem:
    properties:
      avatar:
        allOf:
        - $ref: '#/definitions/schema.AvatarInfo'
        description: 设计师头像
      designer_level:
        description: 设计师等级
        example: 1
        type: integer
      fans_count:
        description: 粉丝数
        example: 1
        type: integer
      id:
        description: 设计师ID
        example: 1
        type: integer
      is_follow:
        description: 是否已经关注
        example: true
        type: boolean
      liked_count:
        description: 被点赞数
        example: 1
        type: integer
      name:
        description: 设计师名称
        example: 设计师名称
        type: string
      role_id:
        description: 和名字对应的角色ID
        example: 1
        type: integer
      work_count:
        description: 作品数
        example: 1
        type: integer
      works:
        description: 热门作品
        items:
          $ref: '#/definitions/schema.WorkItem'
        type: array
    type: object
  schema.DesignerListFollowRes:
    properties:
      list:
        description: 设计师列表
        items:
          $ref: '#/definitions/schema.DesignerInfo'
        type: array
    type: object
  schema.DesignerListSearchRes:
    properties:
      list:
        description: 设计师列表
        items:
          $ref: '#/definitions/schema.DesignerItem'
        type: array
    type: object
  schema.DressInfo:
    properties:
      cover:
        description: 封面图
        example: 封面图
        type: string
      fashion_id:
        description: 时装ID
        example: 1
        type: integer
      id:
        description: 装扮站作品ID
        example: 1
        type: integer
      name:
        description: 装扮站作品名称
        example: 装扮站作品名称
        type: string
      property:
        description: 属性文件
        example: 属性文件
        type: string
      related_fashion_works:
        description: 穿搭数据
        items:
          $ref: '#/definitions/schema.FashionInfo'
        type: array
      role_id:
        description: 作者ID
        example: 1
        type: integer
      role_name:
        description: 作者名称
        example: 作者名称
        type: string
      subtype:
        description: 子类别
        example: 1
        type: integer
      type:
        description: 类别
        example: 1
        type: integer
    type: object
  schema.DressListRecommendRes:
    properties:
      list:
        description: 服饰列表
        items:
          $ref: '#/definitions/schema.DressInfo'
        type: array
    type: object
  schema.FashionInfo:
    properties:
      cover:
        description: 封面图
        example: 封面图
        type: string
      fashion_id:
        description: 时装ID
        example: 1
        type: integer
      id:
        description: 装扮站作品ID
        example: 1
        type: integer
      name:
        description: 装扮站作品名称
        example: 装扮站作品名称
        type: string
      property:
        description: 属性文件
        example: 属性文件
        type: string
      role_id:
        description: 作者ID
        example: 1
        type: integer
      role_name:
        description: 作者名称
        example: 作者名称
        type: string
      subtype:
        description: 子类别
        example: 1
        type: integer
      type:
        description: 类别
        example: 1
        type: integer
    type: object
  schema.FpReviewCallbackFile:
    properties:
      file_id:
        description: 文件id
        type: string
      from_status:
        description: 原始状态
        enum:
        - 0
        - 1
        - 2
        - 3
        type: integer
      to_status:
        description: 当前状态
        enum:
        - 0
        - 1
        - 2
        - 3
        type: integer
    required:
    - file_id
    - from_status
    - to_status
    type: object
  schema.GmCommentBanReq:
    properties:
      expire_time:
        description: '过期时间. 单位: 秒'
        example: 3600
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
    required:
    - expire_time
    - role_id
    type: object
  schema.GmCommentDelReq:
    properties:
      comment_id:
        description: 评论ID
        example: 1
        type: integer
    required:
    - comment_id
    type: object
  schema.GmCommentItem:
    properties:
      content:
        description: 评论内容
        example: 评论内容
        type: string
      id:
        description: 评论ID
        example: 1
        type: integer
      origin_comment_id:
        description: 原始评论ID
        example: 1
        type: integer
      role_id:
        description: 角色ID
        example: 1
        type: integer
    type: object
  schema.GmCommentListWorkReq:
    properties:
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - work_id
    type: object
  schema.GmCommentListWorkRes:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.GmCommentItem'
        type: array
    type: object
  schema.GmCreatorBanReq:
    properties:
      expire_time:
        description: '过期时间. 单位: 秒'
        example: 3600
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
    required:
    - expire_time
    - role_id
    type: object
  schema.GmWorkDelReq:
    properties:
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - work_id
    type: object
  schema.GmWorkDetailReq:
    properties:
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - work_id
    type: object
  schema.GmWorkListRoleReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
    required:
    - role_id
    type: object
  schema.GmWorkRecoverReq:
    properties:
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - work_id
    type: object
  schema.GmWorkSetVisibilityReq:
    properties:
      visibility:
        enum:
        - 1
        - 2
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - visibility
    - work_id
    type: object
  schema.GmWorkUpdateNameReq:
    properties:
      name:
        maxLength: 20
        type: string
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - name
    - work_id
    type: object
  schema.GmWorkUpdateSummaryReq:
    properties:
      summary:
        maxLength: 120
        type: string
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - summary
    - work_id
    type: object
  schema.KafkaSuccessViewWorkActionReq:
    properties:
      role_id:
        description: 角色ID
        type: integer
      scm:
        description: 实验组
        type: string
      server:
        description: 服务器ID
        type: integer
      trace_id:
        description: 追踪ID
        type: string
      user_id:
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        type: integer
    type: object
  schema.OpenWorkDetailRes:
    properties:
      Summary:
        description: '作品描述 #xx#表示标签'
        example: 作品描述
        type: string
      audit_status:
        description: 审核状态 0=审核通过 2=审核中 3=审核失败
        type: integer
      collect_count:
        description: 收藏数
        example: 1
        type: integer
      comment_count:
        description: 评论数
        example: 1
        type: integer
      contents:
        description: 内容 1=表情 2=动作 3=语音 4=外观 5=镜头
        example:
        - 1
        items:
          type: integer
        type: array
      cover:
        description: 封面
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      designer:
        allOf:
        - $ref: '#/definitions/schema.DesignerInfo'
        description: 设计师
      hot:
        description: 热度
        example: 1
        type: integer
      images:
        description: 图片, 和视频二选一
        example:
        - https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        items:
          type: string
        type: array
      is_collected:
        description: 是否已收藏
        example: true
        type: boolean
      is_liked:
        description: 是否已点赞
        example: true
        type: boolean
      like_count:
        description: 点赞数
        example: 1
        type: integer
      name:
        description: 作品名称
        example: 作品名称
        type: string
      property:
        description: 属性文件Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      resample_url:
        description: 重采样Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      role_id:
        description: 上传时的角色ID
        example: 1
        type: integer
      scene:
        description: 场景
        example: 1
        type: integer
      scm:
        description: Scm 实验组字段
        example: scm
        type: string
      trace_id:
        description: TraceId
        example: trace_id
        type: string
      type:
        description: 作品类型 1=模型 2=视频
        example: 1
        type: integer
      video:
        description: 视频
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      visibility:
        description: 可见性 1=所有人可见 2=仅自己可见
        example: 1
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - contents
    type: object
  schema.PaddingItem:
    properties:
      alarmThreshold:
        description: 报警阈值
        type: integer
      cdMinute:
        description: 数据增长cd, 每x分钟增长一次
        type: integer
      gteMaxRatio:
        description: 最大增长比例
        type: integer
      gteMinRatio:
        description: 最小增长比例
        type: integer
      gtePercent:
        description: 最高增长百分比(注水上限)
        type: integer
      gteThresholdsMax:
        description: 注水阈值上限
        type: integer
      gteThresholdsMin:
        description: 注水阈值下限
        type: integer
      minRealThreshold:
        description: 最小真实阈值
        type: integer
      openStatus:
        description: '0: 关闭 1: 开启'
        type: integer
      releaseHour:
        description: 释放时间
        type: integer
    type: object
  schema.PaddingResItem:
    properties:
      allCount:
        description: 总数量
        type: integer
      getThresholdsMax:
        description: 注水阈值上限
        type: integer
      getThresholdsMin:
        description: 注水阈值下限
        type: integer
      paddingCount:
        description: 注水数量
        type: integer
      realCount:
        description: 真实数量
        type: integer
      workCreateTime:
        description: 作品创建时间
        type: integer
      workId:
        description: 作品id
        type: integer
      workName:
        description: 作品名称
        type: string
    type: object
  schema.RankDesignerExpRes:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.RankDesignerItem'
        type: array
      self:
        $ref: '#/definitions/schema.RankDesignerItem'
    type: object
  schema.RankDesignerItem:
    properties:
      designer_level:
        type: integer
      exp:
        type: integer
      gender:
        type: integer
      job:
        type: integer
      level:
        type: integer
      rank:
        type: integer
      role_id:
        type: integer
      role_name:
        type: string
    type: object
  schema.RankWorkHotRes:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.RankWorkItem'
        type: array
    type: object
  schema.RankWorkItem:
    properties:
      designer:
        $ref: '#/definitions/schema.DesignerInfo'
      hot:
        type: integer
      images:
        items:
          type: string
        type: array
      name:
        type: string
      rank:
        type: integer
      summary:
        type: string
      video:
        type: string
      work_id:
        type: integer
    type: object
  schema.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  schema.ServerDesignerDetailRes:
    properties:
      avatar:
        allOf:
        - $ref: '#/definitions/schema.AvatarInfo'
        description: 设计师头像
      designer_level:
        description: 设计师等级
        example: 1
        type: integer
      exp:
        description: 经验值
        example: 1
        type: integer
      exp_threshold:
        description: 下一等级经验值
        example: 1
        type: integer
      fans_count:
        description: 粉丝数
        example: 1
        type: integer
      follow_count:
        description: 关注数
        example: 1
        type: integer
      id:
        description: 设计师ID
        example: 1
        type: integer
      is_follow:
        description: 是否已经关注
        example: true
        type: boolean
      liked_count:
        description: 被点赞数
        example: 1
        type: integer
      name:
        description: 设计师名称
        example: 设计师名称
        type: string
      role_id:
        description: 和名字对应的角色ID
        example: 1
        type: integer
      work_count:
        description: 作品数
        example: 1
        type: integer
    type: object
  schema.ServerFpReviewCallbackReq:
    properties:
      files:
        description: 文件列表
        items:
          $ref: '#/definitions/schema.FpReviewCallbackFile'
        type: array
      operator:
        description: 操作者
        type: string
      project_name:
        description: 项目名称
        type: string
    required:
    - files
    - operator
    - project_name
    type: object
  schema.ServerPaddingTriggerReq:
    properties:
      collectCfg:
        $ref: '#/definitions/schema.PaddingItem'
      commentCfg:
        $ref: '#/definitions/schema.PaddingItem'
      likeCfg:
        $ref: '#/definitions/schema.PaddingItem'
    type: object
  schema.ServerPaddingTriggerRes:
    properties:
      collectData:
        $ref: '#/definitions/schema.PaddingResItem'
      commentData:
        $ref: '#/definitions/schema.PaddingResItem'
      likeData:
        $ref: '#/definitions/schema.PaddingResItem'
    type: object
  schema.SetFakeTimeReq:
    properties:
      fake_time:
        description: 伪造时间 支持 +1h, -1h, +1d, -1d, +1w, -1w, +1M, -1M, +1y, -1y,也支持时间
          "2006-01-02 15:04:05"
        example: "2024-11-01 00:00:00"
        type: string
    required:
    - fake_time
    type: object
  schema.TopicItem:
    properties:
      id:
        description: 话题ID
        example: 1
        type: integer
      name:
        description: 话题名称
        example: 话题名称
        type: string
    type: object
  schema.TopicListRecommendRes:
    properties:
      list:
        description: 话题列表
        items:
          $ref: '#/definitions/schema.TopicItem'
        type: array
    type: object
  schema.TopicListRes:
    properties:
      list:
        description: 话题列表
        items:
          $ref: '#/definitions/schema.TopicItem'
        type: array
    type: object
  schema.TopicSearchRes:
    properties:
      list:
        description: 话题列表
        items:
          $ref: '#/definitions/schema.TopicItem'
        type: array
    type: object
  schema.UserAvatarInfo:
    properties:
      avatar:
        description: 头像ID
        example: 1
        type: integer
      avatar_frame:
        description: 头像框
        example: 1
        type: integer
      gender:
        description: 性别
        example: 1
        type: integer
      is_default:
        description: 是否是默认头像
        example: true
        type: boolean
      job:
        description: 职业
        example: 1
        type: integer
      role_id:
        description: 头像角色ID
        example: 1
        type: integer
    type: object
  schema.UserAvatarListRes:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.UserAvatarInfo'
        type: array
    type: object
  schema.UserAvatarUpdateReq:
    properties:
      avatar_role_id:
        description: 头像角色ID
        example: 1
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - avatar_role_id
    - role_id
    - user_id
    type: object
  schema.UserInfo:
    properties:
      avatar:
        allOf:
        - $ref: '#/definitions/schema.AvatarInfo'
        description: 用户头像
      id:
        description: 用户ID
        example: 1
        type: integer
      name:
        description: 用户昵称
        example: 昵称
        type: string
      server_id:
        description: 服务器ID
        example: 1
        type: integer
    type: object
  schema.UserNameInfo:
    properties:
      is_default:
        example: true
        type: boolean
      role_id:
        example: 1
        type: integer
      role_name:
        example: 角色名称
        type: string
    type: object
  schema.UserNameListRes:
    properties:
      list:
        items:
          $ref: '#/definitions/schema.UserNameInfo'
        type: array
    type: object
  schema.UserUpdateReq:
    properties:
      name_role_id:
        description: 昵称
        example: 1
        type: integer
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
    required:
    - name_role_id
    - role_id
    - user_id
    type: object
  schema.WorkCancelCollectReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkCancelLikeReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkCollectReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scm:
        description: 埋点ID,实验组字段
        type: string
      session_id:
        description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        type: string
      trace_id:
        description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        type: string
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkCompletePlayReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scm:
        description: 埋点ID,实验组字段
        type: string
      session_id:
        description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        type: string
      trace_id:
        description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        type: string
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkCreateReq:
    properties:
      contents:
        description: 内容 1=表情 2=动作 3=语音 4=外观 5=镜头
        example:
        - 1
        items:
          type: integer
        type: array
      cover:
        description: 封面
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      gender:
        description: 上传人性别 0=男 1=女
        enum:
        - 0
        - 1
        example: 0
        type: integer
      images:
        description: 图片, 和视频二选一
        example:
        - https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        items:
          type: string
        type: array
      name:
        description: 作品名称
        example: 作品名称
        maxLength: 14
        type: string
      property:
        description: 属性文件Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      resample_url:
        description: 重采样Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scene:
        description: 场景
        example: 1
        type: integer
      summary:
        description: '作品描述 #xx#表示标签'
        example: 作品描述
        maxLength: 120
        type: string
      type:
        description: 类型 1=模型 2=动作视频 3=纯视频
        enum:
        - 1
        - 2
        - 3
        example: 1
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      video:
        description: 视频，type=2时必填
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      visibility:
        description: 可见性 1=所有人可见 2=仅自己可见
        enum:
        - 1
        - 2
        example: 1
        type: integer
    required:
    - cover
    - name
    - role_id
    - scene
    - summary
    - type
    - user_id
    - visibility
    type: object
  schema.WorkCreateRes:
    properties:
      work_id:
        description: 作品ID
        example: 1
        type: integer
    type: object
  schema.WorkDelReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkDetailRes:
    properties:
      Summary:
        description: '作品描述 #xx#表示标签'
        example: 作品描述
        type: string
      audit_status:
        description: 审核状态 0=审核通过 2=审核中 3=审核失败
        type: integer
      collect_count:
        description: 收藏数
        example: 1
        type: integer
      comment_count:
        description: 评论数
        example: 1
        type: integer
      contents:
        description: 内容 1=表情 2=动作 3=语音 4=外观 5=镜头
        example:
        - 1
        items:
          type: integer
        type: array
      cover:
        description: 封面
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      designer:
        allOf:
        - $ref: '#/definitions/schema.DesignerInfo'
        description: 设计师
      hot:
        description: 热度
        example: 1
        type: integer
      images:
        description: 图片, 和视频二选一
        example:
        - https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        items:
          type: string
        type: array
      is_collected:
        description: 是否已收藏
        example: true
        type: boolean
      is_liked:
        description: 是否已点赞
        example: true
        type: boolean
      like_count:
        description: 点赞数
        example: 1
        type: integer
      name:
        description: 作品名称
        example: 作品名称
        type: string
      property:
        description: 属性文件Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      resample_url:
        description: 重采样Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      role_id:
        description: 上传时的角色ID
        example: 1
        type: integer
      scene:
        description: 场景
        example: 1
        type: integer
      scm:
        description: Scm 实验组字段
        example: scm
        type: string
      trace_id:
        description: TraceId
        example: trace_id
        type: string
      type:
        description: 作品类型 1=模型 2=视频
        example: 1
        type: integer
      video:
        description: 视频
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      visibility:
        description: 可见性 1=所有人可见 2=仅自己可见
        example: 1
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - contents
    type: object
  schema.WorkEffectShareReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      share_id:
        description: 分享ID
        example: 1
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - share_id
    - user_id
    - work_id
    type: object
  schema.WorkItem:
    properties:
      Summary:
        description: '作品描述 #xx#表示标签'
        example: 作品描述
        type: string
      audit_status:
        description: 审核状态 0=审核通过 2=审核中 3=审核失败
        type: integer
      collect_count:
        description: 收藏数
        example: 1
        type: integer
      comment_count:
        description: 评论数
        example: 1
        type: integer
      contents:
        description: 内容 1=表情 2=动作 3=语音 4=外观 5=镜头
        example:
        - 1
        items:
          type: integer
        type: array
      cover:
        description: 封面
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      designer:
        allOf:
        - $ref: '#/definitions/schema.DesignerInfo'
        description: 设计师
      hot:
        description: 热度
        example: 1
        type: integer
      images:
        description: 图片, 和视频二选一
        example:
        - https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        items:
          type: string
        type: array
      is_collected:
        description: 是否已收藏
        example: true
        type: boolean
      is_liked:
        description: 是否已点赞
        example: true
        type: boolean
      like_count:
        description: 点赞数
        example: 1
        type: integer
      name:
        description: 作品名称
        example: 作品名称
        type: string
      property:
        description: 属性文件Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      resample_url:
        description: 重采样Url
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      role_id:
        description: 上传时的角色ID
        example: 1
        type: integer
      scene:
        description: 场景
        example: 1
        type: integer
      scm:
        description: Scm 实验组字段
        example: scm
        type: string
      trace_id:
        description: TraceId
        example: trace_id
        type: string
      type:
        description: 作品类型 1=模型 2=视频
        example: 1
        type: integer
      video:
        description: 视频
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      visibility:
        description: 可见性 1=所有人可见 2=仅自己可见
        example: 1
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - contents
    type: object
  schema.WorkLikeReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scm:
        description: 埋点ID,实验组字段
        type: string
      session_id:
        description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        type: string
      trace_id:
        description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        type: string
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkListRes:
    properties:
      list:
        description: 作品列表
        items:
          $ref: '#/definitions/schema.WorkItem'
        type: array
    type: object
  schema.WorkShareReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkShareRes:
    properties:
      share_id:
        description: 分享ID
        example: 1
        type: integer
    type: object
  schema.WorkTraceReq:
    properties:
      bhv_type:
        description: 行为类型 tip=打赏 report=举报 remake=拍同款 tab=切换tab search=搜索
        enum:
        - tip
        - report
        - remake
        - tab
        - search
        example: "1"
        type: string
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scm:
        description: 埋点ID,实验组字段
        type: string
      session_id:
        description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        type: string
      trace_id:
        description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        type: string
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - bhv_type
    - role_id
    - user_id
    - work_id
    type: object
  schema.WorkUpdateReq:
    properties:
      Summary:
        description: '作品描述 #xx#表示标签'
        example: 作品描述
        maxLength: 120
        type: string
      cover:
        description: 封面
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      images:
        description: 图片, 和视频二选一
        example:
        - https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        items:
          type: string
        type: array
      name:
        description: 作品名称
        example: 作品名称
        maxLength: 20
        type: string
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scene:
        description: 场景
        example: 1
        type: integer
      type:
        description: 类型
        enum:
        - 1
        - 2
        - 3
        example: 1
        type: integer
      user_id:
        default: 1
        description: 用户ID
        type: integer
      video:
        description: 视频
        example: https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06
        type: string
      visibility:
        description: 可见性 1=所有人可见 2=仅自己可见
        enum:
        - 1
        - 2
        example: 1
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - Summary
    - name
    - role_id
    - scene
    - type
    - user_id
    - visibility
    - work_id
    type: object
  schema.WorkUpdateRes:
    properties:
      work_id:
        description: 作品ID
        example: 1
        type: integer
    type: object
  schema.WorkUseReq:
    properties:
      role_id:
        default: 1
        description: 角色Id
        type: integer
      scm:
        description: 埋点ID,实验组字段
        type: string
      session_id:
        description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        type: string
      trace_id:
        description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        type: string
      user_id:
        default: 1
        description: 用户ID
        type: integer
      work_id:
        description: 作品ID
        example: 1
        type: integer
    required:
    - role_id
    - user_id
    - work_id
    type: object
info:
  contact: {}
  description: 接口文档
  title: API
  version: "1.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: 获取API凭证skey
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.AuthLoginReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.AuthLoginResData'
              type: object
      summary: 获取API凭证skey
      tags:
      - auth
  /comment/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.CommentAddReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentAddRes'
              type: object
      security:
      - skeyAuth: []
      summary: 添加评论
      tags:
      - comment
  /comment/cancel_like:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.CommentCancelLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 取消点赞评论
      tags:
      - comment
  /comment/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.CommentDelReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentDelRes'
              type: object
      security:
      - skeyAuth: []
      summary: 删除评论
      tags:
      - comment
  /comment/like:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.CommentLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 点赞评论
      tags:
      - comment
  /comment/list:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 排序方式
        example: new
        in: query
        name: sort
        type: string
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      - description: 作品ID
        example: 1
        in: query
        name: work_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取作品评论列表
      tags:
      - comment
  /comment/reply/list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 评论ID
        example: 1
        in: query
        name: comment_id
        required: true
        type: integer
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      - description: 作品ID
        example: 1
        in: query
        name: work_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommentReplyListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取作品回复列表
      tags:
      - comment
  /common/fp_pass_token:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonFpPassTokenRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取上传fp文件token，用于上传property等不需要审核的文件
      tags:
      - common
  /common/fp_token:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonFpTokenRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取上传fp文件token，用于上传图片等需要审核的文件
      tags:
      - common
  /designer/cancel_follow:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.DesignerCancelFollowReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 取消关注设计师
      tags:
      - designer
  /designer/detail:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 设计师ID
        in: query
        name: designer_id
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.DesignerDetailRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取设计师详细信息
      tags:
      - designer
  /designer/follow:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.DesignerFollowReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 关注设计师
      tags:
      - designer
  /designer/list/follow:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.DesignerListFollowRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取关注的设计师列表
      tags:
      - designer
  /designer/list/search:
    get:
      consumes:
      - application/json
      parameters:
      - description: 关键字
        example: 关键字
        in: query
        name: keyword
        type: string
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.DesignerListSearchRes'
              type: object
      security:
      - skeyAuth: []
      summary: 搜索作者
      tags:
      - designer
  /dress/list/recommend:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      - description: 作品ID
        example: 1
        in: query
        name: work_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.DressListRecommendRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取推荐穿搭
      tags:
      - dress
  /gm/comment/ban:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmCommentBanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 禁止剧组站评论
      tags:
      - gm
  /gm/comment/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmCommentDelReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 删除剧组站评论
      tags:
      - gm
  /gm/comment/list/work:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmCommentListWorkReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.GmCommentListWorkRes'
              type: object
      summary: 根据作品id查询评论内容
      tags:
      - gm
  /gm/creator/ban:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmCreatorBanReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 禁止上传剧组站作品
      tags:
      - gm
  /gm/work/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkDelReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 删除剧本站作品
      tags:
      - gm
  /gm/work/detail:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkDetailReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 根据作品id查询作品情况
      tags:
      - gm
  /gm/work/list/role:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkListRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 根据角色id查询作品清空
      tags:
      - gm
  /gm/work/recover:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkRecoverReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 回复剧本站作品
      tags:
      - gm
  /gm/work/set_visibility:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkSetVisibilityReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 设置剧组站作品可见范围
      tags:
      - gm
  /gm/work/update_name:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkUpdateNameReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 修改剧组站作品名
      tags:
      - gm
  /gm/work/update_summary:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.GmWorkUpdateSummaryReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 修改剧组站作品描述
      tags:
      - gm
  /health:
    get:
      description: 健康检查
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 健康检查
      tags:
      - health
  /kafka/success_view_work_action:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.KafkaSuccessViewWorkActionReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 有效浏览
      tags:
      - kafka
  /open/work/detail:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 作品ID
        example: 1
        in: query
        name: work_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.OpenWorkDetailRes'
              type: object
      summary: 获取作品详情(无需登录)
      tags:
      - open
  /rank/designer/exp:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.RankDesignerExpRes'
              type: object
      summary: 设计师经验排行榜
      tags:
      - rank
  /rank/work/hot:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: role_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.RankWorkHotRes'
              type: object
      summary: 作品热度排行榜
      tags:
      - rank
  /server/designer/detail:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.ServerDesignerDetailRes'
              type: object
      summary: 获取设计师详情
      tags:
      - server
  /server/fp_server_callback:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.ServerFpReviewCallbackReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - fpAuth: []
      summary: FP回调
      tags:
      - server
  /server/padding/trigger:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.ServerPaddingTriggerReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.ServerPaddingTriggerRes'
              type: object
      summary: 触发注水
      tags:
      - server
  /set_fake_time:
    post:
      consumes:
      - application/json
      description: 设置假时间
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.SetFakeTimeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      summary: 设置假时间
      tags:
      - health
  /topic/list:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.TopicListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 热门话题列表
      tags:
      - topic
  /topic/list/recommend:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.TopicListRecommendRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取推荐的话题列表
      tags:
      - topic
  /topic/search:
    get:
      consumes:
      - application/json
      parameters:
      - description: 关键字
        example: 关键字
        in: query
        name: keyword
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.TopicSearchRes'
              type: object
      security:
      - skeyAuth: []
      summary: 搜索话题
      tags:
      - topic
  /user/avatar/list:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.UserAvatarListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 玩家头像列表
      tags:
      - user
  /user/avatar/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.UserAvatarUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 更新玩家头像
      tags:
      - user
  /user/name/list:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.UserNameListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 角色名称列表
      tags:
      - user
  /user/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.UserUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 更新玩家信息
      tags:
      - user
  /work/cancel_collect:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkCancelCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 取消收藏
      tags:
      - work
  /work/cancel_like:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkCancelLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 取消点赞
      tags:
      - work
  /work/collect:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 收藏
      tags:
      - work
  /work/complete-play:
    post:
      consumes:
      - application/json
      deprecated: true
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkCompletePlayReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 有效浏览(废弃，通过日志同步）
      tags:
      - work
  /work/create:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkCreateRes'
              type: object
      security:
      - skeyAuth: []
      summary: 上传作品
      tags:
      - work
  /work/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkDelReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 删除作品
      tags:
      - work
  /work/detail:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 埋点ID,实验组字段
        in: query
        name: scm
        type: string
      - description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳
        in: query
        name: session_id
        type: string
      - description: 埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为
        in: query
        name: trace_id
        type: string
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      - description: 作品ID
        example: 1
        in: query
        name: work_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkDetailRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取作品详情
      tags:
      - work
  /work/like:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkLikeReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 点赞
      tags:
      - work
  /work/list/collect:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔
        example: 1,2,3
        in: query
        name: contents
        type: string
      - description: 性别 0=不限 1=男 2=女
        enum:
        - 0
        - 1
        - 2
        example: 0
        in: query
        name: gender
        type: integer
      - description: Keyword
        example: 关键词
        in: query
        name: keyword
        type: string
      - description: 顺序
        example: asc
        in: query
        name: order
        type: string
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数
        enum:
        - hot
        - new
        - like
        - collect
        - comment
        example: hot
        in: query
        name: sort
        type: string
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取收藏作品列表
      tags:
      - work
  /work/list/designer:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔
        example: 1,2,3
        in: query
        name: contents
        type: string
      - default: 1
        description: 设计师ID
        in: query
        name: designer_id
        required: true
        type: integer
      - description: 性别 0=不限 1=男 2=女
        enum:
        - 0
        - 1
        - 2
        example: 0
        in: query
        name: gender
        type: integer
      - description: Keyword
        example: 关键词
        in: query
        name: keyword
        type: string
      - description: 顺序
        example: asc
        in: query
        name: order
        type: string
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数
        enum:
        - hot
        - new
        - like
        - collect
        - comment
        example: hot
        in: query
        name: sort
        type: string
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取设计师作品列表
      tags:
      - work
  /work/list/mine:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔
        example: 1,2,3
        in: query
        name: contents
        type: string
      - description: 性别 0=不限 1=男 2=女
        enum:
        - 0
        - 1
        - 2
        example: 0
        in: query
        name: gender
        type: integer
      - description: Keyword
        example: 关键词
        in: query
        name: keyword
        type: string
      - description: 顺序
        example: asc
        in: query
        name: order
        type: string
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数
        enum:
        - hot
        - new
        - like
        - collect
        - comment
        example: hot
        in: query
        name: sort
        type: string
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取本人作品列表
      tags:
      - work
  /work/list/recommend:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 链路追踪，每次打开推荐瀑布流是相同的sessionid，同一次打开瀑布流下的多次请求时是不同的traceid，生成方式role_id+时间戳
        example: session_id
        in: query
        name: session_id
        required: true
        type: string
      - description: 跟踪id,用户单次请求生成的唯一id，由role_id+时间戳+三位随机整数组成
        example: trace_id
        in: query
        name: trace_id
        required: true
        type: string
      - description: 类型
        enum:
        - 1
        - 2
        - 3
        example: 1
        in: query
        name: type
        required: true
        type: integer
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 推荐作品列表
      tags:
      - work
  /work/list/search:
    get:
      consumes:
      - application/json
      parameters:
      - description: 动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔
        example: 1,2,3
        in: query
        name: contents
        type: string
      - description: 性别 0=不限 1=男 2=女
        enum:
        - 0
        - 1
        - 2
        example: 0
        in: query
        name: gender
        type: integer
      - description: Keyword
        example: 关键词
        in: query
        name: keyword
        type: string
      - description: 顺序
        example: asc
        in: query
        name: order
        type: string
      - default: 1
        description: 当前页
        in: query
        minimum: 1
        name: page
        required: true
        type: integer
      - default: 10
        description: 每页个数
        in: query
        maximum: 20
        minimum: 1
        name: page_size
        required: true
        type: integer
      - default: 1
        description: 角色Id
        in: query
        name: role_id
        required: true
        type: integer
      - description: 排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数
        enum:
        - hot
        - new
        - like
        - collect
        - comment
        example: hot
        in: query
        name: sort
        type: string
      - example: 话题
        in: query
        name: topic
        type: string
      - default: 1
        description: 用户ID
        in: query
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkListRes'
              type: object
      security:
      - skeyAuth: []
      summary: 搜索作品
      tags:
      - work
  /work/share:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkShareReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkShareRes'
              type: object
      security:
      - skeyAuth: []
      summary: 获取分享ID
      tags:
      - work
  /work/share/visit:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkEffectShareReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 有效分享
      tags:
      - work
  /work/trace:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkTraceReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 推送埋点日志
      tags:
      - work
  /work/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.WorkUpdateRes'
              type: object
      security:
      - skeyAuth: []
      summary: 更新作品
      tags:
      - work
  /work/use:
    post:
      consumes:
      - application/json
      parameters:
      - description: 请求参数
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/schema.WorkUseReq'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/schema.Response'
            - properties:
                data:
                  $ref: '#/definitions/schema.CommonResData'
              type: object
      security:
      - skeyAuth: []
      summary: 应用作品
      tags:
      - work
securityDefinitions:
  fpAuth:
    description: filepicker回调token
    in: header
    name: x-fp-token
    type: apiKey
  skeyAuth:
    description: 用户登录后获取的skey
    in: header
    name: skey
    type: apiKey
swagger: "2.0"
