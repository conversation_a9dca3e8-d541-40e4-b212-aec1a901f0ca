{"swagger": "2.0", "info": {"description": "接口文档", "title": "API", "contact": {}, "version": "1.0"}, "paths": {"/auth/login": {"post": {"description": "获取API凭证skey", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "获取API凭证skey", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.AuthLoginReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.AuthLoginResData"}}}]}}}}}, "/comment/add": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "添加评论", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentAddReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentAddRes"}}}]}}}}}, "/comment/cancel_like": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "取消点赞评论", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentCancelLikeReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/comment/del": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "删除评论", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentDelReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentDelRes"}}}]}}}}}, "/comment/like": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "点赞评论", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CommentLikeReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/comment/list": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "获取作品评论列表", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "string", "example": "new", "description": "排序方式", "name": "sort", "in": "query"}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "integer", "example": 1, "description": "作品ID", "name": "work_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentListRes"}}}]}}}}}, "/comment/reply/list": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["comment"], "summary": "获取作品回复列表", "parameters": [{"type": "integer", "example": 1, "description": "评论ID", "name": "comment_id", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "integer", "example": 1, "description": "作品ID", "name": "work_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommentReplyListRes"}}}]}}}}}, "/common/fp_pass_token": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["common"], "summary": "获取上传fp文件token，用于上传property等不需要审核的文件", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonFpPassTokenRes"}}}]}}}}}, "/common/fp_token": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["common"], "summary": "获取上传fp文件token，用于上传图片等需要审核的文件", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonFpTokenRes"}}}]}}}}}, "/designer/cancel_follow": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["designer"], "summary": "取消关注设计师", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.DesignerCancelFollowReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/designer/detail": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["designer"], "summary": "获取设计师详细信息", "parameters": [{"type": "integer", "default": 1, "description": "设计师ID", "name": "designer_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.DesignerDetailRes"}}}]}}}}}, "/designer/follow": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["designer"], "summary": "关注设计师", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.DesignerFollowReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/designer/list/follow": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["designer"], "summary": "获取关注的设计师列表", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.DesignerListFollowRes"}}}]}}}}}, "/designer/list/search": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["designer"], "summary": "搜索作者", "parameters": [{"type": "string", "example": "关键字", "description": "关键字", "name": "keyword", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.DesignerListSearchRes"}}}]}}}}}, "/dress/list/recommend": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["dress"], "summary": "获取推荐穿搭", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "integer", "example": 1, "description": "作品ID", "name": "work_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.DressListRecommendRes"}}}]}}}}}, "/gm/comment/ban": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "禁止剧组站评论", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmCommentBanReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/comment/del": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "删除剧组站评论", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmCommentDelReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/comment/list/work": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "根据作品id查询评论内容", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmCommentListWorkReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.GmCommentListWorkRes"}}}]}}}}}, "/gm/creator/ban": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "禁止上传剧组站作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmCreatorBanReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/del": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "删除剧本站作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkDelReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/detail": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "根据作品id查询作品情况", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkDetailReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/list/role": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "根据角色id查询作品清空", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkListRoleReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/recover": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "回复剧本站作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkRecoverReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/set_visibility": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "设置剧组站作品可见范围", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkSetVisibilityReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/update_name": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "修改剧组站作品名", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkUpdateNameReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/gm/work/update_summary": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["gm"], "summary": "修改剧组站作品描述", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.GmWorkUpdateSummaryReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/health": {"get": {"description": "健康检查", "produces": ["application/json"], "tags": ["health"], "summary": "健康检查", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/kafka/success_view_work_action": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["kafka"], "summary": "有效浏览", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.KafkaSuccessViewWorkActionReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/open/work/detail": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["open"], "summary": "获取作品详情(无需登录)", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "example": 1, "description": "作品ID", "name": "work_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.OpenWorkDetailRes"}}}]}}}}}, "/rank/designer/exp": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["rank"], "summary": "设计师经验排行榜", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.RankDesignerExpRes"}}}]}}}}}, "/rank/work/hot": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["rank"], "summary": "作品热度排行榜", "parameters": [{"type": "integer", "name": "role_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.RankWorkHotRes"}}}]}}}}}, "/server/designer/detail": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["server"], "summary": "获取设计师详情", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.ServerDesignerDetailRes"}}}]}}}}}, "/server/fp_server_callback": {"post": {"security": [{"fpAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["server"], "summary": "FP回调", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.ServerFpReviewCallbackReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/server/padding/trigger": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["server"], "summary": "触发注水", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.ServerPaddingTriggerReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.ServerPaddingTriggerRes"}}}]}}}}}, "/set_fake_time": {"post": {"description": "设置假时间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["health"], "summary": "设置假时间", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.SetFakeTimeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/topic/list": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topic"], "summary": "热门话题列表", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.TopicListRes"}}}]}}}}}, "/topic/list/recommend": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topic"], "summary": "获取推荐的话题列表", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.TopicListRecommendRes"}}}]}}}}}, "/topic/search": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topic"], "summary": "搜索话题", "parameters": [{"type": "string", "example": "关键字", "description": "关键字", "name": "keyword", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.TopicSearchRes"}}}]}}}}}, "/user/avatar/list": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "玩家头像列表", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.UserAvatarListRes"}}}]}}}}}, "/user/avatar/update": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "更新玩家头像", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.UserAvatarUpdateReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/user/name/list": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "角色名称列表", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.UserNameListRes"}}}]}}}}}, "/user/update": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "更新玩家信息", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.UserUpdateReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/cancel_collect": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "取消收藏", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkCancelCollectReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/cancel_like": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "取消点赞", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkCancelLikeReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/collect": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "收藏", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkCollectReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/complete-play": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "有效浏览(废弃，通过日志同步）", "deprecated": true, "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkCompletePlayReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/create": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "上传作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkCreateReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkCreateRes"}}}]}}}}}, "/work/del": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "删除作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkDelReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/detail": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "获取作品详情", "parameters": [{"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "string", "description": "埋点ID,实验组字段", "name": "scm", "in": "query"}, {"type": "string", "description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "name": "session_id", "in": "query"}, {"type": "string", "description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "name": "trace_id", "in": "query"}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "integer", "example": 1, "description": "作品ID", "name": "work_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkDetailRes"}}}]}}}}}, "/work/like": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "点赞", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkLikeReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/list/collect": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "获取收藏作品列表", "parameters": [{"type": "string", "example": "1,2,3", "description": "动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔", "name": "contents", "in": "query"}, {"enum": [0, 1, 2], "type": "integer", "example": 0, "description": "性别 0=不限 1=男 2=女", "name": "gender", "in": "query"}, {"type": "string", "example": "关键词", "description": "Keyword", "name": "keyword", "in": "query"}, {"type": "string", "example": "asc", "description": "顺序", "name": "order", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"enum": ["hot", "new", "like", "collect", "comment"], "type": "string", "example": "hot", "description": "排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数", "name": "sort", "in": "query"}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkListRes"}}}]}}}}}, "/work/list/designer": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "获取设计师作品列表", "parameters": [{"type": "string", "example": "1,2,3", "description": "动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔", "name": "contents", "in": "query"}, {"type": "integer", "default": 1, "description": "设计师ID", "name": "designer_id", "in": "query", "required": true}, {"enum": [0, 1, 2], "type": "integer", "example": 0, "description": "性别 0=不限 1=男 2=女", "name": "gender", "in": "query"}, {"type": "string", "example": "关键词", "description": "Keyword", "name": "keyword", "in": "query"}, {"type": "string", "example": "asc", "description": "顺序", "name": "order", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"enum": ["hot", "new", "like", "collect", "comment"], "type": "string", "example": "hot", "description": "排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数", "name": "sort", "in": "query"}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkListRes"}}}]}}}}}, "/work/list/mine": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "获取本人作品列表", "parameters": [{"type": "string", "example": "1,2,3", "description": "动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔", "name": "contents", "in": "query"}, {"enum": [0, 1, 2], "type": "integer", "example": 0, "description": "性别 0=不限 1=男 2=女", "name": "gender", "in": "query"}, {"type": "string", "example": "关键词", "description": "Keyword", "name": "keyword", "in": "query"}, {"type": "string", "example": "asc", "description": "顺序", "name": "order", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"enum": ["hot", "new", "like", "collect", "comment"], "type": "string", "example": "hot", "description": "排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数", "name": "sort", "in": "query"}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkListRes"}}}]}}}}}, "/work/list/recommend": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "推荐作品列表", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"type": "string", "example": "session_id", "description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，同一次打开瀑布流下的多次请求时是不同的traceid，生成方式role_id+时间戳", "name": "session_id", "in": "query", "required": true}, {"type": "string", "example": "trace_id", "description": "跟踪id,用户单次请求生成的唯一id，由role_id+时间戳+三位随机整数组成", "name": "trace_id", "in": "query", "required": true}, {"enum": [1, 2, 3], "type": "integer", "example": 1, "description": "类型", "name": "type", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkListRes"}}}]}}}}}, "/work/list/search": {"get": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "搜索作品", "parameters": [{"type": "string", "example": "1,2,3", "description": "动作内容 1=表情 2=动作 3=语音 4=外观 5=镜头 多选用逗号分隔", "name": "contents", "in": "query"}, {"enum": [0, 1, 2], "type": "integer", "example": 0, "description": "性别 0=不限 1=男 2=女", "name": "gender", "in": "query"}, {"type": "string", "example": "关键词", "description": "Keyword", "name": "keyword", "in": "query"}, {"type": "string", "example": "asc", "description": "顺序", "name": "order", "in": "query"}, {"minimum": 1, "type": "integer", "default": 1, "description": "当前页", "name": "page", "in": "query", "required": true}, {"maximum": 20, "minimum": 1, "type": "integer", "default": 10, "description": "每页个数", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "角色Id", "name": "role_id", "in": "query", "required": true}, {"enum": ["hot", "new", "like", "collect", "comment"], "type": "string", "example": "hot", "description": "排序 hot=热度 new=最新 like=点赞数 collect=收藏数 comment=评论数", "name": "sort", "in": "query"}, {"type": "string", "example": "话题", "name": "topic", "in": "query"}, {"type": "integer", "default": 1, "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkListRes"}}}]}}}}}, "/work/share": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "获取分享ID", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkShareReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkShareRes"}}}]}}}}}, "/work/share/visit": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "有效分享", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkEffectShareReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/trace": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "推送埋点日志", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkTraceReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}, "/work/update": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "更新作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkUpdateReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.WorkUpdateRes"}}}]}}}}}, "/work/use": {"post": {"security": [{"skeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["work"], "summary": "应用作品", "parameters": [{"description": "请求参数", "name": "req", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.WorkUseReq"}}], "responses": {"200": {"description": "成功", "schema": {"allOf": [{"$ref": "#/definitions/schema.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schema.CommonResData"}}}]}}}}}}, "definitions": {"schema.AuthLoginReq": {"type": "object", "required": ["aid", "role_id", "time", "token"], "properties": {"aid": {"description": "AID 账号aid", "type": "string", "example": "1"}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "time": {"description": "时间戳,单位秒", "type": "integer", "example": 1712041848}, "token": {"description": "token,计算方式md5(aid+role_id+time+secret_key)", "type": "string", "example": "token"}}}, "schema.AuthLoginResData": {"type": "object", "properties": {"skey": {"description": "调用API凭证 skey", "type": "string", "example": "skey"}, "user_id": {"description": "用户ID", "type": "integer", "example": 1}}}, "schema.AvatarInfo": {"type": "object", "properties": {"avatar": {"description": "头像ID", "type": "integer", "example": 1}, "avatar_frame": {"description": "头像框", "type": "integer", "example": 1}, "gender": {"description": "性别", "type": "integer", "example": 1}, "job": {"description": "职业", "type": "integer", "example": 1}, "role_id": {"description": "头像角色ID", "type": "integer", "example": 1}}}, "schema.CommentAddReq": {"type": "object", "required": ["content", "role_id", "user_id", "work_id"], "properties": {"content": {"description": "评论内容", "type": "string", "maxLength": 100, "example": "评论内容"}, "reply_comment_id": {"description": "回复评论ID", "type": "integer", "minimum": 0, "example": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scm": {"description": "埋点ID,实验组字段", "type": "string"}, "session_id": {"description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "type": "string"}, "trace_id": {"description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.CommentAddRes": {"type": "object", "properties": {"content": {"description": "评论内容", "type": "string", "example": "评论内容"}, "ctime": {"description": "评论时间", "type": "integer", "example": 1}, "id": {"description": "评论ID", "type": "integer", "example": 1}, "is_liked": {"description": "是否已点赞", "type": "boolean", "example": true}, "like_count": {"description": "点赞数", "type": "integer", "example": 1}, "origin_comment_id": {"description": "原评论ID", "type": "integer", "example": 1}, "reply_comment_id": {"description": "回复评论ID", "type": "integer", "example": 1}, "reply_count": {"description": "回复数", "type": "integer", "example": 1}, "reply_list": {"description": "回复列表，默认返回2个, 只对一级评论有效", "type": "array", "items": {"$ref": "#/definitions/schema.CommentItem"}}, "reply_user": {"description": "回复人，只对二级评论有效", "allOf": [{"$ref": "#/definitions/schema.UserInfo"}]}, "role_id": {"description": "评论人RoleId", "type": "integer", "example": 1}, "user": {"description": "评论人信息", "allOf": [{"$ref": "#/definitions/schema.UserInfo"}]}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.CommentCancelLikeReq": {"type": "object", "required": ["comment_id", "role_id", "user_id"], "properties": {"comment_id": {"description": "评论ID", "type": "integer", "example": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.CommentDelReq": {"type": "object", "required": ["comment_id", "role_id", "user_id"], "properties": {"comment_id": {"description": "评论ID", "type": "integer", "example": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.CommentDelRes": {"type": "object", "properties": {"comment_id": {"description": "评论ID", "type": "integer", "example": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.CommentItem": {"type": "object", "properties": {"content": {"description": "评论内容", "type": "string", "example": "评论内容"}, "ctime": {"description": "评论时间", "type": "integer", "example": 1}, "id": {"description": "评论ID", "type": "integer", "example": 1}, "is_liked": {"description": "是否已点赞", "type": "boolean", "example": true}, "like_count": {"description": "点赞数", "type": "integer", "example": 1}, "origin_comment_id": {"description": "原评论ID", "type": "integer", "example": 1}, "reply_comment_id": {"description": "回复评论ID", "type": "integer", "example": 1}, "reply_count": {"description": "回复数", "type": "integer", "example": 1}, "reply_list": {"description": "回复列表，默认返回2个, 只对一级评论有效", "type": "array", "items": {"$ref": "#/definitions/schema.CommentItem"}}, "reply_user": {"description": "回复人，只对二级评论有效", "allOf": [{"$ref": "#/definitions/schema.UserInfo"}]}, "role_id": {"description": "评论人RoleId", "type": "integer", "example": 1}, "user": {"description": "评论人信息", "allOf": [{"$ref": "#/definitions/schema.UserInfo"}]}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.CommentLikeReq": {"type": "object", "required": ["comment_id", "role_id", "user_id"], "properties": {"comment_id": {"description": "评论ID", "type": "integer", "example": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.CommentListRes": {"type": "object", "properties": {"list": {"description": "评论列表", "type": "array", "items": {"$ref": "#/definitions/schema.CommentItem"}}}}, "schema.CommentReplyListRes": {"type": "object", "properties": {"list": {"description": "回复列表", "type": "array", "items": {"$ref": "#/definitions/schema.CommentItem"}}}}, "schema.CommonFpPassTokenRes": {"type": "object", "properties": {"expires": {"description": "过期时间", "type": "integer", "example": 1634025600}, "project": {"description": "项目", "type": "string", "example": "l36-action"}, "token": {"description": "授权Token", "type": "string", "example": "token"}, "upload_url": {"description": "上传地址", "type": "string", "example": "https://l36-action.fp.ps.netease.com/file/new"}}}, "schema.CommonFpTokenRes": {"type": "object", "properties": {"expires": {"description": "过期时间", "type": "integer", "example": 1634025600}, "project": {"description": "项目", "type": "string", "example": "l36-action"}, "token": {"description": "授权Token", "type": "string", "example": "token"}, "upload_url": {"description": "上传地址", "type": "string", "example": "https://l36-action.fp.ps.netease.com/file/new"}}}, "schema.CommonResData": {"type": "object", "properties": {"is_ok": {"type": "boolean", "example": true}}}, "schema.DesignerCancelFollowReq": {"type": "object", "required": ["designer_id", "role_id", "user_id"], "properties": {"designer_id": {"description": "设计师ID", "type": "integer", "default": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.DesignerDetailRes": {"type": "object", "properties": {"avatar": {"description": "设计师头像", "allOf": [{"$ref": "#/definitions/schema.AvatarInfo"}]}, "designer_level": {"description": "设计师等级", "type": "integer", "example": 1}, "exp": {"description": "经验值", "type": "integer", "example": 1}, "exp_threshold": {"description": "下一等级经验值", "type": "integer", "example": 1}, "fans_count": {"description": "粉丝数", "type": "integer", "example": 1}, "follow_count": {"description": "关注数", "type": "integer", "example": 1}, "id": {"description": "设计师ID", "type": "integer", "example": 1}, "is_follow": {"description": "是否已经关注", "type": "boolean", "example": true}, "liked_count": {"description": "被点赞数", "type": "integer", "example": 1}, "name": {"description": "设计师名称", "type": "string", "example": "设计师名称"}, "role_id": {"description": "和名字对应的角色ID", "type": "integer", "example": 1}, "work_count": {"description": "作品数", "type": "integer", "example": 1}}}, "schema.DesignerFollowReq": {"type": "object", "required": ["designer_id", "role_id", "user_id"], "properties": {"designer_id": {"description": "设计师ID", "type": "integer", "default": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.DesignerInfo": {"type": "object", "properties": {"avatar": {"description": "设计师头像", "allOf": [{"$ref": "#/definitions/schema.AvatarInfo"}]}, "id": {"description": "设计师ID", "type": "integer", "example": 1}, "is_follow": {"description": "是否已经关注", "type": "boolean", "example": true}, "name": {"description": "设计师名称", "type": "string", "example": "设计师名称"}, "role_id": {"description": "和名字对应的角色ID", "type": "integer", "example": 1}}}, "schema.DesignerItem": {"type": "object", "properties": {"avatar": {"description": "设计师头像", "allOf": [{"$ref": "#/definitions/schema.AvatarInfo"}]}, "designer_level": {"description": "设计师等级", "type": "integer", "example": 1}, "fans_count": {"description": "粉丝数", "type": "integer", "example": 1}, "id": {"description": "设计师ID", "type": "integer", "example": 1}, "is_follow": {"description": "是否已经关注", "type": "boolean", "example": true}, "liked_count": {"description": "被点赞数", "type": "integer", "example": 1}, "name": {"description": "设计师名称", "type": "string", "example": "设计师名称"}, "role_id": {"description": "和名字对应的角色ID", "type": "integer", "example": 1}, "work_count": {"description": "作品数", "type": "integer", "example": 1}, "works": {"description": "热门作品", "type": "array", "items": {"$ref": "#/definitions/schema.WorkItem"}}}}, "schema.DesignerListFollowRes": {"type": "object", "properties": {"list": {"description": "设计师列表", "type": "array", "items": {"$ref": "#/definitions/schema.DesignerInfo"}}}}, "schema.DesignerListSearchRes": {"type": "object", "properties": {"list": {"description": "设计师列表", "type": "array", "items": {"$ref": "#/definitions/schema.DesignerItem"}}}}, "schema.DressInfo": {"type": "object", "properties": {"cover": {"description": "封面图", "type": "string", "example": "封面图"}, "fashion_id": {"description": "时装ID", "type": "integer", "example": 1}, "id": {"description": "装扮站作品ID", "type": "integer", "example": 1}, "name": {"description": "装扮站作品名称", "type": "string", "example": "装扮站作品名称"}, "property": {"description": "属性文件", "type": "string", "example": "属性文件"}, "related_fashion_works": {"description": "穿搭数据", "type": "array", "items": {"$ref": "#/definitions/schema.FashionInfo"}}, "role_id": {"description": "作者ID", "type": "integer", "example": 1}, "role_name": {"description": "作者名称", "type": "string", "example": "作者名称"}, "subtype": {"description": "子类别", "type": "integer", "example": 1}, "type": {"description": "类别", "type": "integer", "example": 1}}}, "schema.DressListRecommendRes": {"type": "object", "properties": {"list": {"description": "服饰列表", "type": "array", "items": {"$ref": "#/definitions/schema.DressInfo"}}}}, "schema.FashionInfo": {"type": "object", "properties": {"cover": {"description": "封面图", "type": "string", "example": "封面图"}, "fashion_id": {"description": "时装ID", "type": "integer", "example": 1}, "id": {"description": "装扮站作品ID", "type": "integer", "example": 1}, "name": {"description": "装扮站作品名称", "type": "string", "example": "装扮站作品名称"}, "property": {"description": "属性文件", "type": "string", "example": "属性文件"}, "role_id": {"description": "作者ID", "type": "integer", "example": 1}, "role_name": {"description": "作者名称", "type": "string", "example": "作者名称"}, "subtype": {"description": "子类别", "type": "integer", "example": 1}, "type": {"description": "类别", "type": "integer", "example": 1}}}, "schema.FpReviewCallbackFile": {"type": "object", "required": ["file_id", "from_status", "to_status"], "properties": {"file_id": {"description": "文件id", "type": "string"}, "from_status": {"description": "原始状态", "type": "integer", "enum": [0, 1, 2, 3]}, "to_status": {"description": "当前状态", "type": "integer", "enum": [0, 1, 2, 3]}}}, "schema.GmCommentBanReq": {"type": "object", "required": ["expire_time", "role_id"], "properties": {"expire_time": {"description": "过期时间. 单位: 秒", "type": "integer", "example": 3600}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}}}, "schema.GmCommentDelReq": {"type": "object", "required": ["comment_id"], "properties": {"comment_id": {"description": "评论ID", "type": "integer", "example": 1}}}, "schema.GmCommentItem": {"type": "object", "properties": {"content": {"description": "评论内容", "type": "string", "example": "评论内容"}, "id": {"description": "评论ID", "type": "integer", "example": 1}, "origin_comment_id": {"description": "原始评论ID", "type": "integer", "example": 1}, "role_id": {"description": "角色ID", "type": "integer", "example": 1}}}, "schema.GmCommentListWorkReq": {"type": "object", "required": ["work_id"], "properties": {"work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.GmCommentListWorkRes": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.GmCommentItem"}}}}, "schema.GmCreatorBanReq": {"type": "object", "required": ["expire_time", "role_id"], "properties": {"expire_time": {"description": "过期时间. 单位: 秒", "type": "integer", "example": 3600}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}}}, "schema.GmWorkDelReq": {"type": "object", "required": ["work_id"], "properties": {"work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.GmWorkDetailReq": {"type": "object", "required": ["work_id"], "properties": {"work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.GmWorkListRoleReq": {"type": "object", "required": ["role_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}}}, "schema.GmWorkRecoverReq": {"type": "object", "required": ["work_id"], "properties": {"work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.GmWorkSetVisibilityReq": {"type": "object", "required": ["visibility", "work_id"], "properties": {"visibility": {"type": "integer", "enum": [1, 2]}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.GmWorkUpdateNameReq": {"type": "object", "required": ["name", "work_id"], "properties": {"name": {"type": "string", "maxLength": 20}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.GmWorkUpdateSummaryReq": {"type": "object", "required": ["summary", "work_id"], "properties": {"summary": {"type": "string", "maxLength": 120}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.KafkaSuccessViewWorkActionReq": {"type": "object", "properties": {"role_id": {"description": "角色ID", "type": "integer"}, "scm": {"description": "实验组", "type": "string"}, "server": {"description": "服务器ID", "type": "integer"}, "trace_id": {"description": "追踪ID", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer"}, "work_id": {"description": "作品ID", "type": "integer"}}}, "schema.OpenWorkDetailRes": {"type": "object", "required": ["contents"], "properties": {"Summary": {"description": "作品描述 #xx#表示标签", "type": "string", "example": "作品描述"}, "audit_status": {"description": "审核状态 0=审核通过 2=审核中 3=审核失败", "type": "integer"}, "collect_count": {"description": "收藏数", "type": "integer", "example": 1}, "comment_count": {"description": "评论数", "type": "integer", "example": 1}, "contents": {"description": "内容 1=表情 2=动作 3=语音 4=外观 5=镜头", "type": "array", "items": {"type": "integer"}, "example": [1]}, "cover": {"description": "封面", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "designer": {"description": "设计师", "allOf": [{"$ref": "#/definitions/schema.DesignerInfo"}]}, "hot": {"description": "热度", "type": "integer", "example": 1}, "images": {"description": "图片, 和视频二选一", "type": "array", "items": {"type": "string"}, "example": ["https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"]}, "is_collected": {"description": "是否已收藏", "type": "boolean", "example": true}, "is_liked": {"description": "是否已点赞", "type": "boolean", "example": true}, "like_count": {"description": "点赞数", "type": "integer", "example": 1}, "name": {"description": "作品名称", "type": "string", "example": "作品名称"}, "property": {"description": "属性文件Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "resample_url": {"description": "重采样Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "role_id": {"description": "上传时的角色ID", "type": "integer", "example": 1}, "scene": {"description": "场景", "type": "integer", "example": 1}, "scm": {"description": "Scm 实验组字段", "type": "string", "example": "scm"}, "trace_id": {"description": "TraceId", "type": "string", "example": "trace_id"}, "type": {"description": "作品类型 1=模型 2=视频", "type": "integer", "example": 1}, "video": {"description": "视频", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "visibility": {"description": "可见性 1=所有人可见 2=仅自己可见", "type": "integer", "example": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.PaddingItem": {"type": "object", "properties": {"alarmThreshold": {"description": "报警阈值", "type": "integer"}, "cdMinute": {"description": "数据增长cd, 每x分钟增长一次", "type": "integer"}, "gteMaxRatio": {"description": "最大增长比例", "type": "integer"}, "gteMinRatio": {"description": "最小增长比例", "type": "integer"}, "gtePercent": {"description": "最高增长百分比(注水上限)", "type": "integer"}, "gteThresholdsMax": {"description": "注水阈值上限", "type": "integer"}, "gteThresholdsMin": {"description": "注水阈值下限", "type": "integer"}, "minRealThreshold": {"description": "最小真实阈值", "type": "integer"}, "openStatus": {"description": "0: 关闭 1: 开启", "type": "integer"}, "releaseHour": {"description": "释放时间", "type": "integer"}}}, "schema.PaddingResItem": {"type": "object", "properties": {"allCount": {"description": "总数量", "type": "integer"}, "getThresholdsMax": {"description": "注水阈值上限", "type": "integer"}, "getThresholdsMin": {"description": "注水阈值下限", "type": "integer"}, "paddingCount": {"description": "注水数量", "type": "integer"}, "realCount": {"description": "真实数量", "type": "integer"}, "workCreateTime": {"description": "作品创建时间", "type": "integer"}, "workId": {"description": "作品id", "type": "integer"}, "workName": {"description": "作品名称", "type": "string"}}}, "schema.RankDesignerExpRes": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.RankDesignerItem"}}, "self": {"$ref": "#/definitions/schema.RankDesignerItem"}}}, "schema.RankDesignerItem": {"type": "object", "properties": {"designer_level": {"type": "integer"}, "exp": {"type": "integer"}, "gender": {"type": "integer"}, "job": {"type": "integer"}, "level": {"type": "integer"}, "rank": {"type": "integer"}, "role_id": {"type": "integer"}, "role_name": {"type": "string"}}}, "schema.RankWorkHotRes": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.RankWorkItem"}}}}, "schema.RankWorkItem": {"type": "object", "properties": {"designer": {"$ref": "#/definitions/schema.DesignerInfo"}, "hot": {"type": "integer"}, "images": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "rank": {"type": "integer"}, "summary": {"type": "string"}, "video": {"type": "string"}, "work_id": {"type": "integer"}}}, "schema.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "schema.ServerDesignerDetailRes": {"type": "object", "properties": {"avatar": {"description": "设计师头像", "allOf": [{"$ref": "#/definitions/schema.AvatarInfo"}]}, "designer_level": {"description": "设计师等级", "type": "integer", "example": 1}, "exp": {"description": "经验值", "type": "integer", "example": 1}, "exp_threshold": {"description": "下一等级经验值", "type": "integer", "example": 1}, "fans_count": {"description": "粉丝数", "type": "integer", "example": 1}, "follow_count": {"description": "关注数", "type": "integer", "example": 1}, "id": {"description": "设计师ID", "type": "integer", "example": 1}, "is_follow": {"description": "是否已经关注", "type": "boolean", "example": true}, "liked_count": {"description": "被点赞数", "type": "integer", "example": 1}, "name": {"description": "设计师名称", "type": "string", "example": "设计师名称"}, "role_id": {"description": "和名字对应的角色ID", "type": "integer", "example": 1}, "work_count": {"description": "作品数", "type": "integer", "example": 1}}}, "schema.ServerFpReviewCallbackReq": {"type": "object", "required": ["files", "operator", "project_name"], "properties": {"files": {"description": "文件列表", "type": "array", "items": {"$ref": "#/definitions/schema.FpReviewCallbackFile"}}, "operator": {"description": "操作者", "type": "string"}, "project_name": {"description": "项目名称", "type": "string"}}}, "schema.ServerPaddingTriggerReq": {"type": "object", "properties": {"collectCfg": {"$ref": "#/definitions/schema.PaddingItem"}, "commentCfg": {"$ref": "#/definitions/schema.PaddingItem"}, "likeCfg": {"$ref": "#/definitions/schema.PaddingItem"}}}, "schema.ServerPaddingTriggerRes": {"type": "object", "properties": {"collectData": {"$ref": "#/definitions/schema.PaddingResItem"}, "commentData": {"$ref": "#/definitions/schema.PaddingResItem"}, "likeData": {"$ref": "#/definitions/schema.PaddingResItem"}}}, "schema.SetFakeTimeReq": {"type": "object", "required": ["fake_time"], "properties": {"fake_time": {"description": "伪造时间 支持 +1h, -1h, +1d, -1d, +1w, -1w, +1M, -1M, +1y, -1y,也支持时间 \"2006-01-02 15:04:05\"", "type": "string", "example": "2024-11-01 00:00:00"}}}, "schema.TopicItem": {"type": "object", "properties": {"id": {"description": "话题ID", "type": "integer", "example": 1}, "name": {"description": "话题名称", "type": "string", "example": "话题名称"}}}, "schema.TopicListRecommendRes": {"type": "object", "properties": {"list": {"description": "话题列表", "type": "array", "items": {"$ref": "#/definitions/schema.TopicItem"}}}}, "schema.TopicListRes": {"type": "object", "properties": {"list": {"description": "话题列表", "type": "array", "items": {"$ref": "#/definitions/schema.TopicItem"}}}}, "schema.TopicSearchRes": {"type": "object", "properties": {"list": {"description": "话题列表", "type": "array", "items": {"$ref": "#/definitions/schema.TopicItem"}}}}, "schema.UserAvatarInfo": {"type": "object", "properties": {"avatar": {"description": "头像ID", "type": "integer", "example": 1}, "avatar_frame": {"description": "头像框", "type": "integer", "example": 1}, "gender": {"description": "性别", "type": "integer", "example": 1}, "is_default": {"description": "是否是默认头像", "type": "boolean", "example": true}, "job": {"description": "职业", "type": "integer", "example": 1}, "role_id": {"description": "头像角色ID", "type": "integer", "example": 1}}}, "schema.UserAvatarListRes": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.UserAvatarInfo"}}}}, "schema.UserAvatarUpdateReq": {"type": "object", "required": ["avatar_role_id", "role_id", "user_id"], "properties": {"avatar_role_id": {"description": "头像角色ID", "type": "integer", "example": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.UserInfo": {"type": "object", "properties": {"avatar": {"description": "用户头像", "allOf": [{"$ref": "#/definitions/schema.AvatarInfo"}]}, "id": {"description": "用户ID", "type": "integer", "example": 1}, "name": {"description": "用户昵称", "type": "string", "example": "昵称"}, "server_id": {"description": "服务器ID", "type": "integer", "example": 1}}}, "schema.UserNameInfo": {"type": "object", "properties": {"is_default": {"type": "boolean", "example": true}, "role_id": {"type": "integer", "example": 1}, "role_name": {"type": "string", "example": "角色名称"}}}, "schema.UserNameListRes": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/schema.UserNameInfo"}}}}, "schema.UserUpdateReq": {"type": "object", "required": ["name_role_id", "role_id", "user_id"], "properties": {"name_role_id": {"description": "昵称", "type": "integer", "example": 1}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}}}, "schema.WorkCancelCollectReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkCancelLikeReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkCollectReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scm": {"description": "埋点ID,实验组字段", "type": "string"}, "session_id": {"description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "type": "string"}, "trace_id": {"description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkCompletePlayReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scm": {"description": "埋点ID,实验组字段", "type": "string"}, "session_id": {"description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "type": "string"}, "trace_id": {"description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkCreateReq": {"type": "object", "required": ["cover", "name", "role_id", "scene", "summary", "type", "user_id", "visibility"], "properties": {"contents": {"description": "内容 1=表情 2=动作 3=语音 4=外观 5=镜头", "type": "array", "items": {"type": "integer"}, "example": [1]}, "cover": {"description": "封面", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "gender": {"description": "上传人性别 0=男 1=女", "type": "integer", "enum": [0, 1], "example": 0}, "images": {"description": "图片, 和视频二选一", "type": "array", "items": {"type": "string"}, "example": ["https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"]}, "name": {"description": "作品名称", "type": "string", "maxLength": 14, "example": "作品名称"}, "property": {"description": "属性文件Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "resample_url": {"description": "重采样Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scene": {"description": "场景", "type": "integer", "example": 1}, "summary": {"description": "作品描述 #xx#表示标签", "type": "string", "maxLength": 120, "example": "作品描述"}, "type": {"description": "类型 1=模型 2=动作视频 3=纯视频", "type": "integer", "enum": [1, 2, 3], "example": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "video": {"description": "视频，type=2时必填", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "visibility": {"description": "可见性 1=所有人可见 2=仅自己可见", "type": "integer", "enum": [1, 2], "example": 1}}}, "schema.WorkCreateRes": {"type": "object", "properties": {"work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkDelReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkDetailRes": {"type": "object", "required": ["contents"], "properties": {"Summary": {"description": "作品描述 #xx#表示标签", "type": "string", "example": "作品描述"}, "audit_status": {"description": "审核状态 0=审核通过 2=审核中 3=审核失败", "type": "integer"}, "collect_count": {"description": "收藏数", "type": "integer", "example": 1}, "comment_count": {"description": "评论数", "type": "integer", "example": 1}, "contents": {"description": "内容 1=表情 2=动作 3=语音 4=外观 5=镜头", "type": "array", "items": {"type": "integer"}, "example": [1]}, "cover": {"description": "封面", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "designer": {"description": "设计师", "allOf": [{"$ref": "#/definitions/schema.DesignerInfo"}]}, "hot": {"description": "热度", "type": "integer", "example": 1}, "images": {"description": "图片, 和视频二选一", "type": "array", "items": {"type": "string"}, "example": ["https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"]}, "is_collected": {"description": "是否已收藏", "type": "boolean", "example": true}, "is_liked": {"description": "是否已点赞", "type": "boolean", "example": true}, "like_count": {"description": "点赞数", "type": "integer", "example": 1}, "name": {"description": "作品名称", "type": "string", "example": "作品名称"}, "property": {"description": "属性文件Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "resample_url": {"description": "重采样Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "role_id": {"description": "上传时的角色ID", "type": "integer", "example": 1}, "scene": {"description": "场景", "type": "integer", "example": 1}, "scm": {"description": "Scm 实验组字段", "type": "string", "example": "scm"}, "trace_id": {"description": "TraceId", "type": "string", "example": "trace_id"}, "type": {"description": "作品类型 1=模型 2=视频", "type": "integer", "example": 1}, "video": {"description": "视频", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "visibility": {"description": "可见性 1=所有人可见 2=仅自己可见", "type": "integer", "example": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkEffectShareReq": {"type": "object", "required": ["role_id", "share_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "share_id": {"description": "分享ID", "type": "integer", "example": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkItem": {"type": "object", "required": ["contents"], "properties": {"Summary": {"description": "作品描述 #xx#表示标签", "type": "string", "example": "作品描述"}, "audit_status": {"description": "审核状态 0=审核通过 2=审核中 3=审核失败", "type": "integer"}, "collect_count": {"description": "收藏数", "type": "integer", "example": 1}, "comment_count": {"description": "评论数", "type": "integer", "example": 1}, "contents": {"description": "内容 1=表情 2=动作 3=语音 4=外观 5=镜头", "type": "array", "items": {"type": "integer"}, "example": [1]}, "cover": {"description": "封面", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "designer": {"description": "设计师", "allOf": [{"$ref": "#/definitions/schema.DesignerInfo"}]}, "hot": {"description": "热度", "type": "integer", "example": 1}, "images": {"description": "图片, 和视频二选一", "type": "array", "items": {"type": "string"}, "example": ["https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"]}, "is_collected": {"description": "是否已收藏", "type": "boolean", "example": true}, "is_liked": {"description": "是否已点赞", "type": "boolean", "example": true}, "like_count": {"description": "点赞数", "type": "integer", "example": 1}, "name": {"description": "作品名称", "type": "string", "example": "作品名称"}, "property": {"description": "属性文件Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "resample_url": {"description": "重采样Url", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "role_id": {"description": "上传时的角色ID", "type": "integer", "example": 1}, "scene": {"description": "场景", "type": "integer", "example": 1}, "scm": {"description": "Scm 实验组字段", "type": "string", "example": "scm"}, "trace_id": {"description": "TraceId", "type": "string", "example": "trace_id"}, "type": {"description": "作品类型 1=模型 2=视频", "type": "integer", "example": 1}, "video": {"description": "视频", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "visibility": {"description": "可见性 1=所有人可见 2=仅自己可见", "type": "integer", "example": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkLikeReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scm": {"description": "埋点ID,实验组字段", "type": "string"}, "session_id": {"description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "type": "string"}, "trace_id": {"description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkListRes": {"type": "object", "properties": {"list": {"description": "作品列表", "type": "array", "items": {"$ref": "#/definitions/schema.WorkItem"}}}}, "schema.WorkShareReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkShareRes": {"type": "object", "properties": {"share_id": {"description": "分享ID", "type": "integer", "example": 1}}}, "schema.WorkTraceReq": {"type": "object", "required": ["bhv_type", "role_id", "user_id", "work_id"], "properties": {"bhv_type": {"description": "行为类型 tip=打赏 report=举报 remake=拍同款 tab=切换tab search=搜索", "type": "string", "enum": ["tip", "report", "remake", "tab", "search"], "example": "1"}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scm": {"description": "埋点ID,实验组字段", "type": "string"}, "session_id": {"description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "type": "string"}, "trace_id": {"description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkUpdateReq": {"type": "object", "required": ["Summary", "name", "role_id", "scene", "type", "user_id", "visibility", "work_id"], "properties": {"Summary": {"description": "作品描述 #xx#表示标签", "type": "string", "maxLength": 120, "example": "作品描述"}, "cover": {"description": "封面", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "images": {"description": "图片, 和视频二选一", "type": "array", "items": {"type": "string"}, "example": ["https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"]}, "name": {"description": "作品名称", "type": "string", "maxLength": 20, "example": "作品名称"}, "role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scene": {"description": "场景", "type": "integer", "example": 1}, "type": {"description": "类型", "type": "integer", "enum": [1, 2, 3], "example": 1}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "video": {"description": "视频", "type": "string", "example": "https://l36-pyq.fp.ps.netease.com/file/6776614ee0299ed117b661d0cIVuuiLI06"}, "visibility": {"description": "可见性 1=所有人可见 2=仅自己可见", "type": "integer", "enum": [1, 2], "example": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkUpdateRes": {"type": "object", "properties": {"work_id": {"description": "作品ID", "type": "integer", "example": 1}}}, "schema.WorkUseReq": {"type": "object", "required": ["role_id", "user_id", "work_id"], "properties": {"role_id": {"description": "角色Id", "type": "integer", "default": 1}, "scm": {"description": "埋点ID,实验组字段", "type": "string"}, "session_id": {"description": "链路追踪，每次打开推荐瀑布流是相同的sessionid，打开多次请求时是不同的traceid，生成方式role_id+时间戳", "type": "string"}, "trace_id": {"description": "埋点ID，用户请求的唯一标识（需要保证traceid全局唯一，每次请求都不一样），可以是role_id+时间戳+四位随机数，主要是将用户的该次请求下的行为关联起来，曝光、点击、点赞等行为", "type": "string"}, "user_id": {"description": "用户ID", "type": "integer", "default": 1}, "work_id": {"description": "作品ID", "type": "integer", "example": 1}}}}, "securityDefinitions": {"fpAuth": {"description": "filepicker回调token", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-fp-token", "in": "header"}, "skeyAuth": {"description": "用户登录后获取的skey", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "skey", "in": "header"}}}