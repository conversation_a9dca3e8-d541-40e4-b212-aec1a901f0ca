package util

import (
	"crypto/md5"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"app/config"

	yunyinglog "ccc-gitlab.leihuo.netease.com/pkgo/yunying-log"
)

func GetRandomIntN(n int) int {
	if n <= 0 {
		return 0
	}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return r.Intn(n)
}

func GetRandomFloat64(min, max float64) float64 {
	if min >= max {
		return min
	}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return min + r.Float64()*(max-min)
}

func GetRandomElements[T any](arr []T, num int) []T {
	if num >= len(arr) {
		return arr
	}
	// 复制一份，避免修改原数组
	data := make([]T, len(arr))
	copy(data, arr)

	// 随机交换元素
	for i := len(data) - 1; i > len(data)-num-1; i-- {
		j := GetRandomIntN(i + 1)
		data[i], data[j] = data[j], data[i]
	}

	return data[len(data)-num:]
}

func GetHalfHourAgo() int64 {
	now := time.Now()
	rounded := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())
	rounded = rounded.Add(-30 * time.Minute)
	if rounded.Minute() >= 30 {
		rounded = time.Date(rounded.Year(), rounded.Month(), rounded.Day(), rounded.Hour(), 30, 0, 0, rounded.Location())
	} else {
		rounded = time.Date(rounded.Year(), rounded.Month(), rounded.Day(), rounded.Hour(), 0, 0, 0, rounded.Location())
	}
	return rounded.UnixMilli()
}

func GetTodayStart() int64 {
	now := time.Now()
	rounded := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	return rounded.UnixMilli()
}

func UnixMilliToDate(unixMill int64) string {
	return time.UnixMilli(unixMill).Format(time.DateOnly)
}

// LogYunying 记录运营日志
func LogYunying(event string, data map[string]interface{}) {
	if config.C.Test.TestEnv {
		data["env"] = "test"
	} else {
		data["env"] = "release"
	}
	yunyinglog.Log(event, data)
}

// Md5 计算字符串的 MD5 值
func Md5(str string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(str)))
}

func ReplaceLineBreak(str string) string {
	return strings.ReplaceAll(str, "\n", " ")
}
