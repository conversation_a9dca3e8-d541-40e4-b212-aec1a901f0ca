package util

import (
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
)

func LowercaseFirstLetter(str string) string {
	if len(str) == 0 {
		return ""
	}
	return strings.ToLower(str[0:1]) + str[1:]
}

func CamelToSnake(str string) string {
	var result string
	for i, v := range str {
		if 'A' <= v && v <= 'Z' {
			if i != 0 {
				result += "_"
			}
			result += string(v + 32)
		} else {
			result += string(v)
		}
	}
	return result
}

func CsvStringToSlice(str string) []string {
	if str == "" {
		return []string{}
	}
	return strings.Split(str, ",")
}

func SliceToCsvString(slice []string) string {
	return strings.Join(slice, ",")
}

func CsvStringToIntSlice(str string) ([]int, error) {
	if str == "" {
		return []int{}, nil
	}
	var result []int
	for _, s := range strings.Split(str, ",") {
		v, err := strconv.Atoi(s)
		if err != nil {
			return []int{}, err
		}
		result = append(result, v)
	}
	return result, nil
}

func IntSliceToDigit(slice []int) int {
	var result int
	for _, s := range slice {
		result |= 1 << (s - 1)
	}
	return result
}

func IntArrayToJson[T ~int](arr []T) string {
	bytes, _ := sonic.Marshal(arr)
	return string(bytes)
}

func JsonToIntArray(str string) []int {
	var intArr []int
	if err := sonic.Unmarshal([]byte(str), &intArr); err != nil {
		return []int{}
	}
	return intArr
}

func JsonToStringArray(str string) []string {
	var strArr []string
	if err := sonic.Unmarshal([]byte(str), &strArr); err != nil {
		return []string{}
	}
	return strArr
}

func StringArrayToJson(arr []string) string {
	bytes, _ := sonic.Marshal(arr)
	return string(bytes)
}

func StructToJson[T any](s T) string {
	bytes, _ := sonic.Marshal(s)
	return string(bytes)
}

const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func RandString(n int) string {
	rand.Seed(time.Now().UnixNano())
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}
