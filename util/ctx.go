package util

import (
	"strconv"
	"time"

	"app/config"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

const (
	GameServerTimeKey = "_game_server_time"
)

func Bind(ctx *gin.Context, data interface{}) (e error) {
	if ctx.Request.Method == "POST" {
		e = ctx.ShouldBindBodyWith(data, binding.JSON)
	} else {
		e = ctx.ShouldBind(data)
	}
	if e != nil {
		elog.WithContext(ctx).WithError(e).WithField("data", data).Warn("Bind")
	}
	if e == nil {
		ctx.Set("params", data)
	}
	return
}

func GetParamsFromContext(ctx *gin.Context) any {
	params, _ := ctx.Get("params")
	return params
}

func SetCtxRoleId(ctx *gin.Context, roleId int64) {
	ctx.Set("role_id", roleId)
}

func GetRoleIdFromCtx(ctx *gin.Context) int64 {
	// 从上下文中获取用户id
	userId, exist := ctx.Get("role_id")
	if exist {
		return userId.(int64)
	}
	return 0
}

func BindForGm(ctx *gin.Context, data interface{}) (e error) {
	e = ctx.ShouldBindQuery(data)
	if e != nil {
		e = ctx.ShouldBind(data)
		if e != nil {
			elog.WithContext(ctx).WithError(e).WithField("data", data).Warn("BindForGm")
		}
	}
	if e == nil {
		ctx.Set("params", data)
	}
	return
}

func SetGameServerTime(ctx *gin.Context) {
	if config.C.Test.TestEnv {
		return
	}
	remoteServerTime := ctx.GetHeader("x-nshm-server-time")
	if remoteServerTime == "" {
		return
	}
	remoteServerTimeInt, err := strconv.ParseInt(remoteServerTime, 10, 64)
	if err != nil {
		return
	}
	ctx.Set(GameServerTimeKey, remoteServerTimeInt)
}

func GetGameServerTime(ctx *gin.Context) int64 {
	now := time.Now().UnixMilli()
	if !config.C.Test.TestEnv {
		return now
	}
	remoteServerTime, ok := ctx.Get(GameServerTimeKey)
	if remoteServerTime == nil || !ok {
		return now
	}
	if remoteServerTimeInt, ok := remoteServerTime.(int64); ok {
		return remoteServerTimeInt * 1000
	}
	return now
}
