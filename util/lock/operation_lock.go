package lock

import (
	"context"
	"time"

	"app/config"

	"github.com/redis/go-redis/v9"
)

type OperationLock struct {
	key string
	rds redis.UniversalClient
}

func NewOperationLock(rds redis.UniversalClient, key string) *OperationLock {
	return &OperationLock{key: key, rds: rds}
}

func (l *OperationLock) GetKey() string {
	return config.C.Redis.Prefix + l.key
}

func (l *OperationLock) Lock(ctx context.Context, duration time.Duration) (bool, error) {
	return l.rds.SetNX(ctx, l.<PERSON>ey(), "1", duration).Result()
}

func (l *OperationLock) Unlock(ctx context.Context) error {
	return l.rds.Del(ctx, l.<PERSON>()).Err()
}
