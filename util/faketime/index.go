package faketime

import (
	"os"
	"time"

	sm "github.com/cch123/supermonkey"
)

var (
	patchDuration time.Duration = 0
	initialized   bool          = false
)

func updatePatchDuration() {
	faketime := os.Getenv("FAKETIME")
	if faketime == "" {
		return
	}

	duration, err := time.ParseDuration(faketime)
	if err == nil {
		patchDuration = duration
		return
	}

	patchTime := os.Getenv("PATCHTIME")
	if patchTime == "" {
		patchTime = _now().Format(time.DateTime)
	}

	pt, err := time.ParseInLocation(time.DateTime, patchTime, time.Local)
	if err != nil {
		pt = _now()
	}

	ft, err := time.ParseInLocation(time.DateTime, faketime, time.Local)
	if err != nil {
		ft = _now()
	}

	patchDuration = ft.Sub(pt)
}

func patchNow() time.Time {
	return _now().Add(patchDuration)
}

func Init() {
	if !initialized {
		updatePatchDuration()
		sm.Patch(time.Now, patchNow)
		initialized = true
	}
}

func SetFakeTime(faketime string) {
	os.Setenv("FAKETIME", faketime)
	os.Setenv("PATCHTIME", _now().Format(time.DateTime))
	updatePatchDuration()
}
