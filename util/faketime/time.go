package faketime

import (
	"time"
	"unsafe"
)

// 主要是这两个私有方法导出来

//go:linkname runtimeNano runtime.nanotime
func runtimeNano() int64

//go:linkname now time.now
func now() (sec int64, nsec int32, mono int64)

var startNano = runtimeNano() - 1

type _time struct {
	wall uint64
	ext  int64
	loc  *time.Location
}

const (
	secondsPerMinute = 60
	secondsPerHour   = 60 * secondsPerMinute
	secondsPerDay    = 24 * secondsPerHour
)

const (
	unixToInternal int64 = (1969*365 + 1969/4 - 1969/100 + 1969/400) * secondsPerDay
	wallToInternal int64 = (1884*365 + 1884/4 - 1884/100 + 1884/400) * secondsPerDay
)

const (
	hasMonotonic = 1 << 63
	minWall      = wallToInternal // year 1885
	nsecShift    = 30
)

func _now() time.Time {
	sec, nsec, mono := now()
	mono -= startNano
	sec += unixToInternal - minWall
	var t *_time
	if uint64(sec)>>33 != 0 {
		t = &_time{uint64(nsec), sec + minWall, time.Local}
	} else {
		t = &_time{hasMonotonic | uint64(sec)<<nsecShift | uint64(nsec), mono, time.Local}
	}
	return *(*time.Time)(unsafe.Pointer(t))
}
