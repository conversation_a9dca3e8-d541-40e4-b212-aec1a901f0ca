package log

import (
	"context"
	"fmt"
	"strings"
	"time"

	"app/config"
	"app/conn"
	"app/util"
	"app/util/lock"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
)

type PopoLoggerHook struct{}

func (h *PopoLoggerHook) Levels() []logrus.Level {
	return []logrus.Level{logrus.ErrorLevel, logrus.FatalLevel}
}

func (h *PopoLoggerHook) Fire(entry *logrus.Entry) error {
	// 组装告警信息
	data := entry.Data

	dataContent := ""
	if method, ok := data["method"].(string); ok {
		if uri, ok := data["uri"].(string); ok {
			dataContent = fmt.Sprintf("%s\napi:\t[%s]%s", dataContent, method, uri)
		}
	}
	if ip, ok := data["client_ip"].(string); ok {
		dataContent = fmt.Sprintf("%s\nclient_ip:\t%s", dataContent, ip)
	}
	if id, ok := data["trace_id"].(string); ok {
		dataContent = fmt.Sprintf("%s\ntrace_id:\t%s", dataContent, id)
	}
	if err, ok := data[logrus.ErrorKey].(error); ok {
		dataContent = fmt.Sprintf("%s\nerror:\t%s", dataContent, err.Error())
	}
	if body, ok := data["body"]; ok {
		dataContent = fmt.Sprintf("%s\nbody:\t%v", dataContent, body)
	}
	if params, ok := data["params"]; ok {
		dataContent = fmt.Sprintf("%s\nparams:\t%v", dataContent, params)
	}

	message := fmt.Sprintf("\ntime:\t%s\nmsg:\t[%s]%s", entry.Time.Format(time.DateTime), entry.Level.String(), entry.Message)
	if dataContent != "" {
		message = fmt.Sprintf("%s%s", message, dataContent)
	}

	msg := entry.Message
	rds := conn.GetRedisConn()
	popo := conn.GetPopo()
	go func() {
		msgLock := lock.NewOperationLock(rds, msg)
		locked, err := msgLock.Lock(context.Background(), time.Minute*5)
		if err != nil || !locked {
			return
		}

		if traceId, ok := data["trace_id"].(string); ok {
			// 同一个 traceId 只处罚一次推送
			traceLock := lock.NewOperationLock(rds, traceId)
			locked, err := traceLock.Lock(context.Background(), time.Minute*5)
			if err != nil || !locked {
				return
			}
		}
		// 对 msg 进行限流
		if config.C.App.Maintainer != nil && len(config.C.App.Maintainer) > 0 {
			atUsers := lo.Filter(config.C.App.Maintainer, func(item string, index int) bool {
				return strings.Contains(item, "@")
			})
			for _, maintainer := range config.C.App.Maintainer {
				popo.SendInfoMessage(message, maintainer, false, atUsers)
			}
		}
	}()
	return nil
}

func NewPopoLoggerHook() *PopoLoggerHook {
	return &PopoLoggerHook{}
}

// LogWithContext 业务通用日志entry
// 按照业务的基本特征，将基础属性放在Context中并记录日志时进行统一记录处理
func LogWithContext(ctx context.Context) *elog.Entry {
	entry := elog.WithContext(ctx)
	// 如果是 gin.Context 则获取 params
	if ginCtx, ok := ctx.(*gin.Context); ok {
		if params := util.GetParamsFromContext(ginCtx); params != nil {
			entry = entry.WithField("params", params)
		}
	}
	return entry
}

func LogError(ctx context.Context, err error, params ...any) *elog.Entry {
	entry := elog.WithContext(ctx).WithError(err)
	if len(params) > 0 {
		entry = entry.WithField("params", params)
	}
	return entry
}

// LogWithCtxContext 业务通用日志entry
// 按照业务的基本特征，将基础属性放在Context中并记录日志时进行统一记录处理
func LogWithCtxContext(ctx context.Context) *elog.Entry {
	entry := elog.WithContext(ctx)
	return entry
}
