package yidun

import (
	"context"
	"fmt"
	"time"

	"app/config"
	"app/dao/cache"
	"app/util"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	v5 "github.com/yidun/yidun-golang-sdk/yidun/service/antispam/text"
	"github.com/yidun/yidun-golang-sdk/yidun/service/antispam/text/v5/check/sync/single"
	"golang.org/x/sync/singleflight"
)

type TextCheckSignalParams struct {
	DataId  string
	Content string
}

type CheckSuggestion int

var (
	CheckSuggestionFailed    CheckSuggestion = -1 // 接口请求失败
	CheckSuggestionPass      CheckSuggestion = 0
	CheckSuggestionSuspected CheckSuggestion = 1
	CheckSuggestionBlock     CheckSuggestion = 2
)

func TextCheckSingle(ctx context.Context, params TextCheckSignalParams) CheckSuggestion {
	defer util.TimeCost("TextCheckSingleForYiDun")()
	request := single.NewTextCheckRequest(config.C.YiDun.BusinessId)
	textCheckClient := v5.NewTextClientWithAccessKey(config.C.YiDun.SecretId, config.C.YiDun.SecretKey)

	request.SetDataID(fmt.Sprint(params.DataId))
	request.SetContent(params.Content)
	/** 100：色情，200：广告，260：广告法，300：暴恐，400：违禁，500：涉政，600：谩骂，700：灌水，900：其他，1100：涉价值观*/
	request.SetCheckLabels("100,200,400,500,600,900,1100")

	res, err := textCheckClient.SyncCheckText(request)
	if err != nil {
		elog.WithContext(ctx).WithField("err", err.Error()).Error("TextCheckSignal error")
		return CheckSuggestionFailed
	}

	if res.GetCode() == 200 {
		data := res.Result.Antispam
		suggestion := *data.Suggestion
		return CheckSuggestion(suggestion)
	} else {
		elog.WithContext(ctx).WithField("resCode", res.GetCode()).WithField("resMsg", res.GetMsg()).Error("TextCheckSignal res error")
	}
	return CheckSuggestionFailed
}

var sf singleflight.Group = singleflight.Group{}

func TextCheckSignalWithLRuCache(ctx context.Context, params TextCheckSignalParams) CheckSuggestion {
	key := fmt.Sprintf("TextCheckSignalWithLRuCache:%s", params.Content)
	if record, ok := cache.GlobalLRUCache.Get(key); ok {
		return record.(CheckSuggestion)
	} else {
		record, _, _ = sf.Do(key, func() (interface{}, error) {
			return TextCheckSingle(ctx, params), nil
		})
		sf.Forget(key)
		cache.GlobalLRUCache.Add(key, record, time.Minute)
		return record.(CheckSuggestion)
	}
}
