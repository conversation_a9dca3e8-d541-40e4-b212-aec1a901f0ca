package util

func AssertToArray(arr interface{}) []interface{} {
	if arr == nil {
		return []interface{}{}
	}
	list := []interface{}{}
	if a, ok := arr.([]int); ok {
		for _, v := range a {
			list = append(list, v)
		}
		return list
	}
	if a, ok := arr.([]int32); ok {
		for _, v := range a {
			list = append(list, v)
		}
		return list
	}
	if a, ok := arr.([]int64); ok {
		for _, v := range a {
			list = append(list, v)
		}
		return list
	}
	if a, ok := arr.([]string); ok {
		for _, v := range a {
			list = append(list, v)
		}
		return list
	}
	return []interface{}{arr}
}
