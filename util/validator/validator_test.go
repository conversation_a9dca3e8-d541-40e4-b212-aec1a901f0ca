package validator

import (
	"testing"

	"app/config"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"
)

type TestStruct struct {
	URL string `validate:"fp_url"`
}

func TestFpUrl(t *testing.T) {
	validate := validator.New()
	_ = validate.RegisterValidation("fp_url", fpUrl)

	tests := []struct {
		name    string
		url     string
		wantErr bool
	}{
		{
			name:    "empty string should pass",
			url:     "",
			wantErr: false,
		},
		{
			name:    "valid fp url should pass",
			url:     "https://l36.fp.ps.netease.com/file/abc123",
			wantErr: false,
		},
		{
			name:    "valid fp url with subdomain should pass",
			url:     "https://l36-1.fp.ps.netease.com/file/xyz789",
			wantErr: false,
		},
		{
			name:    "invalid url should fail",
			url:     "https://invalid.com/file/123",
			wantErr: true,
		},
		{
			name:    "malformed url should fail",
			url:     "not-a-url",
			wantErr: true,
		},
	}

	// 测试 SkipAudit = false 的情况
	config.C.Filepicker.SkipAudit = false
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			test := TestStruct{
				URL: tt.url,
			}
			err := validate.Struct(test)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}

	// 测试 SkipAudit = true 的情况
	config.C.Filepicker.SkipAudit = true
	t.Run("all urls should pass when SkipAudit is true", func(t *testing.T) {
		for _, tt := range tests {
			test := TestStruct{
				URL: tt.url,
			}
			err := validate.Struct(test)
			assert.NoError(t, err)
		}
	})
}
