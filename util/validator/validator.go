package validator

import (
	"regexp"

	"app/config"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

var fpUrl validator.Func = func(fl validator.FieldLevel) bool {
	params := fl.Field().String()
	if params == "" {
		return true
	}
	if config.C.Filepicker.SkipAudit {
		return true
	}
	// 支持 http 和 https 协议
	re := regexp.MustCompile(`https?:\/\/l36.*\.fp\.ps\.netease\.com\/file\/\w+`)
	return re.MatchString(params)
}

var replySort validator.Func = func(fl validator.FieldLevel) bool {
	params := fl.Field().String()
	if params == "" {
		return true
	}
	return params == "hot" || params == "new" || params == "mine"
}

var workOrder validator.Func = func(fl validator.FieldLevel) bool {
	params := fl.Field().String()
	if params == "" {
		return true
	}
	return params == "asc" || params == "desc"
}

func init() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		_ = v.RegisterValidation("fp_url", fpUrl)
		_ = v.RegisterValidation("reply_sort", replySort)
		_ = v.RegisterValidation("work_order", workOrder)
	}
}
