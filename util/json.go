package util

import (
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/bytedance/sonic"
)

func JsonStringify(v interface{}) string {
	bytes, err := sonic.Marshal(v)
	if err != nil {
		return ""
	}
	return string(bytes)
}

func JsonParseWithDefaultValue[T any](str string, defaultValue *T) T {
	if str == "null" || str == "" {
		return *defaultValue
	}
	err := sonic.Unmarshal([]byte(str), &defaultValue)
	if err != nil {
		elog.WithField("err", err.<PERSON>rror()).Error("JsonParseWithDefaultValue")
	}
	return *defaultValue
}

func JsonParseStrArray(str string) []string {
	arr := make([]string, 0)
	if str == "" || str == "null" {
		return arr
	}

	err := sonic.Unmarshal([]byte(str), &arr)
	if err != nil {
		return arr
	}
	return arr
}
