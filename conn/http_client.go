package conn

import (
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"app/config"
)

var httpClientMap = make(map[string]*http.Client)

func GetClient(kl ...string) *http.Client {
	key := HttpClient1
	if len(kl) > 0 {
		key = kl[0]
	}

	client, exists := httpClientMap[key]
	if !exists {
		fmt.Println(fmt.Errorf("http client %s not inited", key))
		return nil
	}
	return client
}

const HttpClient1 = "hc1"

var hc1Once = sync.Once{}

func InitHttpClient1(conf *config.HttpClientConfig) {
	hc1Once.Do(func() {
		client := &http.Client{
			Timeout: time.Duration(conf.Timeout) * time.Millisecond,
			Transport: &http.Transport{
				Proxy: http.ProxyFromEnvironment,
				DialContext: (&net.Dialer{
					Timeout:   30 * time.Second,
					KeepAlive: 30 * time.Second,
				}).DialContext,
				ForceAttemptHTTP2:     true,
				MaxIdleConns:          conf.MaxIdleConns,
				MaxIdleConnsPerHost:   conf.MaxIdleConnsPerHost,
				MaxConnsPerHost:       conf.MaxConnsPerHost,
				IdleConnTimeout:       time.Duration(conf.IdleConnTimeout) * time.Minute,
				TLSHandshakeTimeout:   10 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
			},
		}
		fmt.Printf("http client %s init success !\n", HttpClient1)
		httpClientMap[HttpClient1] = client
	})
}
