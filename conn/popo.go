package conn

import (
	"fmt"
	"os"
	"sync"
	"time"

	"app/config"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/kit"
	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/z/popo"
)

var (
	p    popo.IPopo
	once sync.Once
)

func GetPopo() popo.IPopo {
	once.Do(func() {
		if config.C.Popo.Project == "" || config.C.Popo.Biz == "" || config.C.Popo.Env == "" {
			fmt.Fprintln(os.Stderr, "请先配置POPO，否则不予启动程序")
			os.Exit(1)
		}
		if nil == config.C.App.Maintainer || len(config.C.App.Maintainer) == 0 {
			fmt.Fprintln(os.Stderr, "请先配置APP维护者列表，否则不予启动程序")
			os.Exit(1)
		}
		p = popo.NewPopo(config.C.Popo.Url,
			config.C.Popo.Salt,
			config.C.Popo.Project,
			config.C.Popo.Biz,
			config.C.Popo.Env,
			time.Duration(config.C.Popo.Timeout)*time.Millisecond,
			elog.GetStd(),
			fmt.Sprintf("Hostname: %s\nMessage:", kit.Hostname()),
			config.C.App.Maintainer)
	})
	return p
}
