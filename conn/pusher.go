package conn

import (
	"sync"

	"app/config"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	pusher2 "ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/z/pusher"
)

var (
	pusher     pusher2.IPusher
	pusherOnce sync.Once
)

func GetPusher() pusher2.IPusher {
	pusherOnce.Do(func() {
		var err error
		c := config.C.Pusher
		pusher, err = pusher2.NewPusher(c.LogPath+"/"+c.<PERSON>, c.Activity, c.<PERSON>, c.Max<PERSON>ize, c.ReserveDays, c.<PERSON>ount)
		if err != nil {
			elog.WithField("conf", c).WithError(err).Error("初始化pusher错误")
		}
	})
	return pusher
}
