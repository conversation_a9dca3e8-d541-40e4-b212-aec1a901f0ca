package conn

import (
	"context"
	"errors"
	"fmt"
	"time"

	"app/config"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
)

type MysqlLogger struct {
	elog *elog.Logger
	// cfg
	LogLevel                  logger.LogLevel
	IgnoreRecordNotFoundError bool
	IgnoreRecordDuplicate     bool
	ParameterizedQueries      bool
	SlowThreshold             time.Duration
	Debug                     bool

	// format str
	infoStr, warnStr, errStr            string
	traceStr, traceErrStr, traceWarnStr string
}

func NewMysqlLogger() *MysqlLogger {
	slowThreshold := 200 * time.Millisecond
	if config.C.Mysql.SlowThreshold > 0 {
		slowThreshold = time.Duration(config.C.Mysql.SlowThreshold) * time.Millisecond
	}
	return &MysqlLogger{
		elog:                      elog.GetStd(),
		IgnoreRecordNotFoundError: true,
		IgnoreRecordDuplicate:     true,
		SlowThreshold:             slowThreshold,
		Debug:                     config.C.Mysql.Debug,

		infoStr:      "%s [info] ",
		warnStr:      "%s [warn] ",
		errStr:       "%s [error] ",
		traceStr:     "%s [%.3fms] [rows:%v] %s",
		traceWarnStr: "%s %s [%.3fms] [rows:%v] %s",
		traceErrStr:  "%s %s [%.3fms] [rows:%v] %s",
	}
}

func (l *MysqlLogger) LogMode(level logger.LogLevel) logger.Interface {
	newlogger := *l
	newlogger.LogLevel = level
	return &newlogger
}

func (l *MysqlLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		l.elog.Info(l.infoStr+msg, append([]interface{}{utils.FileWithLineNum()}, data...)...)
	}
}

func (l *MysqlLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		l.elog.Warn(l.warnStr+msg, append([]interface{}{utils.FileWithLineNum()}, data...)...)
	}
}

func (l *MysqlLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		l.elog.Error(l.errStr+msg, append([]interface{}{utils.FileWithLineNum()}, data...)...)
	}
}

func (l *MysqlLogger) ParamsFilter(ctx context.Context, sql string, params ...interface{}) (string, []interface{}) {
	if l.ParameterizedQueries {
		return sql, nil
	}
	return sql, params
}

func (l *MysqlLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}
	if l.Debug {
		sql, rows := fc()
		fmt.Printf("sql: %s, rows: %d err: %v\n", sql, rows, err)
		return
	}
	elapsed := time.Since(begin)
	switch {
	case err != nil && l.LogLevel >= logger.Error && (!errors.Is(err, logger.ErrRecordNotFound) || !l.IgnoreRecordNotFoundError):
		if err.Error() == "duplicated key not allowed" && l.IgnoreRecordDuplicate {
			return
		}
		sql, rows := fc()
		if rows == -1 {
			l.elog.Error(l.traceErrStr, utils.FileWithLineNum(), err, float64(elapsed.Nanoseconds())/1e6, "-", sql)
		} else {
			l.elog.Error(l.traceErrStr, utils.FileWithLineNum(), err, float64(elapsed.Nanoseconds())/1e6, rows, sql)
		}
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		sql, rows := fc()
		slowLog := fmt.Sprintf("SLOW SQL >= %v", l.SlowThreshold)
		if rows == -1 {
			l.elog.Warn(l.traceWarnStr, utils.FileWithLineNum(), slowLog, float64(elapsed.Nanoseconds())/1e6, "-", sql)
		} else {
			l.elog.Warn(l.traceWarnStr, utils.FileWithLineNum(), slowLog, float64(elapsed.Nanoseconds())/1e6, rows, sql)
		}
	case l.LogLevel == logger.Info:
		sql, rows := fc()
		if rows == -1 {
			l.elog.Info(l.traceStr, utils.FileWithLineNum(), float64(elapsed.Nanoseconds())/1e6, "-", sql)
		} else {
			l.elog.Info(l.traceStr, utils.FileWithLineNum(), float64(elapsed.Nanoseconds())/1e6, rows, sql)
		}
	}
}
