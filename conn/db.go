package conn

import (
	"fmt"
	"log"
	"sync"
	"time"

	"app/config"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var dbMap = make(map[string]*gorm.DB)

func GetDB(kl ...string) *gorm.DB {
	key := DB1
	if len(kl) > 0 {
		key = kl[0]
	}

	db, exists := dbMap[key]
	if !exists {
		fmt.Println(fmt.Errorf("database %s not connected", key))
		return nil
	}
	return db
}

const DB1 = "db1"

var db1Once = sync.Once{}

func ConnectDB1(conf *config.MySQLConfig) {
	db1Once.Do(func() {
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", conf.Username, conf.Password, conf.Host, conf.Port, conf.DBName)
		var err error
		newLogger := NewMysqlLogger()
		db, err := gorm.Open(mysql.Open(dsn+"?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{
			TranslateError: true,
			// Logger: logger.New(
			//	log.New(os.Stdout, "\r\n", log.LstdFlags),
			//	logger.Config{
			//		SlowThreshold:             time.Second,
			//		Colorful:                  false,
			//		IgnoreRecordNotFoundError: true,
			//		ParameterizedQueries:      true,
			//		LogLevel:                  logger.Info,
			//	}),
			Logger: newLogger.LogMode(logger.LogLevel(conf.LogLevel)),
		})
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB, err := db.DB()
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB.SetMaxIdleConns(conf.MaxIdle)
		sqlDB.SetMaxOpenConns(conf.MaxOpen)
		sqlDB.SetConnMaxLifetime(time.Duration(conf.ConnMaxLifetime) * time.Minute)
		fmt.Printf("database %s conn success ! %v\n", DB1, sqlDB.Ping())
		dbMap[DB1] = db
	})
}

const SLAVE = "slave"

var slaveOnce = sync.Once{}

func ConnectSlaveDB(conf *config.MySQLConfig) {
	slaveOnce.Do(func() {
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", conf.Username, conf.Password, conf.Host, conf.Port, conf.DBName)
		var err error
		newLogger := NewMysqlLogger()
		db, err := gorm.Open(mysql.Open(dsn+"?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{
			TranslateError: true,
			Logger:         newLogger.LogMode(logger.Info),
		})
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB, err := db.DB()
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB.SetMaxIdleConns(conf.MaxIdle)
		sqlDB.SetMaxOpenConns(conf.MaxOpen)
		sqlDB.SetConnMaxLifetime(time.Duration(conf.ConnMaxLifetime) * time.Minute)
		fmt.Printf("database %s conn success ! %v\n", SLAVE, sqlDB.Ping())
		dbMap[SLAVE] = db
	})
}

const ONLINE = "online"

var onlineOnce = sync.Once{}

func ConnectOnlineDB(conf *config.MySQLConfig) {
	onlineOnce.Do(func() {
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", conf.Username, conf.Password, conf.Host, conf.Port, conf.DBName)
		var err error
		newLogger := NewMysqlLogger()
		db, err := gorm.Open(mysql.Open(dsn+"?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{
			TranslateError: true,
			Logger:         newLogger.LogMode(logger.Info),
		})
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB, err := db.DB()
		if err != nil {
			log.Fatal(dsn, err)
		}
		sqlDB.SetMaxIdleConns(conf.MaxIdle)
		sqlDB.SetMaxOpenConns(conf.MaxOpen)
		sqlDB.SetConnMaxLifetime(time.Duration(conf.ConnMaxLifetime) * time.Minute)
		fmt.Printf("database %s conn success ! %v\n", ONLINE, sqlDB.Ping())
		dbMap[ONLINE] = db
	})
}
