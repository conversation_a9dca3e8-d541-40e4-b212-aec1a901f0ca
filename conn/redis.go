package conn

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"app/config"

	"ccc-gitlab.leihuo.netease.com/pkgo/golib/v2/elog"
	"github.com/redis/go-redis/v9"
)

var Cache *redis.Client

var cacheOnce = sync.Once{}

func GetRedisConn() *redis.Client {
	return Cache
}

func ConnectRedis1(conf *config.RedisConfig) *redis.Client {
	cacheOnce.Do(func() {
		Cache = redis.NewClient(&redis.Options{
			Addr:     conf.Addr,
			Password: conf.Password, // no password set
			PoolSize: conf.MaxActive,
			DB:       conf.DB, // use default DB
		})
	})
	_, err := Cache.Ping(context.Background()).Result()
	if nil != err {
		fmt.Printf("redis conn error ! %v\n", err)
	} else {
		fmt.Println("redis conn success !")
	}
	return Cache
}

func RedisMonitor(conf *config.RedisConfig) {
	ch := make(chan string)
	ctx := context.Background() // 创建一个不带cancel的context
	monitor := redis.NewClient(&redis.Options{
		Addr:        conf.Addr,
		Password:    conf.Password, // no password set
		PoolSize:    conf.MaxActive,
		DB:          conf.DB, // use default DB
		ReadTimeout: -1,      // no timeout 设置后可一直监听
	}).Monitor(ctx, ch)

	go monitor.Start()

	for {
		select {
		case <-ctx.Done():
			fmt.Println("monitor stop")
			return
		case val := <-ch:
			if strings.Contains(val, "ping") {
				continue
			}
			elog.WithField("redis command", val).Info("redis command")
			fmt.Println("redis command: ", val)
		}
	}
}
