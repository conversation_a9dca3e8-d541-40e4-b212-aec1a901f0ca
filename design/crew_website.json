{"openapi": "3.1.0", "info": {"title": "L36_crew_website", "version": "0.1.0"}, "paths": {"/api/v1/audio/normalize": {"post": {"tags": ["audio"], "summary": "Normalize Audio", "operationId": "normalize_audio_api_v1_audio_normalize_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudioNormalizeReq"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudioNormalizeResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/filepicker/clone": {"post": {"tags": ["filepicker"], "summary": "<PERSON><PERSON>", "operationId": "clone_api_v1_filepicker_clone_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilePickerCloneReq"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilePickerCloneResp"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/filepicker/delete": {"post": {"tags": ["filepicker"], "summary": "Delete", "operationId": "delete_api_v1_filepicker_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilePickerDeleteReq"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AudioNormalizeReq": {"properties": {"url": {"type": "string", "title": "Url", "description": "语音URL"}, "uid": {"type": "string", "title": "<PERSON><PERSON>", "description": "用户标识"}, "expires": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expires", "description": "克隆文件的过期时间，单位为秒，默认不过期"}}, "type": "object", "required": ["url", "uid"], "title": "AudioNormalizeReq"}, "AudioNormalizeResp": {"properties": {"audio_url": {"type": "string", "title": "Audio Url", "description": "语音URL"}}, "type": "object", "required": ["audio_url"], "title": "AudioNormalizeResp"}, "FilePickerCloneReq": {"properties": {"url": {"type": "string", "title": "Url", "description": "源文件URL"}, "uid": {"type": "string", "title": "<PERSON><PERSON>", "description": "源文件的用户标识"}, "expires": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expires", "description": "克隆文件的过期时间，单位为秒，默认不过期"}}, "type": "object", "required": ["url", "uid"], "title": "FilePickerCloneReq"}, "FilePickerCloneResp": {"properties": {"clone_url": {"type": "string", "title": "Clone Url", "description": "克隆文件URL"}}, "type": "object", "required": ["clone_url"], "title": "FilePickerCloneResp"}, "FilePickerDeleteReq": {"properties": {"url": {"type": "string", "title": "Url", "description": "废弃文件URL"}}, "type": "object", "required": ["url"], "title": "FilePickerDeleteReq"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}