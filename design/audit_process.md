# 作品审核流程说明文档

## 一、概述
作品审核主要针对用户上传的媒体文件(视频和图片)进行内容审核,确保内容的合规性。整个审核流程包含自动检查和人工审核两个部分。

## 二、审核状态定义
- Pass: 通过
- Reject: 拒绝  
- Reviewing: 审核中

## 三、详细流程

### 1. 作品创建时的审核
```mermaid
graph TD
    A[用户创建作品] --> B[检查媒体文件有效性checkMedia]
    B --> C[获取初始审核状态getAuditStatus]
    C --> D[保存作品到数据库]
    D --> E{是否审核通过?}
    E -->|是| F[完成创建]
    E -->|否| G[等待审核]
```

### 2. 定时检查审核状态
```mermaid
graph TD
    A[定时任务/5分钟] --> B[检查15-45分钟前的待审核作品]
    B --> C[获取最新审核状态getNewAuditStatus]
    C --> D{状态是否变化?}
    D -->|是| E[更新作品审核状态]
    D -->|否| F[继续等待]
```

### 3. FP服务器回调通知
```mermaid
graph TD
    A[FP服务器回调] --> B[更新媒体文件审核状态]
    B --> C[触发作品状态更新]
    C --> D{是否全部通过?}
    D -->|是| E[更新作品为通过]
    D -->|否| F[维持当前状态]
```

## 四、审核规则说明

1. **媒体文件审核**
   - 视频和图片都需要通过审核
   - 任一媒体文件被拒绝,则作品审核失败
   - 所有媒体文件通过,作品才算通过

2. **审核状态更新机制**
   - 创建时进行初始审核
   - 定时任务每5分钟检查一次
   - 接收FP服务器的实时回调通知

3. **审核超时处理**
   - 系统会检查15-45分钟前发布的待审核作品
   - 通过定时任务确保审核状态及时更新

## 五、注意事项

1. 作品创建时必须包含有效的媒体文件
2. 审核状态变更会实时反映到作品状态
3. 使用多重机制(定时任务+回调)确保审核状态准确性
4. 审核拒绝的作品不能被其他用户查看
5. 私密作品同样需要通过审核才能保存 